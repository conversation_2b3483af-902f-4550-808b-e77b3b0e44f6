import{s as p}from"./index.DqILEIKx.js";import{_ as d,p as o}from"./utils.BWEB-mtU.js";import{d as s,g as l,o as n}from"./index.CpC-7sc3.js";import{r as c}from"./reactivity.esm-bundler.Bx7uHohy.js";const i=s({__name:"InputText",setup(t,{expose:r}){r();const e={theme:c({root:`appearance-none rounded-md outline-hidden
        bg-surface-0 dark:bg-surface-950
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        text-surface-700 dark:text-surface-0
        placeholder:text-surface-500 dark:placeholder:text-surface-400
        border border-surface-300 dark:border-surface-700
        enabled:hover:border-surface-400 dark:enabled:hover:border-surface-600
        enabled:focus:border-primary
        disabled:bg-surface-200 disabled:text-surface-500
        dark:disabled:bg-surface-700 dark:disabled:text-surface-400
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-invalid:placeholder:text-red-600 dark:p-invalid:placeholder:text-red-400
        px-3 py-2 p-fluid:w-full
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-lg p-large:px-[0.875rem] p-large:py-[0.625rem]
        transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`}),get InputText(){return p},get ptViewMerge(){return o}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function u(t,r,a,e,f,m){return n(),l(e.InputText,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},null,8,["pt","ptOptions"])}const h=d(i,[["render",u]]);export{h as I};
