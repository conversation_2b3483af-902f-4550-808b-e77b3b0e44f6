import{u as k0}from"./useTrpc.CcBnDuWb.js";import{u as E0,C as D0}from"./ConfirmDialog.Cn649_Oc.js";import{u as F0}from"./useToast.Cyn6G0qw.js";import{M as p0,u as w0}from"./MatchingDetailsGrid.5afoSyQe.js";import{t as I0}from"./trpc.CMxyjkwB.js";import A0 from"./Card.DllwvXut.js";import V0 from"./Button.CplYapw1.js";import{I as B0}from"./InputText.CPqCR4in.js";import{D as T0,s as L0}from"./index.ClGz6GkZ.js";import{T as P0}from"./Tag.BtN2Bjhy.js";import{V as M0}from"./Message.DXe4eGzY.js";import{D as S0}from"./Dialog.DjvB895c.js";import R0 from"./PartWizard.xV09UB_V.js";import{V as N0}from"./AutoComplete.WqgqstcD.js";import{S as U0}from"./Select.B5f7pqRM.js";import{V as z0}from"./Textarea.C8hcWg9_.js";import{M as H0,a as O0,R as j0}from"./MatchingLoadingState.CWg9RiuK.js";import{S as G0}from"./SecondaryButton.ImrBLtmY.js";import{D as q0}from"./DangerButton.DyBZF5lv.js";import W0 from"./Toast.BFjVikSW.js";import{_ as Q0}from"./utils.BWEB-mtU.js";import{c as e0}from"./createLucideIcon.CxvjiKko.js";import{I as X0}from"./info.D2H3qZLp.js";import{L as K0}from"./list.Ce343wC8.js";import{a as Y0,T as J0,P as Z0}from"./trash.D7SMYTt1.js";import{L as $0}from"./link.DoOSpg3_.js";import{d as u4,c,a as e,e as o,g as h,b as v,w as l,n as e4,$ as t4,o as n,f,F as L,r as P}from"./index.CpC-7sc3.js";import{r as d,t as i}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./index.enLFHxHc.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.2frgj6Y9.js";import"./ToggleSwitch.DRF-wltF.js";import"./index.DqILEIKx.js";import"./Icon.DGPcirKX.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";/* empty css                            */import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.DBjPSdxz.js";import"./index.CwY1vywt.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.hiPlcmdl.js";import"./index.DUcQAuYR.js";import"./index.CyH7ziOX.js";import"./index.C2Xch34u.js";import"./Tab.DSDMNzbv.js";import"./index.B3SmZZpj.js";import"./QuickCreateBrand.ZBe-Tlwy.js";import"./AttributeValueInput.CrEMZkDz.js";import"./InputNumber.8Wyucp72.js";import"./Checkbox.Czip7_Ii.js";import"./utils.D8YPi1lO.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./index.BtgaEm74.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a4=e0("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s4=e0("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),o4=u4({__name:"PartsTable",setup(t0,{expose:t}){t();const{loading:q,error:u,clearError:M,parts:b,partCategories:p,matching:a,partApplicability:r}=k0(),W=E0(),w=F0(),{getAccuracyLabel:a0,getAccuracySeverity:s0}=w0(),Q=d([]),k=d([]),X=d(0),I=d(25),E=d(0),o0=d([]),S=d({}),R=d(""),D=d(null),A=d([]),N=d(!1),U=d(null),z=d(!1),C=d(null),H=d(!1),V=d(null),B=d(!1),y=d(null),O=d(!1),g=d({accuracy:"EXACT_MATCH",notes:""}),r0=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],j=d("createdAt"),G=d(-1),x=async()=>{try{M();const s={skip:E.value*I.value,take:I.value,orderBy:{[j.value]:G.value===1?"asc":"desc"},include:{image:!0,partCategory:!0,attributes:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},equipmentApplicabilities:{include:{equipmentModel:{include:{brand:!0}}}}}};if(R.value.trim()&&(s.where={...s.where,name:{contains:R.value.trim(),mode:"insensitive"}}),D.value){const _=typeof D.value=="object"?D.value.id:D.value;s.where={...s.where,partCategoryId:_}}const m=await b.findMany(s);m&&(Q.value=m);const F=await b.findMany({where:s.where,select:{id:!0}});F&&(X.value=F.length)}catch(s){console.error("Ошибка загрузки запчастей:",s)}},K=async()=>{try{const s=await p.findMany({orderBy:{name:"asc"}});s&&(k.value=s)}catch(s){console.error("Ошибка загрузки категорий:",s)}},Y=async s=>{try{const m=await I0.partAttributes.findByPartId.query({partId:s});m&&(S.value[s]=m)}catch(m){console.error("Ошибка загрузки атрибутов запчасти:",m)}},l0=s=>{E.value=s.page,I.value=s.rows,x()},i0=s=>{j.value=s.sortField,G.value=s.sortOrder,x()},n0=s=>{const m=s.data.id;S.value[m]||Y(m)},d0=()=>{x()},c0=()=>{E.value=0,x()};let T;const m0=()=>{clearTimeout(T),T=setTimeout(()=>{E.value=0,x()},500)},f0=s=>{U.value=s,N.value=!0},g0=s=>{W.confirmDelete("запчасть",async()=>{try{await b.delete({where:{id:s.id}}),x()}catch{w.error("Ошибка","Не удалось удалить запчасть",5e3)}})},v0=s=>{x(),U.value=null,N.value=!1},x0=s=>{C.value=s,z.value=!0,J()},J=async()=>{if(C.value){H.value=!0,V.value=null;try{const s=await a.findMatchingCatalogItems({partId:C.value.id});V.value=s?s.candidates||[]:[]}catch(s){console.error("Ошибка подбора для Part:",s),V.value=[]}finally{H.value=!1}}},C0=s=>{y.value=s,g.value.accuracy=s.accuracySuggestion,g.value.notes="";const m=(s.details||[]).find(_=>String(_.kind).includes("NEAR")||String(_.kind).includes("LEGACY"));m?.notes&&(g.value.notes=m.notes),(s.details||[]).find(_=>_.kind==="NUMBER_WITHIN_TOLERANCE")&&!g.value.notes&&(g.value.notes="Совпадение по допуску"),B.value=!0},Z=()=>{B.value=!1,y.value=null,g.value.accuracy="EXACT_MATCH",g.value.notes=""},y0=async()=>{if(!(!C.value||!y.value)){O.value=!0;try{await r.upsert({where:{partId_catalogItemId:{partId:C.value.id,catalogItemId:y.value.catalogItem.id}},create:{partId:C.value.id,catalogItemId:y.value.catalogItem.id,accuracy:g.value.accuracy,notes:g.value.notes||void 0},update:{accuracy:g.value.accuracy,notes:g.value.notes||void 0}}),z.value=!1,B.value=!1,w.success("Успешно","Позиция привязана к группе"),x(),Z()}catch(s){console.error("Ошибка привязки:",s),w.error("Ошибка","Не удалось привязать позицию")}finally{O.value=!1}}},_0=s=>new Date(s).toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric"}),h0=s=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[s]||s,b0=s=>{const m=s.query.toLowerCase();m?A.value=k.value.filter(F=>F.name.toLowerCase().includes(m)):A.value=k.value},$=()=>{A.value=k.value};e4(()=>{K(),x(),$()});const u0={loading:q,error:u,clearError:M,partsApi:b,partCategories:p,matching:a,partApplicability:r,confirm:W,toast:w,getAccuracyLabel:a0,getAccuracySeverity:s0,parts:Q,categories:k,totalCount:X,pageSize:I,currentPage:E,expandedRows:o0,partAttributes:S,searchQuery:R,selectedCategory:D,categorySuggestions:A,editDialogVisible:N,selectedPartForEdit:U,matchingDialogVisible:z,selectedPartForMatching:C,matchingLoading:H,matchingResults:V,showLinkConfirmDialog:B,selectedLinkCandidate:y,linking:O,linkConfirmForm:g,accuracyOptions:r0,sortField:j,sortOrder:G,loadParts:x,loadCategories:K,loadPartAttributes:Y,onPageChange:l0,onSort:i0,onRowExpand:n0,refreshData:d0,applyFilters:c0,get searchTimeout(){return T},set searchTimeout(s){T=s},debouncedSearch:m0,editPart:f0,deletePart:g0,onPartSaved:v0,openMatching:x0,runPartMatching:J,openLinkConfirmDialog:C0,closeLinkConfirmDialog:Z,confirmLinkItem:y0,formatDate:_0,getUnitLabel:h0,filterCategories:b0,initializeCategorySuggestions:$,VCard:A0,VButton:V0,VInputText:B0,VDataTable:T0,get VColumn(){return L0},VTag:P0,VMessage:M0,VDialog:S0,PartWizard:R0,VConfirmDialog:D0,VAutoComplete:N0,VSelect:U0,VTextarea:z0,MatchingDetailsGrid:p0,MatchingEmptyState:O0,MatchingLoadingState:H0,get RefreshCcwIcon(){return j0},get PlusIcon(){return Z0},SecondaryButton:G0,get LinkIcon(){return $0},get TrashIcon(){return J0},DangerButton:q0,get PencilIcon(){return Y0},Toast:W0,get EyeIcon(){return a4},get ListIcon(){return K0},get InfoIcon(){return X0},get PackageIcon(){return s4}};return Object.defineProperty(u0,"__isScriptSetup",{enumerable:!1,value:!0}),u0}}),r4={class:"space-y-6"},l4={class:"flex items-center justify-between"},i4={class:"flex gap-3"},n4={href:"/admin/parts/create"},d4={class:"p-6"},c4={class:"grid grid-cols-1 gap-4 md:grid-cols-3"},m4={class:"flex-1"},f4={class:"flex items-end"},g4={class:"text-surface-600 dark:text-surface-400 text-sm"},v4={class:"text-surface-900 dark:text-surface-100 font-medium"},x4={class:"text-surface-900 dark:text-surface-100 font-medium"},C4={class:"text-surface-700 dark:text-surface-300 font-mono text-sm"},y4={class:"text-surface-900 dark:text-surface-100 font-medium"},_4=["href"],h4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},b4={key:1,class:"text-surface-500 dark:text-surface-400 text-sm"},k4={class:"flex items-center gap-2"},E4={class:"text-surface-600 dark:text-surface-400 text-sm"},D4={class:"flex gap-2"},F4=["href"],p4=["href"],w4={class:"bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4"},I4={class:"grid grid-cols-1 gap-6 lg:grid-cols-2"},A4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},V4={key:0,class:"space-y-2"},B4={class:"flex-1"},T4={class:"text-surface-900 dark:text-surface-100 text-sm font-medium"},L4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},P4={class:"ml-3 text-right"},M4={class:"text-surface-700 dark:text-surface-300 font-medium"},S4={key:0,class:"text-surface-500 dark:text-surface-400 text-sm"},R4={class:"text-surface-500 dark:text-surface-400 py-6 text-center"},N4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},U4={key:0,class:"space-y-2"},z4={class:"flex items-start justify-between"},H4={class:"flex-1"},O4={class:"text-surface-900 dark:text-surface-100 font-medium"},j4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},G4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},q4={class:"ml-3"},W4={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},Q4={class:"text-surface-500 dark:text-surface-400 py-6 text-center"},X4={class:"lg:col-span-2"},K4={key:0,class:"grid grid-cols-1 gap-3 md:grid-cols-2"},Y4={class:"flex items-start justify-between"},J4={class:"flex-1"},Z4={class:"text-surface-900 dark:text-surface-100 font-medium"},$4={key:0,class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},uu={class:"ml-3"},eu={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},tu={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},au={class:"p-4"},su={class:"mb-4 flex items-center justify-between"},ou={class:"font-semibold"},ru={key:2,class:"space-y-3"},lu={class:"grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3"},iu={class:"md:col-span-1"},nu={class:"font-mono font-semibold"},du={class:"text-surface-500"},cu={class:"mt-2"},mu={class:"mt-3"},fu={class:"md:col-span-2"},gu={key:0,class:"space-y-4"},vu={class:"bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2"},xu={class:"font-semibold"},Cu={class:"font-semibold"},yu={class:"text-sm"},_u={class:"space-y-3"},hu={class:"flex justify-between"};function bu(t0,t,q,u,M,b){const p=t4("Icon");return n(),c("div",r4,[e("div",l4,[t[10]||(t[10]=e("div",null,[e("h2",{class:"text-surface-900 dark:text-surface-0 text-xl font-semibold"},"Запчасти"),e("p",{class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},"Управление группами взаимозаменяемости")],-1)),e("div",i4,[o(u.SecondaryButton,{onClick:u.refreshData,disabled:u.loading,outlined:""},{default:l(()=>[t[8]||(t[8]=f(" Обновить ")),o(u.RefreshCcwIcon,{class:"h-4 w-4"})]),_:1,__:[8]},8,["disabled"]),e("a",n4,[o(u.VButton,{outlined:""},{default:l(()=>[t[9]||(t[9]=f(" Создать ")),o(u.PlusIcon)]),_:1,__:[9]})])])]),o(u.VCard,null,{content:l(()=>[e("div",d4,[e("div",c4,[e("div",null,[t[11]||(t[11]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Поиск по названию ",-1)),o(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=a=>u.searchQuery=a),placeholder:"Введите название запчасти...",class:"w-full",onInput:u.debouncedSearch},null,8,["modelValue"])]),e("div",m4,[t[12]||(t[12]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Фильтр по категории ",-1)),o(u.VAutoComplete,{modelValue:u.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=a=>u.selectedCategory=a),suggestions:u.categorySuggestions,onComplete:u.filterCategories,"option-label":"name","option-value":"id",placeholder:"Поиск категории...",class:"w-full",dropdown:"","show-clear":"",onChange:u.applyFilters},null,8,["modelValue","suggestions"])]),e("div",f4,[e("div",g4,[e("div",null,[t[13]||(t[13]=f(" Всего запчастей: ")),e("span",v4,i(u.totalCount),1)]),e("div",null,[t[14]||(t[14]=f(" Показано: ")),e("span",x4,i(u.parts.length),1)])])])])])]),_:1}),o(u.VCard,null,{content:l(()=>[o(u.VDataTable,{value:u.parts,loading:u.loading,paginator:"",rows:u.pageSize,"total-records":u.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:u.onPageChange,onSort:u.onSort,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":"",expandedRows:u.expandedRows,"onUpdate:expandedRows":t[2]||(t[2]=a=>u.expandedRows=a),onRowExpand:u.onRowExpand},{expansion:l(({data:a})=>[e("div",w4,[e("div",I4,[e("div",null,[e("h4",A4,[o(u.ListIcon,{class:"h-4 w-4 text-blue-600"}),t[16]||(t[16]=f(" Атрибуты запчасти "))]),u.partAttributes[a.id]?.length>0?(n(),c("div",V4,[(n(!0),c(L,null,P(u.partAttributes[a.id],r=>(n(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"},[e("div",B4,[e("div",T4,i(r.template?.title||"Без названия"),1),r.description?(n(),c("div",L4,i(r.description),1)):v("",!0)]),e("div",P4,[e("div",M4,i(r.value||"Не указано"),1),r.unit?(n(),c("div",S4,i(u.getUnitLabel(r.unit)),1)):v("",!0)])]))),128))])):v("",!0),e("div",R4,[o(u.InfoIcon,{class:"mb-2 inline-block text-2xl"}),t[17]||(t[17]=f(" Атрибуты не заданы "))])]),e("div",null,[e("h4",N4,[o(u.PackageIcon,{class:"h-4 w-4 text-green-600"}),t[18]||(t[18]=f(" Каталожные позиции "))]),a.applicabilities?.length>0?(n(),c("div",U4,[(n(!0),c(L,null,P(a.applicabilities,r=>(n(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",z4,[e("div",H4,[e("div",O4,i(r.catalogItem?.sku||"N/A"),1),e("div",j4,i(r.catalogItem?.brand?.name||"Неизвестный бренд"),1),r.catalogItem?.description?(n(),c("div",G4,i(r.catalogItem.description),1)):v("",!0)]),e("div",q4,[o(u.VTag,{severity:u.getAccuracySeverity(r.accuracy),class:"text-sm"},{default:l(()=>[f(i(u.getAccuracyLabel(r.accuracy)),1)]),_:2},1032,["severity"])])]),r.notes?(n(),c("div",W4,[o(u.InfoIcon,{class:"mr-1 inline-block h-4 w-4"}),f(" "+i(r.notes),1)])):v("",!0)]))),128))])):v("",!0),e("div",Q4,[o(u.InfoIcon,{class:"mb-2 inline-block text-2xl"}),t[19]||(t[19]=f(" Каталожные позиции не добавлены "))])]),e("div",X4,[t[22]||(t[22]=e("h4",{class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},"Применимость к технике",-1)),a.equipmentApplicabilities?.length>0?(n(),c("div",K4,[(n(!0),c(L,null,P(a.equipmentApplicabilities,r=>(n(),c("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",Y4,[e("div",J4,[e("div",Z4,i(r.equipmentModel?.name||"N/A"),1),r.equipmentModel?.brand?(n(),c("div",$4,i(r.equipmentModel.brand.name),1)):v("",!0)]),e("div",uu,[o(u.VTag,{severity:"info",class:"text-sm"},{default:l(()=>t[20]||(t[20]=[f(" Техника ")])),_:1,__:[20]})])]),r.notes?(n(),c("div",eu,[o(u.InfoIcon,{class:"mr-1 inline-block h-4 w-4"}),f(" "+i(r.notes),1)])):v("",!0)]))),128))])):(n(),c("div",tu,[o(p,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[21]||(t[21]=f(" Применимость к технике не указана "))]))])])])]),default:l(()=>[o(u.VColumn,{expander:"",style:{width:"50px"}}),o(u.VColumn,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:l(({data:a})=>[e("span",C4,"#"+i(a.id),1)]),_:1}),o(u.VColumn,{field:"name",header:"Название",sortable:"",style:{width:"30%"}},{body:l(({data:a})=>[e("div",null,[e("div",y4,[e("a",{href:`/admin/parts/${a.id}`,class:"hover:underline text-primary"},i(a.name||"Без названия"),9,_4)]),e("div",h4,"Уровень: "+i(a.level)+" | Путь: "+i(a.path),1)])]),_:1}),o(u.VColumn,{field:"partCategory.name",header:"Категория",style:{width:"20%"}},{body:l(({data:a})=>[a.partCategory?(n(),h(u.VTag,{key:0,severity:"info",class:"text-sm"},{default:l(()=>[f(i(a.partCategory.name),1)]),_:2},1024)):(n(),c("span",b4,"Не указана"))]),_:1}),o(u.VColumn,{header:"Детали",style:{width:"20%"}},{body:l(({data:a})=>[e("div",k4,[o(u.VTag,{severity:"secondary",class:"text-sm"},{default:l(()=>[f(i(u.partAttributes[a.id]?.length||a.attributes?.length||0)+" атр. ",1)]),_:2},1024),o(u.VTag,{severity:"success",class:"text-sm"},{default:l(()=>[f(i(a.applicabilities?.length||0)+" поз. ",1)]),_:2},1024)])]),_:1}),o(u.VColumn,{field:"createdAt",header:"Создано",sortable:"",style:{width:"120px"}},{body:l(({data:a})=>[e("span",E4,i(u.formatDate(a.createdAt)),1)]),_:1}),o(u.VColumn,{header:"Действия",style:{width:"140px"}},{body:l(({data:a})=>[e("div",D4,[e("a",{href:`/admin/parts/${a.id}`},[o(u.VButton,{size:"small",outlined:""},{default:l(()=>[o(u.EyeIcon,{class:"h-4 w-4"})]),_:1})],8,F4),e("a",{href:`/admin/parts/${a.id}/edit`},[o(u.VButton,{outlined:""},{default:l(()=>[o(u.PencilIcon,{class:"h-4 w-4"})]),_:1})],8,p4),o(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:r=>u.openMatching(a)},{default:l(()=>[t[15]||(t[15]=f(" Подобрать ")),o(u.LinkIcon,{class:"h-4 w-4"})]),_:2,__:[15]},1032,["onClick"]),o(u.DangerButton,{size:"small",outlined:"",onClick:r=>u.deletePart(a)},{default:l(()=>[o(u.TrashIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","expandedRows"])]),_:1}),u.error?(n(),h(u.VMessage,{key:0,severity:"error",class:"mt-4"},{default:l(()=>[f(i(u.error),1)]),_:1})):v("",!0),o(u.VDialog,{visible:u.editDialogVisible,"onUpdate:visible":t[3]||(t[3]=a=>u.editDialogVisible=a),modal:"",header:"Редактировать запчасть",class:""},{default:l(()=>[u.selectedPartForEdit?(n(),h(u.PartWizard,{key:0,part:u.selectedPartForEdit,mode:"edit",onUpdated:u.onPartSaved},null,8,["part"])):v("",!0)]),_:1},8,["visible"]),o(u.VDialog,{visible:u.matchingDialogVisible,"onUpdate:visible":t[4]||(t[4]=a=>u.matchingDialogVisible=a),modal:"",header:"Подбор каталожных позиций",class:"w-full max-w-4xl"},{default:l(()=>[e("div",au,[e("div",su,[t[23]||(t[23]=e("div",{class:"text-surface-500 text-sm"},"Группа",-1)),e("div",ou,i(u.selectedPartForMatching?.name||"#"+u.selectedPartForMatching?.id),1),o(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:u.matchingLoading,onClick:u.runPartMatching},{default:l(()=>[o(u.RefreshCcwIcon)]),_:1},8,["loading"])]),u.matchingLoading?(n(),h(u.MatchingLoadingState,{key:0,paddingClass:"py-8"})):!u.matchingResults||u.matchingResults.length===0?(n(),h(u.MatchingEmptyState,{key:1})):(n(),c("div",ru,[(n(!0),c(L,null,P(u.matchingResults,a=>(n(),h(u.VCard,{key:a.catalogItem.id,class:"border"},{content:l(()=>[e("div",lu,[e("div",iu,[e("div",nu,i(a.catalogItem.sku),1),e("div",du,i(a.catalogItem.brand?.name),1),e("div",cu,[o(u.VTag,{value:u.getAccuracyLabel(a.accuracySuggestion),severity:u.getAccuracySeverity(a.accuracySuggestion)},null,8,["value","severity"])]),e("div",mu,[o(u.VButton,{size:"small",label:"Привязать",onClick:r=>u.openLinkConfirmDialog(a)},{icon:l(()=>[o(u.LinkIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick"])])]),e("div",fu,[t[24]||(t[24]=e("div",{class:"text-surface-500 mb-2 text-sm"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:a.details},null,8,["details"])])])]),_:2},1024))),128))]))])]),_:1},8,["visible"]),o(u.VDialog,{visible:u.showLinkConfirmDialog,"onUpdate:visible":t[7]||(t[7]=a=>u.showLinkConfirmDialog=a),modal:"",header:"Подтверждение привязки",class:"w-full max-w-3xl"},{footer:l(()=>[e("div",hu,[o(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeLinkConfirmDialog}),o(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLinkItem,loading:u.linking},null,8,["loading"])])]),default:l(()=>[u.selectedLinkCandidate?(n(),c("div",gu,[e("div",vu,[e("div",null,[t[25]||(t[25]=e("div",{class:"text-surface-500 text-sm"},"Группа взаимозаменяемости",-1)),e("div",xu,i(u.selectedPartForMatching?.name||`Группа #${u.selectedPartForMatching?.id}`),1)]),e("div",null,[t[26]||(t[26]=e("div",{class:"text-surface-500 text-sm"},"Каталожная позиция",-1)),e("div",Cu,i(u.selectedLinkCandidate.catalogItem.sku),1),e("div",yu,i(u.selectedLinkCandidate.catalogItem.brand?.name),1)])]),e("div",null,[t[27]||(t[27]=e("h3",{class:"mb-3 text-lg font-semibold"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:u.selectedLinkCandidate.details},null,8,["details"])]),e("div",_u,[e("div",null,[t[28]||(t[28]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Точность совпадения ",-1)),o(u.VSelect,{modelValue:u.linkConfirmForm.accuracy,"onUpdate:modelValue":t[5]||(t[5]=a=>u.linkConfirmForm.accuracy=a),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Примечания ",-1)),o(u.VTextarea,{modelValue:u.linkConfirmForm.notes,"onUpdate:modelValue":t[6]||(t[6]=a=>u.linkConfirmForm.notes=a),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),t[30]||(t[30]=e("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):v("",!0)]),_:1},8,["visible"]),o(u.VConfirmDialog),o(u.Toast)])}const pe=Q0(o4,[["render",bu]]);export{pe as default};
