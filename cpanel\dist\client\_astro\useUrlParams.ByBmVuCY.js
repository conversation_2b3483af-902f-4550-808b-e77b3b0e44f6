import"./router.DKcY2uv6.js";import{a as c,o as h,n as i,b as u,s as d}from"./schemas.BR5-L2eu.js";import{r as K}from"./reactivity.esm-bundler.Bx7uHohy.js";import{h as p,l as M,n as _}from"./index.CpC-7sc3.js";const q=c(["createdAt","updatedAt","id","name","parentId","level","path","imageId","partCategoryId"]),H=c(["id","sku","source","description","brandId","isPublic","imageId"]),E=h({templateId:i(),value:d().optional(),values:u(d()).optional(),minValue:i().optional(),maxValue:i().optional(),matchType:c(["exact","contains","range","in"]).default("exact")});h({name:d().optional(),attributeFilters:u(E).optional(),categoryId:i().optional(),categoryIds:u(i()).optional(),brandIds:u(i()).optional(),limit:i().min(1).max(100).default(20),offset:i().min(0).default(0),orderBy:q.default("name").refine(o=>["name","createdAt","updatedAt"].includes(o),"Недопустимое поле сортировки"),orderDir:c(["asc","desc"]).default("asc")});h({search:d().optional(),sku:d().optional(),brandId:i().optional(),attributeFilters:u(E).optional(),limit:i().min(1).max(100).default(20),offset:i().min(0).default(0),orderBy:H.default("sku").refine(o=>["id","sku","source","description","brandId","isPublic","imageId"].includes(o),"Недопустимое поле сортировки"),orderDir:c(["asc","desc"]).default("asc")});h({templateId:i(),entityType:c(["part","catalogItem","equipment"])});const Q=(o,T={})=>{const{prefix:y="",arrayParams:f=[],numberParams:A=[],booleanParams:b=[],debounceMs:x=300,replaceHistory:V=!0}=T,s=K({...o});let g=null;const N=(t,r)=>{if(!(r==null||r===""))return f.includes(t)?Array.isArray(r)?r.map(e=>String(e)).filter(e=>e!==""):[String(r)].filter(e=>e!==""):String(r)},I=(t,r)=>{if(!r||Array.isArray(r)&&r.length===0)return f.includes(t)?[]:null;if(f.includes(t)){const e=Array.isArray(r)?r:[r];return A.includes(t)?e.map(a=>{const n=Number(a);return isNaN(n)?null:n}).filter(a=>a!==null):b.includes(t)?e.map(a=>a==="true"||a==="1"):e}if(A.includes(t)){const e=Number(Array.isArray(r)?r[0]:r);return isNaN(e)?null:e}if(b.includes(t)){const e=Array.isArray(r)?r[0]:r;return e==="true"||e==="1"}return Array.isArray(r)?r[0]:r},F=()=>typeof window>"u"?new URLSearchParams:new URLSearchParams(window.location.search),w=()=>{const t=F(),r={...o};Object.keys(r).forEach(e=>{const a=y+e,n=t.get(a),l=t.getAll(a);(n!==null||l.length>0)&&(f.includes(e)&&l.length>0?r[e]=I(e,l):n!==null&&(r[e]=I(e,n)))}),s.value=r},S=()=>{typeof window>"u"||(g&&clearTimeout(g),g=setTimeout(()=>{const t=F(),r=[];t.forEach((n,l)=>{l.startsWith(y)&&r.push(l)}),r.forEach(n=>t.delete(n)),Object.entries(s.value).forEach(([n,l])=>{const P=y+n,m=N(n,l);m!==void 0&&(Array.isArray(m)?m.forEach(B=>t.append(P,B)):t.set(P,m))});const e=t.toString(),a=typeof window<"u"&&window.location.search?window.location.search.replace(/^\?/,""):"";if(e!==a){const n=new URL(window.location.href);n.search=e,V?window.history.replaceState(null,"",n):window.history.pushState(null,"",n)}},x))},j=(t,r)=>{s.value[t]=r},O=t=>{Object.assign(s.value,t)},U=()=>{s.value={...o}},L=t=>{s.value[t]=o[t]},C=p(()=>Object.entries(s.value).some(([t,r])=>{const e=o[t];return Array.isArray(r)&&Array.isArray(e)?r.length!==e.length||!r.every((a,n)=>a===e[n]):r!==e})),D=p(()=>Object.entries(s.value).reduce((t,[r,e])=>{const a=o[r];return Array.isArray(e)?t+(e.length>0?1:0):e!==a&&e!==null&&e!==void 0&&e!==""?t+1:t},0)),R=p(()=>JSON.stringify(s.value,null,2));return M(s,S,{deep:!0}),_(()=>{if(w(),typeof window<"u"){const t=()=>{w()};return window.addEventListener("popstate",t),()=>{window.removeEventListener("popstate",t)}}}),{filters:p(()=>s.value),hasActiveFilters:C,activeFiltersCount:D,filtersString:R,updateFilter:j,updateFilters:O,resetFilters:U,resetFilter:L,loadFromUrl:w,saveToUrl:S}};export{Q as u};
