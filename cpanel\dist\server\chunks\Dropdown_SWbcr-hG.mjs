import { defineComponent, useSSRContext, computed, mergeProps } from 'vue';
import { S as Select } from './Select_CSb2vMfa.mjs';
import { ssrRenderComponent } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_Cit0rBg5.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Dropdown",
  props: {
    modelValue: {},
    options: {},
    optionLabel: { default: "label" },
    optionValue: { default: "value" },
    placeholder: { default: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435..." },
    disabled: { type: Boolean, default: false },
    showClear: { type: Boolean, default: false },
    class: { default: "" }
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const value = computed({
      get: () => props.modelValue,
      set: (val) => emit("update:modelValue", val)
    });
    const __returned__ = { props, emit, value, Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Select"], mergeProps({
    modelValue: $setup.value,
    "onUpdate:modelValue": ($event) => $setup.value = $event,
    options: $props.options,
    "option-label": $props.optionLabel,
    "option-value": $props.optionValue,
    placeholder: $props.placeholder,
    disabled: $props.disabled,
    "show-clear": $props.showClear,
    class: $props.class
  }, _ctx.$attrs, _attrs), null, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Dropdown.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Dropdown = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { Dropdown as D };
