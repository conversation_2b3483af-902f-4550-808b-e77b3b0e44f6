import{s as k,_ as B,p as $}from"./utils.BWEB-mtU.js";import{B as _,c as v,o as l,j as u,m as i,d as L,g as b,w as h,aA as g,az as D,af as y,aB as W,aC as p,ai as M,aI as S,p as z,q as w,b as N,a as m,a0 as T,u as U,z as H,ax as f}from"./index.CpC-7sc3.js";import{r as P,n as j}from"./reactivity.esm-bundler.Bx7uHohy.js";import{s as q}from"./index.B3SmZZpj.js";import{s as Q}from"./index.DUcQAuYR.js";import{R as A,f as O}from"./index.CMLtULFQ.js";var G=`
    .p-tabs {
        display: flex;
        flex-direction: column;
    }

    .p-tablist {
        display: flex;
        position: relative;
    }

    .p-tabs-scrollable > .p-tablist {
        overflow: hidden;
    }

    .p-tablist-viewport {
        overflow-x: auto;
        overflow-y: hidden;
        scroll-behavior: smooth;
        scrollbar-width: none;
        overscroll-behavior: contain auto;
    }

    .p-tablist-viewport::-webkit-scrollbar {
        display: none;
    }

    .p-tablist-tab-list {
        position: relative;
        display: flex;
        background: dt('tabs.tablist.background');
        border-style: solid;
        border-color: dt('tabs.tablist.border.color');
        border-width: dt('tabs.tablist.border.width');
    }

    .p-tablist-content {
        flex-grow: 1;
    }

    .p-tablist-nav-button {
        all: unset;
        position: absolute !important;
        flex-shrink: 0;
        inset-block-start: 0;
        z-index: 2;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: dt('tabs.nav.button.background');
        color: dt('tabs.nav.button.color');
        width: dt('tabs.nav.button.width');
        transition:
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        box-shadow: dt('tabs.nav.button.shadow');
        outline-color: transparent;
        cursor: pointer;
    }

    .p-tablist-nav-button:focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.nav.button.focus.ring.shadow');
        outline: dt('tabs.nav.button.focus.ring.width') dt('tabs.nav.button.focus.ring.style') dt('tabs.nav.button.focus.ring.color');
        outline-offset: dt('tabs.nav.button.focus.ring.offset');
    }

    .p-tablist-nav-button:hover {
        color: dt('tabs.nav.button.hover.color');
    }

    .p-tablist-prev-button {
        inset-inline-start: 0;
    }

    .p-tablist-next-button {
        inset-inline-end: 0;
    }

    .p-tablist-prev-button:dir(rtl),
    .p-tablist-next-button:dir(rtl) {
        transform: rotate(180deg);
    }

    .p-tab {
        flex-shrink: 0;
        cursor: pointer;
        user-select: none;
        position: relative;
        border-style: solid;
        white-space: nowrap;
        gap: dt('tabs.tab.gap');
        background: dt('tabs.tab.background');
        border-width: dt('tabs.tab.border.width');
        border-color: dt('tabs.tab.border.color');
        color: dt('tabs.tab.color');
        padding: dt('tabs.tab.padding');
        font-weight: dt('tabs.tab.font.weight');
        transition:
            background dt('tabs.transition.duration'),
            border-color dt('tabs.transition.duration'),
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        margin: dt('tabs.tab.margin');
        outline-color: transparent;
    }

    .p-tab:not(.p-disabled):focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.tab.focus.ring.shadow');
        outline: dt('tabs.tab.focus.ring.width') dt('tabs.tab.focus.ring.style') dt('tabs.tab.focus.ring.color');
        outline-offset: dt('tabs.tab.focus.ring.offset');
    }

    .p-tab:not(.p-tab-active):not(.p-disabled):hover {
        background: dt('tabs.tab.hover.background');
        border-color: dt('tabs.tab.hover.border.color');
        color: dt('tabs.tab.hover.color');
    }

    .p-tab-active {
        background: dt('tabs.tab.active.background');
        border-color: dt('tabs.tab.active.border.color');
        color: dt('tabs.tab.active.color');
    }

    .p-tabpanels {
        background: dt('tabs.tabpanel.background');
        color: dt('tabs.tabpanel.color');
        padding: dt('tabs.tabpanel.padding');
        outline: 0 none;
    }

    .p-tabpanel:focus-visible {
        box-shadow: dt('tabs.tabpanel.focus.ring.shadow');
        outline: dt('tabs.tabpanel.focus.ring.width') dt('tabs.tabpanel.focus.ring.style') dt('tabs.tabpanel.focus.ring.color');
        outline-offset: dt('tabs.tabpanel.focus.ring.offset');
    }

    .p-tablist-active-bar {
        z-index: 1;
        display: block;
        position: absolute;
        inset-block-end: dt('tabs.active.bar.bottom');
        height: dt('tabs.active.bar.height');
        background: dt('tabs.active.bar.background');
        transition: 250ms cubic-bezier(0.35, 0, 0.25, 1);
    }
`,J={root:function(t){var n=t.props;return["p-tabs p-component",{"p-tabs-scrollable":n.scrollable}]}},X=_.extend({name:"tabs",style:G,classes:J}),Y={name:"BaseTabs",extends:k,props:{value:{type:[String,Number],default:void 0},lazy:{type:Boolean,default:!1},scrollable:{type:Boolean,default:!1},showNavigators:{type:Boolean,default:!0},tabindex:{type:Number,default:0},selectOnFocus:{type:Boolean,default:!1}},style:X,provide:function(){return{$pcTabs:this,$parentInstance:this}}},K={name:"Tabs",extends:Y,inheritAttrs:!1,emits:["update:value"],data:function(){return{d_value:this.value}},watch:{value:function(t){this.d_value=t}},methods:{updateValue:function(t){this.d_value!==t&&(this.d_value=t,this.$emit("update:value",t))},isVertical:function(){return this.orientation==="vertical"}}};function Z(e,t,n,a,s,r){return l(),v("div",i({class:e.cx("root")},e.ptmi("root")),[u(e.$slots,"default")],16)}K.render=Z;const tt=L({__name:"Tabs",emits:["update:value"],setup(e,{expose:t,emit:n}){t();const a=e,s=n,r=P({root:"flex flex-col"});function o(d){s("update:value",d)}const c={props:a,emit:s,theme:r,onUpdateValue:o,get Tabs(){return K},get ptViewMerge(){return $}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}});function et(e,t,n,a,s,r){return l(),b(a.Tabs,{value:a.props.value,unstyled:"",pt:a.theme,ptOptions:{mergeProps:a.ptViewMerge},"onUpdate:value":a.onUpdateValue},{default:h(()=>[u(e.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const $t=B(tt,[["render",et]]);var at={root:"p-tablist",content:function(t){var n=t.instance;return["p-tablist-content",{"p-tablist-viewport":n.$pcTabs.scrollable}]},tabList:"p-tablist-tab-list",activeBar:"p-tablist-active-bar",prevButton:"p-tablist-prev-button p-tablist-nav-button",nextButton:"p-tablist-next-button p-tablist-nav-button"},nt=_.extend({name:"tablist",classes:at}),rt={name:"BaseTabList",extends:k,props:{},style:nt,provide:function(){return{$pcTabList:this,$parentInstance:this}}},I={name:"TabList",extends:rt,inheritAttrs:!1,inject:["$pcTabs"],data:function(){return{isPrevButtonEnabled:!1,isNextButtonEnabled:!0}},resizeObserver:void 0,watch:{showNavigators:function(t){t?this.bindResizeObserver():this.unbindResizeObserver()},activeValue:{flush:"post",handler:function(){this.updateInkBar()}}},mounted:function(){var t=this;setTimeout(function(){t.updateInkBar()},150),this.showNavigators&&(this.updateButtonState(),this.bindResizeObserver())},updated:function(){this.showNavigators&&this.updateButtonState()},beforeUnmount:function(){this.unbindResizeObserver()},methods:{onScroll:function(t){this.showNavigators&&this.updateButtonState(),t.preventDefault()},onPrevButtonClick:function(){var t=this.$refs.content,n=this.getVisibleButtonWidths(),a=g(t)-n,s=Math.abs(t.scrollLeft),r=a*.8,o=s-r,c=Math.max(o,0);t.scrollLeft=S(t)?-1*c:c},onNextButtonClick:function(){var t=this.$refs.content,n=this.getVisibleButtonWidths(),a=g(t)-n,s=Math.abs(t.scrollLeft),r=a*.8,o=s+r,c=t.scrollWidth-a,d=Math.min(o,c);t.scrollLeft=S(t)?-1*d:d},bindResizeObserver:function(){var t=this;this.resizeObserver=new ResizeObserver(function(){return t.updateButtonState()}),this.resizeObserver.observe(this.$refs.list)},unbindResizeObserver:function(){var t;(t=this.resizeObserver)===null||t===void 0||t.unobserve(this.$refs.list),this.resizeObserver=void 0},updateInkBar:function(){var t=this.$refs,n=t.content,a=t.inkbar,s=t.tabs;if(a){var r=y(n,'[data-pc-name="tab"][data-p-active="true"]');this.$pcTabs.isVertical()?(a.style.height=W(r)+"px",a.style.top=p(r).top-p(s).top+"px"):(a.style.width=M(r)+"px",a.style.left=p(r).left-p(s).left+"px")}},updateButtonState:function(){var t=this.$refs,n=t.list,a=t.content,s=a.scrollTop,r=a.scrollWidth,o=a.scrollHeight,c=a.offsetWidth,d=a.offsetHeight,V=Math.abs(a.scrollLeft),C=[g(a),D(a)],R=C[0],F=C[1];this.$pcTabs.isVertical()?(this.isPrevButtonEnabled=s!==0,this.isNextButtonEnabled=n.offsetHeight>=d&&parseInt(s)!==o-F):(this.isPrevButtonEnabled=V!==0,this.isNextButtonEnabled=n.offsetWidth>=c&&parseInt(V)!==r-R)},getVisibleButtonWidths:function(){var t=this.$refs,n=t.prevButton,a=t.nextButton,s=0;return this.showNavigators&&(s=(n?.offsetWidth||0)+(a?.offsetWidth||0)),s}},computed:{templates:function(){return this.$pcTabs.$slots},activeValue:function(){return this.$pcTabs.d_value},showNavigators:function(){return this.$pcTabs.scrollable&&this.$pcTabs.showNavigators},prevButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.previous:void 0},nextButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.next:void 0},dataP:function(){return O({scrollable:this.$pcTabs.scrollable})}},components:{ChevronLeftIcon:q,ChevronRightIcon:Q},directives:{ripple:A}},st=["data-p"],ot=["aria-label","tabindex"],it=["data-p"],lt=["aria-orientation"],ct=["aria-label","tabindex"];function ut(e,t,n,a,s,r){var o=z("ripple");return l(),v("div",i({ref:"list",class:e.cx("root"),"data-p":r.dataP},e.ptmi("root")),[r.showNavigators&&s.isPrevButtonEnabled?w((l(),v("button",i({key:0,ref:"prevButton",type:"button",class:e.cx("prevButton"),"aria-label":r.prevButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:t[0]||(t[0]=function(){return r.onPrevButtonClick&&r.onPrevButtonClick.apply(r,arguments)})},e.ptm("prevButton"),{"data-pc-group-section":"navigator"}),[(l(),b(T(r.templates.previcon||"ChevronLeftIcon"),i({"aria-hidden":"true"},e.ptm("prevIcon")),null,16))],16,ot)),[[o]]):N("",!0),m("div",i({ref:"content",class:e.cx("content"),onScroll:t[1]||(t[1]=function(){return r.onScroll&&r.onScroll.apply(r,arguments)}),"data-p":r.dataP},e.ptm("content")),[m("div",i({ref:"tabs",class:e.cx("tabList"),role:"tablist","aria-orientation":r.$pcTabs.orientation||"horizontal"},e.ptm("tabList")),[u(e.$slots,"default"),m("span",i({ref:"inkbar",class:e.cx("activeBar"),role:"presentation","aria-hidden":"true"},e.ptm("activeBar")),null,16)],16,lt)],16,it),r.showNavigators&&s.isNextButtonEnabled?w((l(),v("button",i({key:1,ref:"nextButton",type:"button",class:e.cx("nextButton"),"aria-label":r.nextButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:t[2]||(t[2]=function(){return r.onNextButtonClick&&r.onNextButtonClick.apply(r,arguments)})},e.ptm("nextButton"),{"data-pc-group-section":"navigator"}),[(l(),b(T(r.templates.nexticon||"ChevronRightIcon"),i({"aria-hidden":"true"},e.ptm("nextIcon")),null,16))],16,ct)),[[o]]):N("",!0)],16,st)}I.render=ut;const x=`!absolute flex-shrink-0 top-0 z-20 h-full flex items-center justify-center cursor-pointer
        bg-surface-0 dark:bg-surface-900 text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0 w-10
        shadow-[0px_0px_10px_50px_rgba(255,255,255,0.6)] dark:shadow-[0px_0px_10px_50px] dark:shadow-surface-900/50
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary
        transition-colors duration-200`,dt=L({__name:"TabList",setup(e,{expose:t}){t();const n=P({root:"flex relative",prevButton:x+" start-0",nextButton:x+" end-0",content:`flex-grow
        p-scrollable:overflow-x-auto p-scrollable:overflow-y-hidden p-scrollable:overscroll-y-contain p-scrollable:overscroll-x-auto
        scroll-smooth [scrollbar-width:none]`,tabList:`relative flex bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700
        p-scrollable:overflow-hidden`,activeBar:"z-10 block absolute -bottom-px h-px bg-primary transition-[left] duration-200 ease-[cubic-bezier(0.35,0,0.25,1)]"}),a={navButton:x,theme:n,get TabList(){return I},get ptViewMerge(){return $}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function bt(e,t,n,a,s,r){return l(),b(a.TabList,{unstyled:"",pt:a.theme,ptOptions:{mergeProps:a.ptViewMerge}},{default:h(()=>[u(e.$slots,"default")]),_:3},8,["pt","ptOptions"])}const _t=B(dt,[["render",bt]]);var pt={root:function(t){var n=t.instance,a=t.props;return["p-tab",{"p-tab-active":n.active,"p-disabled":a.disabled}]}},ft=_.extend({name:"tab",classes:pt}),vt={name:"BaseTab",extends:k,props:{value:{type:[String,Number],default:void 0},disabled:{type:Boolean,default:!1},as:{type:[String,Object],default:"BUTTON"},asChild:{type:Boolean,default:!1}},style:ft,provide:function(){return{$pcTab:this,$parentInstance:this}}},E={name:"Tab",extends:vt,inheritAttrs:!1,inject:["$pcTabs","$pcTabList"],methods:{onFocus:function(){this.$pcTabs.selectOnFocus&&this.changeActiveValue()},onClick:function(){this.changeActiveValue()},onKeydown:function(t){switch(t.code){case"ArrowRight":this.onArrowRightKey(t);break;case"ArrowLeft":this.onArrowLeftKey(t);break;case"Home":this.onHomeKey(t);break;case"End":this.onEndKey(t);break;case"PageDown":this.onPageDownKey(t);break;case"PageUp":this.onPageUpKey(t);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(t);break}},onArrowRightKey:function(t){var n=this.findNextTab(t.currentTarget);n?this.changeFocusedTab(t,n):this.onHomeKey(t),t.preventDefault()},onArrowLeftKey:function(t){var n=this.findPrevTab(t.currentTarget);n?this.changeFocusedTab(t,n):this.onEndKey(t),t.preventDefault()},onHomeKey:function(t){var n=this.findFirstTab();this.changeFocusedTab(t,n),t.preventDefault()},onEndKey:function(t){var n=this.findLastTab();this.changeFocusedTab(t,n),t.preventDefault()},onPageDownKey:function(t){this.scrollInView(this.findLastTab()),t.preventDefault()},onPageUpKey:function(t){this.scrollInView(this.findFirstTab()),t.preventDefault()},onEnterKey:function(t){this.changeActiveValue(),t.preventDefault()},findNextTab:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=n?t:t.nextElementSibling;return a?f(a,"data-p-disabled")||f(a,"data-pc-section")==="activebar"?this.findNextTab(a):y(a,'[data-pc-name="tab"]'):null},findPrevTab:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=n?t:t.previousElementSibling;return a?f(a,"data-p-disabled")||f(a,"data-pc-section")==="activebar"?this.findPrevTab(a):y(a,'[data-pc-name="tab"]'):null},findFirstTab:function(){return this.findNextTab(this.$pcTabList.$refs.tabs.firstElementChild,!0)},findLastTab:function(){return this.findPrevTab(this.$pcTabList.$refs.tabs.lastElementChild,!0)},changeActiveValue:function(){this.$pcTabs.updateValue(this.value)},changeFocusedTab:function(t,n){H(n),this.scrollInView(n)},scrollInView:function(t){var n;t==null||(n=t.scrollIntoView)===null||n===void 0||n.call(t,{block:"nearest"})}},computed:{active:function(){var t;return U((t=this.$pcTabs)===null||t===void 0?void 0:t.d_value,this.value)},id:function(){var t;return"".concat((t=this.$pcTabs)===null||t===void 0?void 0:t.$id,"_tab_").concat(this.value)},ariaControls:function(){var t;return"".concat((t=this.$pcTabs)===null||t===void 0?void 0:t.$id,"_tabpanel_").concat(this.value)},attrs:function(){return i(this.asAttrs,this.a11yAttrs,this.ptmi("root",this.ptParams))},asAttrs:function(){return this.as==="BUTTON"?{type:"button",disabled:this.disabled}:void 0},a11yAttrs:function(){return{id:this.id,tabindex:this.active?this.$pcTabs.tabindex:-1,role:"tab","aria-selected":this.active,"aria-controls":this.ariaControls,"data-pc-name":"tab","data-p-disabled":this.disabled,"data-p-active":this.active,onFocus:this.onFocus,onKeydown:this.onKeydown}},ptParams:function(){return{context:{active:this.active}}},dataP:function(){return O({active:this.active})}},directives:{ripple:A}};function ht(e,t,n,a,s,r){var o=z("ripple");return e.asChild?u(e.$slots,"default",{key:1,dataP:r.dataP,class:j(e.cx("root")),active:r.active,a11yAttrs:r.a11yAttrs,onClick:r.onClick}):w((l(),b(T(e.as),i({key:0,class:e.cx("root"),"data-p":r.dataP,onClick:r.onClick},r.attrs),{default:h(function(){return[u(e.$slots,"default")]}),_:3},16,["class","data-p","onClick"])),[[o]])}E.render=ht;const gt=L({__name:"Tab",setup(e,{expose:t}){t();const n=e,a=P({root:`flex-shrink-0 cursor-pointer select-none relative whitespace-nowrap py-4 px-[1.125rem]
        border-b border-surface-200 dark:border-surface-700 font-semibold
        text-surface-500 dark:text-surface-400
        transition-colors duration-200 -mb-px
        not-p-active:enabled:hover:text-surface-700 dark:not-p-active:enabled:hover:text-surface-0
        p-active:border-primary p-active:text-primary
        disabled:pointer-events-none disabled:opacity-60
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary`}),s={props:n,theme:a,get Tab(){return E},get ptViewMerge(){return $}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}});function mt(e,t,n,a,s,r){return l(),b(a.Tab,{value:a.props.value,unstyled:"",pt:a.theme,ptOptions:{mergeProps:a.ptViewMerge}},{default:h(()=>[u(e.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const Lt=B(gt,[["render",mt]]);export{Lt as V,_t as a,$t as b};
