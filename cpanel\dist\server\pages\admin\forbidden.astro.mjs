import { e as createComponent, f as createAstro, k as renderComponent, l as renderScript, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../chunks/AdminLayout_DWqIyijm.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Astro = createAstro();
const $$Forbidden = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Forbidden;
  const user = Astro2.locals.user;
  const userRole = user?.role || "UNKNOWN";
  const getRoleMessage = (role) => {
    switch (role) {
      case "USER":
        return "Обычные пользователи не имеют доступа к административной панели.";
      case "SHOP":
        return "У вас есть доступ к некоторым разделам админ панели, но не к этой странице.";
      case "ADMIN":
        return "Произошла ошибка при проверке ваших прав доступа.";
      default:
        return "Ваша роль не определена или не предоставляет доступ к этому ресурсу.";
    }
  };
  const roleMessage = getRoleMessage(userRole);
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "Доступ запрещен - PartTec", "showSidebar": false }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="min-h-[60vh] flex items-center justify-center"> <div class="max-w-md w-full text-center"> <!-- Иконка ошибки --> <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6"> <svg class="h-12 w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path> </svg> </div> <!-- Заголовок --> <h1 class="text-3xl font-bold text-surface-900 mb-4">
Доступ запрещен
</h1> <!-- Описание ошибки --> <p class="text-lg text-surface-600 mb-2">
У вас недостаточно прав для доступа к этой странице.
</p> <!-- Сообщение в зависимости от роли --> <p class="text-sm text-surface-500 mb-8"> ${roleMessage} </p> <!-- Информация о пользователе (только в development) --> ${false} <!-- Действия --> <div class="space-y-4"> <!-- Кнопка возврата --> <button onclick="history.back()" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors">
Вернуться назад
</button> <!-- Ссылка на главную админ панели --> ${(userRole === "SHOP" || userRole === "ADMIN") && renderTemplate`<a href="/admin" class="block w-full bg-surface-100 hover:bg-surface-200 text-surface-700 font-medium py-3 px-6 rounded-lg transition-colors">
Перейти на главную админ панели
</a>`} <!-- Ссылка на выход для пользователей без доступа --> ${userRole === "USER" && renderTemplate`<a href="/admin/logout" class="block w-full bg-surface-100 hover:bg-surface-200 text-surface-700 font-medium py-3 px-6 rounded-lg transition-colors">
Выйти из системы
</a>`} </div> <!-- Дополнительная информация --> <div class="mt-8 text-xs text-surface-400"> <p>Если вы считаете, что это ошибка, обратитесь к администратору системы.</p> </div> </div> </div> ` })} ${renderScript($$result, "D:/Dev/parttec/cpanel/src/pages/admin/forbidden.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/forbidden.astro", void 0);
const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/forbidden.astro";
const $$url = "/admin/forbidden";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Forbidden,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
