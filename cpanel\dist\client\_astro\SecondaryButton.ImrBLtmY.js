import{s as d}from"./index.DV5zenSI.js";import{_ as s,p as l}from"./utils.BWEB-mtU.js";import{d as p,g as i,o as c,i as u,r as b,w as f,j as x,k as v}from"./index.CpC-7sc3.js";import{b as m,r as g}from"./reactivity.esm-bundler.Bx7uHohy.js";const k=p({__name:"SecondaryButton",setup(r,{expose:t}){t();const e={theme:g({root:`inline-flex cursor-pointer select-none items-center justify-center overflow-hidden relative
        px-3 py-2 gap-2 rounded-md disabled:pointer-events-none disabled:opacity-60 transition-colors duration-200
        bg-surface-100 enabled:hover:bg-surface-200 enabled:active:bg-surface-300
        border border-surface-100 enabled:hover:border-surface-200 enabled:active:border-surface-300
        text-surface-600 enabled:hover:text-surface-700 enabled:active:text-surface-800
        dark:bg-surface-800 dark:enabled:hover:bg-surface-700 dark:enabled:active:bg-surface-600
        dark:border-surface-800 dark:enabled:hover:border-surface-700 dark:enabled:active:border-surface-600
        dark:text-surface-300 dark:enabled:hover:text-surface-200 dark:enabled:active:text-surface-100
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2
        focus-visible:outline-surface-600 dark:focus-visible:outline-surface-300
        p-vertical:flex-col p-fluid:w-full p-fluid:p-icon-only:w-10
        p-icon-only:w-10 p-icon-only:px-0 p-icon-only:gap-0
        p-icon-only:p-rounded:rounded-full p-icon-only:p-rounded:h-10
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-[1.125rem] p-large:px-[0.875rem] p-large:py-[0.625rem]
        p-raised:shadow-sm p-rounded:rounded-[2rem]
        p-outlined:bg-transparent enabled:hover:p-outlined:bg-surface-50 enabled:active:p-outlined:bg-surface-100
        p-outlined:border-surface-200 enabled:hover:p-outlined:border-surface-200 enabled:active:p-outlined:border-surface-200
        p-outlined:text-surface-500 enabled:hover:p-outlined:text-surface-500 enabled:active:p-outlined:text-surface-500
        dark:p-outlined:bg-transparent dark:enabled:hover:p-outlined:bg-white/5 dark:enabled:active:p-outlined:bg-white/15
        dark:p-outlined:border-surface-700 dark:enabled:hover:p-outlined:border-surface-700 dark:enabled:active:p-outlined:border-surface-700
        dark:p-outlined:text-surface-400 dark:enabled:hover:p-outlined:text-surface-400 dark:enabled:active:p-outlined:text-surface-400
        p-text:bg-transparent enabled:hover:p-text:bg-surface-50 enabled:active:p-text:bg-surface-100
        p-text:border-transparent enabled:hover:p-text:border-transparent enabled:active:p-text:border-transparent
        p-text:text-surface-500 enabled:hover:p-text:text-surface-500 enabled:active:p-text:text-surface-500
        dark:p-text:bg-transparent dark:enabled:hover:p-text:bg-surface-800 dark:enabled:active:p-text:bg-surface-700
        dark:p-text:border-transparent dark:enabled:hover:p-text:border-transparent dark:enabled:active:p-text:border-transparent
        dark:p-text:text-surface-400 dark:enabled:hover:p-text:text-surface-400 dark:enabled:active:p-text:text-surface-400
    `,loadingIcon:"",icon:"p-right:order-1 p-bottom:order-2",label:`font-medium p-icon-only:invisible p-icon-only:w-0
        p-small:text-sm p-large:text-[1.125rem]`,pcBadge:{root:"min-w-4 h-4 leading-4"}}),get Button(){return d},get ptViewMerge(){return l}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function h(r,t,n,e,_,y){return c(),i(e.Button,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},u({_:2},[b(r.$slots,(w,a)=>({name:a,fn:f(o=>[x(r.$slots,a,m(v(o??{})))])}))]),1032,["pt","ptOptions"])}const M=s(k,[["render",h]]);export{M as S};
