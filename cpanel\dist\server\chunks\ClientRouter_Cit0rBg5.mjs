import { twMerge } from 'tailwind-merge';
import { mergeProps, createElementBlock, openBlock, renderSlot, createTextVNode, toDisplayString, resolveComponent, resolveDirective, withDirectives, createBlock, resolveDynamicComponent, withCtx, createCommentVNode, normalizeClass, reactive, computed, readonly, Teleport } from 'vue';
import { cn } from '@primeuix/utils';
import { isEmpty, isNotEmpty } from '@primeuix/utils/object';
import SpinnerIcon from '@primevue/icons/spinner';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/badge';
import BaseStyle from '@primevue/core/base/style';
import { getAttribute, removeClass, getHeight, getWidth, getOuterWidth, getOuterHeight, getOffset, addClass, createElement, isClient } from '@primeuix/utils/dom';
import BaseDirective from '@primevue/core/basedirective';
import { style as style$1 } from '@primeuix/styles/ripple';
import { style as style$2 } from '@primeuix/styles/button';
import { U as UserCreateSchema, u as useSession, i as isAdmin, b as isShopOwner, c as canAccessAdmin, a as authClient, h as hasRole, g as getErrorMessage } from './auth-client_DR4Fzy6c.mjs';
import { z } from 'zod';
import { EventBus } from '@primeuix/utils/eventbus';
import { e as createComponent, f as createAstro, h as addAttribute, l as renderScript, r as renderTemplate } from './astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                                  */

const ptViewMerge = (globalPTProps = {}, selfPTProps = {}, datasets) => {
  const { class: globalClass, ...globalRest } = globalPTProps;
  const { class: selfClass, ...selfRest } = selfPTProps;
  return mergeProps({ class: twMerge(globalClass, selfClass) }, globalRest, selfRest, datasets);
};

const _export_sfc = (sfc, props) => {
  const target = sfc.__vccOpts || sfc;
  for (const [key, val] of props) {
    target[key] = val;
  }
  return target;
};

var classes$2 = {
  root: function root(_ref) {
    var props = _ref.props,
      instance = _ref.instance;
    return ['p-badge p-component', {
      'p-badge-circle': isNotEmpty(props.value) && String(props.value).length === 1,
      'p-badge-dot': isEmpty(props.value) && !instance.$slots["default"],
      'p-badge-sm': props.size === 'small',
      'p-badge-lg': props.size === 'large',
      'p-badge-xl': props.size === 'xlarge',
      'p-badge-info': props.severity === 'info',
      'p-badge-success': props.severity === 'success',
      'p-badge-warn': props.severity === 'warn',
      'p-badge-danger': props.severity === 'danger',
      'p-badge-secondary': props.severity === 'secondary',
      'p-badge-contrast': props.severity === 'contrast'
    }];
  }
};
var BadgeStyle = BaseStyle.extend({
  name: 'badge',
  style: style,
  classes: classes$2
});

var script$1$1 = {
  name: 'BaseBadge',
  "extends": BaseComponent,
  props: {
    value: {
      type: [String, Number],
      "default": null
    },
    severity: {
      type: String,
      "default": null
    },
    size: {
      type: String,
      "default": null
    }
  },
  style: BadgeStyle,
  provide: function provide() {
    return {
      $pcBadge: this,
      $parentInstance: this
    };
  }
};

function _typeof$3(o) { "@babel/helpers - typeof"; return _typeof$3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$3(o); }
function _defineProperty$3(e, r, t) { return (r = _toPropertyKey$3(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$3(t) { var i = _toPrimitive$3(t, "string"); return "symbol" == _typeof$3(i) ? i : i + ""; }
function _toPrimitive$3(t, r) { if ("object" != _typeof$3(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$3(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script$3 = {
  name: 'Badge',
  "extends": script$1$1,
  inheritAttrs: false,
  computed: {
    dataP: function dataP() {
      return cn(_defineProperty$3(_defineProperty$3({
        circle: this.value != null && String(this.value).length === 1,
        empty: this.value == null && !this.$slots["default"]
      }, this.severity, this.severity), this.size, this.size));
    }
  }
};

var _hoisted_1$1 = ["data-p"];
function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("span", mergeProps({
    "class": _ctx.cx('root'),
    "data-p": $options.dataP
  }, _ctx.ptmi('root')), [renderSlot(_ctx.$slots, "default", {}, function () {
    return [createTextVNode(toDisplayString(_ctx.value), 1)];
  })], 16, _hoisted_1$1);
}

script$3.render = render$2;

var classes$1 = {
  root: 'p-ink'
};
var RippleStyle = BaseStyle.extend({
  name: 'ripple-directive',
  style: style$1,
  classes: classes$1
});

var BaseRipple = BaseDirective.extend({
  style: RippleStyle
});

function _typeof$2(o) { "@babel/helpers - typeof"; return _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$2(o); }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _defineProperty$2(e, r, t) { return (r = _toPropertyKey$2(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$2(t) { var i = _toPrimitive$2(t, "string"); return "symbol" == _typeof$2(i) ? i : i + ""; }
function _toPrimitive$2(t, r) { if ("object" != _typeof$2(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$2(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var Ripple = BaseRipple.extend('ripple', {
  watch: {
    'config.ripple': function configRipple(newValue) {
      if (newValue) {
        this.createRipple(this.$host);
        this.bindEvents(this.$host);
        this.$host.setAttribute('data-pd-ripple', true);
        this.$host.style['overflow'] = 'hidden';
        this.$host.style['position'] = 'relative';
      } else {
        this.remove(this.$host);
        this.$host.removeAttribute('data-pd-ripple');
      }
    }
  },
  unmounted: function unmounted(el) {
    this.remove(el);
  },
  timeout: undefined,
  methods: {
    bindEvents: function bindEvents(el) {
      el.addEventListener('mousedown', this.onMouseDown.bind(this));
    },
    unbindEvents: function unbindEvents(el) {
      el.removeEventListener('mousedown', this.onMouseDown.bind(this));
    },
    createRipple: function createRipple(el) {
      var ink = this.getInk(el);
      if (!ink) {
        ink = createElement('span', _defineProperty$2(_defineProperty$2({
          role: 'presentation',
          'aria-hidden': true,
          'data-p-ink': true,
          'data-p-ink-active': false,
          "class": !this.isUnstyled() && this.cx('root'),
          onAnimationEnd: this.onAnimationEnd.bind(this)
        }, this.$attrSelector, ''), 'p-bind', this.ptm('root')));
        el.appendChild(ink);
        this.$el = ink;
      }
    },
    remove: function remove(el) {
      var ink = this.getInk(el);
      if (ink) {
        this.$host.style['overflow'] = '';
        this.$host.style['position'] = '';
        this.unbindEvents(el);
        ink.removeEventListener('animationend', this.onAnimationEnd);
        ink.remove();
      }
    },
    onMouseDown: function onMouseDown(event) {
      var _this = this;
      var target = event.currentTarget;
      var ink = this.getInk(target);
      if (!ink || getComputedStyle(ink, null).display === 'none') {
        return;
      }
      !this.isUnstyled() && removeClass(ink, 'p-ink-active');
      ink.setAttribute('data-p-ink-active', 'false');
      if (!getHeight(ink) && !getWidth(ink)) {
        var d = Math.max(getOuterWidth(target), getOuterHeight(target));
        ink.style.height = d + 'px';
        ink.style.width = d + 'px';
      }
      var offset = getOffset(target);
      var x = event.pageX - offset.left + document.body.scrollTop - getWidth(ink) / 2;
      var y = event.pageY - offset.top + document.body.scrollLeft - getHeight(ink) / 2;
      ink.style.top = y + 'px';
      ink.style.left = x + 'px';
      !this.isUnstyled() && addClass(ink, 'p-ink-active');
      ink.setAttribute('data-p-ink-active', 'true');
      this.timeout = setTimeout(function () {
        if (ink) {
          !_this.isUnstyled() && removeClass(ink, 'p-ink-active');
          ink.setAttribute('data-p-ink-active', 'false');
        }
      }, 401);
    },
    onAnimationEnd: function onAnimationEnd(event) {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      !this.isUnstyled() && removeClass(event.currentTarget, 'p-ink-active');
      event.currentTarget.setAttribute('data-p-ink-active', 'false');
    },
    getInk: function getInk(el) {
      return el && el.children ? _toConsumableArray(el.children).find(function (child) {
        return getAttribute(child, 'data-pc-name') === 'ripple';
      }) : undefined;
    }
  }
});

function _typeof$1(o) { "@babel/helpers - typeof"; return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$1(o); }
function _defineProperty$1(e, r, t) { return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$1(t) { var i = _toPrimitive$1(t, "string"); return "symbol" == _typeof$1(i) ? i : i + ""; }
function _toPrimitive$1(t, r) { if ("object" != _typeof$1(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$1(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var classes = {
  root: function root(_ref) {
    var instance = _ref.instance,
      props = _ref.props;
    return ['p-button p-component', _defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1(_defineProperty$1({
      'p-button-icon-only': instance.hasIcon && !props.label && !props.badge,
      'p-button-vertical': (props.iconPos === 'top' || props.iconPos === 'bottom') && props.label,
      'p-button-loading': props.loading,
      'p-button-link': props.link || props.variant === 'link'
    }, "p-button-".concat(props.severity), props.severity), 'p-button-raised', props.raised), 'p-button-rounded', props.rounded), 'p-button-text', props.text || props.variant === 'text'), 'p-button-outlined', props.outlined || props.variant === 'outlined'), 'p-button-sm', props.size === 'small'), 'p-button-lg', props.size === 'large'), 'p-button-plain', props.plain), 'p-button-fluid', instance.hasFluid)];
  },
  loadingIcon: 'p-button-loading-icon',
  icon: function icon(_ref3) {
    var props = _ref3.props;
    return ['p-button-icon', _defineProperty$1({}, "p-button-icon-".concat(props.iconPos), props.label)];
  },
  label: 'p-button-label'
};
var ButtonStyle = BaseStyle.extend({
  name: 'button',
  style: style$2,
  classes: classes
});

var script$1 = {
  name: 'BaseButton',
  "extends": BaseComponent,
  props: {
    label: {
      type: String,
      "default": null
    },
    icon: {
      type: String,
      "default": null
    },
    iconPos: {
      type: String,
      "default": 'left'
    },
    iconClass: {
      type: [String, Object],
      "default": null
    },
    badge: {
      type: String,
      "default": null
    },
    badgeClass: {
      type: [String, Object],
      "default": null
    },
    badgeSeverity: {
      type: String,
      "default": 'secondary'
    },
    loading: {
      type: Boolean,
      "default": false
    },
    loadingIcon: {
      type: String,
      "default": undefined
    },
    as: {
      type: [String, Object],
      "default": 'BUTTON'
    },
    asChild: {
      type: Boolean,
      "default": false
    },
    link: {
      type: Boolean,
      "default": false
    },
    severity: {
      type: String,
      "default": null
    },
    raised: {
      type: Boolean,
      "default": false
    },
    rounded: {
      type: Boolean,
      "default": false
    },
    text: {
      type: Boolean,
      "default": false
    },
    outlined: {
      type: Boolean,
      "default": false
    },
    size: {
      type: String,
      "default": null
    },
    variant: {
      type: String,
      "default": null
    },
    plain: {
      type: Boolean,
      "default": false
    },
    fluid: {
      type: Boolean,
      "default": null
    }
  },
  style: ButtonStyle,
  provide: function provide() {
    return {
      $pcButton: this,
      $parentInstance: this
    };
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script$2 = {
  name: 'Button',
  "extends": script$1,
  inheritAttrs: false,
  inject: {
    $pcFluid: {
      "default": null
    }
  },
  methods: {
    getPTOptions: function getPTOptions(key) {
      var _ptm = key === 'root' ? this.ptmi : this.ptm;
      return _ptm(key, {
        context: {
          disabled: this.disabled
        }
      });
    }
  },
  computed: {
    disabled: function disabled() {
      return this.$attrs.disabled || this.$attrs.disabled === '' || this.loading;
    },
    defaultAriaLabel: function defaultAriaLabel() {
      return this.label ? this.label + (this.badge ? ' ' + this.badge : '') : this.$attrs.ariaLabel;
    },
    hasIcon: function hasIcon() {
      return this.icon || this.$slots.icon;
    },
    attrs: function attrs() {
      return mergeProps(this.asAttrs, this.a11yAttrs, this.getPTOptions('root'));
    },
    asAttrs: function asAttrs() {
      return this.as === 'BUTTON' ? {
        type: 'button',
        disabled: this.disabled
      } : undefined;
    },
    a11yAttrs: function a11yAttrs() {
      return {
        'aria-label': this.defaultAriaLabel,
        'data-pc-name': 'button',
        'data-p-disabled': this.disabled,
        'data-p-severity': this.severity
      };
    },
    hasFluid: function hasFluid() {
      return isEmpty(this.fluid) ? !!this.$pcFluid : this.fluid;
    },
    dataP: function dataP() {
      return cn(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, this.size, this.size), 'icon-only', this.hasIcon && !this.label && !this.badge), "loading", this.loading), "fluid", this.hasFluid), "rounded", this.rounded), "raised", this.raised), "outlined", this.outlined || this.variant === 'outlined'), "text", this.text || this.variant === 'text'), "link", this.link || this.variant === 'link'), "vertical", (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label));
    },
    dataIconP: function dataIconP() {
      return cn(_defineProperty(_defineProperty({}, this.iconPos, this.iconPos), this.size, this.size));
    },
    dataLabelP: function dataLabelP() {
      return cn(_defineProperty(_defineProperty({}, this.size, this.size), 'icon-only', this.hasIcon && !this.label && !this.badge));
    }
  },
  components: {
    SpinnerIcon: SpinnerIcon,
    Badge: script$3
  },
  directives: {
    ripple: Ripple
  }
};

var _hoisted_1 = ["data-p"];
var _hoisted_2 = ["data-p"];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_SpinnerIcon = resolveComponent("SpinnerIcon");
  var _component_Badge = resolveComponent("Badge");
  var _directive_ripple = resolveDirective("ripple");
  return !_ctx.asChild ? withDirectives((openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
    key: 0,
    "class": _ctx.cx('root'),
    "data-p": $options.dataP
  }, $options.attrs), {
    "default": withCtx(function () {
      return [renderSlot(_ctx.$slots, "default", {}, function () {
        return [_ctx.loading ? renderSlot(_ctx.$slots, "loadingicon", mergeProps({
          key: 0,
          "class": [_ctx.cx('loadingIcon'), _ctx.cx('icon')]
        }, _ctx.ptm('loadingIcon')), function () {
          return [_ctx.loadingIcon ? (openBlock(), createElementBlock("span", mergeProps({
            key: 0,
            "class": [_ctx.cx('loadingIcon'), _ctx.cx('icon'), _ctx.loadingIcon]
          }, _ctx.ptm('loadingIcon')), null, 16)) : (openBlock(), createBlock(_component_SpinnerIcon, mergeProps({
            key: 1,
            "class": [_ctx.cx('loadingIcon'), _ctx.cx('icon')],
            spin: ""
          }, _ctx.ptm('loadingIcon')), null, 16, ["class"]))];
        }) : renderSlot(_ctx.$slots, "icon", mergeProps({
          key: 1,
          "class": [_ctx.cx('icon')]
        }, _ctx.ptm('icon')), function () {
          return [_ctx.icon ? (openBlock(), createElementBlock("span", mergeProps({
            key: 0,
            "class": [_ctx.cx('icon'), _ctx.icon, _ctx.iconClass],
            "data-p": $options.dataIconP
          }, _ctx.ptm('icon')), null, 16, _hoisted_1)) : createCommentVNode("", true)];
        }), _ctx.label ? (openBlock(), createElementBlock("span", mergeProps({
          key: 2,
          "class": _ctx.cx('label')
        }, _ctx.ptm('label'), {
          "data-p": $options.dataLabelP
        }), toDisplayString(_ctx.label), 17, _hoisted_2)) : createCommentVNode("", true), _ctx.badge ? (openBlock(), createBlock(_component_Badge, {
          key: 3,
          value: _ctx.badge,
          "class": normalizeClass(_ctx.badgeClass),
          severity: _ctx.badgeSeverity,
          unstyled: _ctx.unstyled,
          pt: _ctx.ptm('pcBadge')
        }, null, 8, ["value", "class", "severity", "unstyled", "pt"])) : createCommentVNode("", true)];
      })];
    }),
    _: 3
  }, 16, ["class", "data-p"])), [[_directive_ripple]]) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    "class": normalizeClass(_ctx.cx('root')),
    a11yAttrs: $options.a11yAttrs
  });
}

script$2.render = render$1;

const LoginFormSchema = z.object({
  email: z.string().email("Введите корректный email"),
  password: z.string().min(6, "Пароль должен содержать минимум 6 символов"),
  rememberMe: z.boolean().optional().default(false)
});
const RegisterFormSchema = UserCreateSchema.extend({
  password: z.string().min(6, "Пароль должен содержать минимум 6 символов"),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine((val) => val === true, "Необходимо принять условия использования")
}).refine((data) => data.password === data.confirmPassword, {
  message: "Пароли не совпадают",
  path: ["confirmPassword"]
});
const loadingState = reactive({
  signIn: false,
  signUp: false,
  signOut: false,
  session: false
});
function useAuth() {
  const sessionData = useSession();
  const session = computed(() => sessionData.value?.data || null);
  const user = computed(() => session.value?.user);
  const isAuthenticated = computed(() => !!session.value?.user);
  const isLoading = computed(
    () => Object.values(loadingState).some((loading) => loading)
  );
  const refetchSession = async () => {
    try {
      const result = await authClient.getSession();
      if (result.data) {
        return result.data;
      }
    } catch (error) {
      console.error("Error refetching session:", error);
    }
  };
  const userRole = computed(() => user.value?.role || null);
  const isUserAdmin = computed(() => user.value ? isAdmin(user.value) : false);
  const isUserShopOwner = computed(() => user.value ? isShopOwner(user.value) : false);
  const isUserRegular = computed(() => user.value?.role === "USER");
  const userCanAccessAdmin = computed(() => user.value ? canAccessAdmin(user.value) : false);
  const signIn = async (data) => {
    const validationResult = LoginFormSchema.safeParse(data);
    if (!validationResult.success) {
      return {
        error: {
          message: "Ошибка валидации",
          details: validationResult.error.issues
        }
      };
    }
    loadingState.signIn = true;
    try {
      const result = await authClient.signIn.email({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe
      });
      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } };
      }
      await refetchSession();
      return { data: result.data };
    } catch (error) {
      return { error: { message: getErrorMessage(error, "Произошла ошибка при входе") } };
    } finally {
      loadingState.signIn = false;
    }
  };
  const signUp = async (data) => {
    const validationResult = RegisterFormSchema.safeParse(data);
    if (!validationResult.success) {
      return {
        error: {
          message: "Ошибка валидации",
          details: validationResult.error.issues
        }
      };
    }
    loadingState.signUp = true;
    try {
      const result = await authClient.signUp.email({
        email: data.email,
        password: data.password,
        name: data.name || ""
        // Роль будет установлена через дополнительные поля на сервере
      });
      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } };
      }
      if (data.role && data.role !== "USER") {
        try {
          const adminRole = data.role === "ADMIN" ? "admin" : "user";
          await authClient.admin.setRole({
            userId: result.data?.user?.id,
            role: adminRole
          });
        } catch (roleError) {
          console.warn("Failed to set user role:", roleError);
        }
      }
      return { data: result.data };
    } catch (error) {
      return { error: { message: getErrorMessage(error, "Произошла ошибка при регистрации") } };
    } finally {
      loadingState.signUp = false;
    }
  };
  const signOut = async () => {
    loadingState.signOut = true;
    try {
      const result = await authClient.signOut();
      if (result.error) {
        return { error: { message: getErrorMessage(result.error) } };
      }
      await refetchSession();
      return { data: result.data };
    } catch (error) {
      return { error: { message: getErrorMessage(error, "Произошла ошибка при выходе") } };
    } finally {
      loadingState.signOut = false;
    }
  };
  const checkRole = (role) => {
    return user.value ? hasRole(user.value, role) : false;
  };
  const displayName = computed(() => {
    if (!user.value) return null;
    return user.value.name || user.value.email;
  });
  const userAvatar = computed(() => {
    return user.value?.image || null;
  });
  return {
    // Состояние
    user,
    session,
    isAuthenticated,
    isLoading,
    // Роли
    userRole,
    isUserAdmin,
    isUserShopOwner,
    isUserRegular,
    userCanAccessAdmin,
    // Пользовательские данные
    displayName,
    userAvatar,
    // Методы
    signIn,
    signUp,
    signOut,
    checkRole,
    refetchSession,
    // Состояния загрузки
    loadingState: readonly(loadingState),
    // Схемы валидации
    LoginFormSchema,
    RegisterFormSchema
  };
}

var OverlayEventBus = EventBus();

var script = {
  name: 'Portal',
  props: {
    appendTo: {
      type: [String, Object],
      "default": 'body'
    },
    disabled: {
      type: Boolean,
      "default": false
    }
  },
  data: function data() {
    return {
      mounted: false
    };
  },
  mounted: function mounted() {
    this.mounted = isClient();
  },
  computed: {
    inline: function inline() {
      return this.disabled || this.appendTo === 'self';
    }
  }
};

function render(_ctx, _cache, $props, $setup, $data, $options) {
  return $options.inline ? renderSlot(_ctx.$slots, "default", {
    key: 0
  }) : $data.mounted ? (openBlock(), createBlock(Teleport, {
    key: 1,
    to: $props.appendTo
  }, [renderSlot(_ctx.$slots, "default")], 8, ["to"])) : createCommentVNode("", true);
}

script.render = render;

let navigateOnServerWarned = false;
async function navigate(href, options) {
  {
    if (!navigateOnServerWarned) {
      const warning = new Error(
        "The view transitions client API was called during a server side render. This may be unintentional as the navigate() function is expected to be called in response to user interactions. Please make sure that your usage is correct."
      );
      warning.name = "Warning";
      console.warn(warning);
      navigateOnServerWarned = true;
    }
    return;
  }
}

const $$Astro = createAstro();
const $$ClientRouter = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ClientRouter;
  const { fallback = "animate" } = Astro2.props;
  return renderTemplate`<meta name="astro-view-transitions-enabled" content="true"><meta name="astro-view-transitions-fallback"${addAttribute(fallback, "content")}>${renderScript($$result, "D:/Dev/parttec/cpanel/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Dev/parttec/cpanel/node_modules/astro/components/ClientRouter.astro", void 0);

export { $$ClientRouter as $, OverlayEventBus as O, Ripple as R, _export_sfc as _, script$2 as a, script$3 as b, navigate as n, ptViewMerge as p, script as s, useAuth as u };
