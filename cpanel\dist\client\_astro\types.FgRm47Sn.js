var g;(function(r){r.assertEqual=a=>{};function e(a){}r.assertIs=e;function t(a){throw new Error}r.assertNever=t,r.arrayToEnum=a=>{const n={};for(const i of a)n[i]=i;return n},r.getValidEnumValues=a=>{const n=r.objectKeys(a).filter(o=>typeof a[a[o]]!="number"),i={};for(const o of n)i[o]=a[o];return r.objectValues(i)},r.objectValues=a=>r.objectKeys(a).map(function(n){return a[n]}),r.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const n=[];for(const i in a)Object.prototype.hasOwnProperty.call(a,i)&&n.push(i);return n},r.find=(a,n)=>{for(const i of a)if(n(i))return i},r.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function s(a,n=" | "){return a.map(i=>typeof i=="string"?`'${i}'`:i).join(n)}r.joinValues=s,r.jsonStringifyReplacer=(a,n)=>typeof n=="bigint"?n.toString():n})(g||(g={}));var se;(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(se||(se={}));const u=g.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),S=r=>{switch(typeof r){case"undefined":return u.undefined;case"string":return u.string;case"number":return Number.isNaN(r)?u.nan:u.number;case"boolean":return u.boolean;case"function":return u.function;case"bigint":return u.bigint;case"symbol":return u.symbol;case"object":return Array.isArray(r)?u.array:r===null?u.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?u.promise:typeof Map<"u"&&r instanceof Map?u.map:typeof Set<"u"&&r instanceof Set?u.set:typeof Date<"u"&&r instanceof Date?u.date:u.object;default:return u.unknown}},d=g.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class O extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(n){return n.message},s={_errors:[]},a=n=>{for(const i of n.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,h=0;for(;h<i.path.length;){const f=i.path[h];h===i.path.length-1?(o[f]=o[f]||{_errors:[]},o[f]._errors.push(t(i))):o[f]=o[f]||{_errors:[]},o=o[f],h++}}};return a(this),s}static assert(e){if(!(e instanceof O))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,g.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)if(a.path.length>0){const n=a.path[0];t[n]=t[n]||[],t[n].push(e(a))}else s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}O.create=r=>new O(r);const Y=(r,e)=>{let t;switch(r.code){case d.invalid_type:r.received===u.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,g.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${g.joinValues(r.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${g.joinValues(r.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${g.joinValues(r.options)}, received '${r.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:g.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case d.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="bigint"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case d.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,g.assertNever(r)}return{message:t}};let xe=Y;function ke(){return xe}const be=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,n=[...t,...a.path||[]],i={...a,path:n};if(a.message!==void 0)return{...a,path:n,message:a.message};let o="";const h=s.filter(f=>!!f).slice().reverse();for(const f of h)o=f(i,{data:e,defaultError:o}).message;return{...a,path:n,message:o}};function c(r,e){const t=ke(),s=be({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===Y?void 0:Y].filter(a=>!!a)});r.common.issues.push(s)}class k{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return p;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const n=await a.key,i=await a.value;s.push({key:n,value:i})}return k.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:n,value:i}=a;if(n.status==="aborted"||i.status==="aborted")return p;n.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),n.value!=="__proto__"&&(typeof i.value<"u"||a.alwaysSet)&&(s[n.value]=i.value)}return{status:e.value,value:s}}}const p=Object.freeze({status:"aborted"}),L=r=>({status:"dirty",value:r}),b=r=>({status:"valid",value:r}),re=r=>r.status==="aborted",ae=r=>r.status==="dirty",j=r=>r.status==="valid",U=r=>typeof Promise<"u"&&r instanceof Promise;var l;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e?.message})(l||(l={}));class T{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ne=(r,e)=>{if(j(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new O(r.common.issues);return this._error=t,this._error}}};function y(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(i,o)=>{const{message:h}=r;return i.code==="invalid_enum_value"?{message:h??o.defaultError}:typeof o.data>"u"?{message:h??s??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:h??t??o.defaultError}},description:a}}class _{get description(){return this._def.description}_getType(e){return S(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:S(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new k,ctx:{common:e.parent.common,data:e.data,parsedType:S(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(U(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){const s={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)},a=this._parseSync({data:e,path:s.path,parent:s});return ne(s,a)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)};if(!this["~standard"].async)try{const s=this._parseSync({data:e,path:[],parent:t});return j(s)?{value:s.value}:{issues:t.common.issues}}catch(s){s?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(s=>j(s)?{value:s.value}:{issues:t.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)},a=this._parse({data:e,path:s.path,parent:s}),n=await(U(a)?a:Promise.resolve(a));return ne(s,n)}refine(e,t){const s=a=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,n)=>{const i=e(a),o=()=>n.addIssue({code:d.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(h=>h?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((s,a)=>e(s)?!0:(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new $({schema:this,typeName:m.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return N.create(this,this._def)}nullable(){return V.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return w.create(this)}promise(){return q.create(this,this._def)}or(e){return B.create([this,e],this._def)}and(e){return F.create(this,e,this._def)}transform(e){return new $({...y(this._def),schema:this,typeName:m.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new X({...y(this._def),innerType:this,defaultValue:t,typeName:m.ZodDefault})}brand(){return new We({typeName:m.ZodBranded,type:this,...y(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new K({...y(this._def),innerType:this,catchValue:t,typeName:m.ZodCatch})}describe(e){const t=this.constructor;return new t({...this._def,description:e})}pipe(e){return te.create(this,e)}readonly(){return ee.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const we=/^c[^\s-]{8,}$/i,Te=/^[0-9a-z]+$/,Ce=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Oe=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Se=/^[a-z0-9_-]{21}$/i,Ne=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ae=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ze=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Re="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let J;const je=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ie=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Ee=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,$e=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Ve=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Me=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ye="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Le=new RegExp(`^${ye}$`);function _e(r){let e="[0-5]\\d";r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`);const t=r.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function ze(r){return new RegExp(`^${_e(r)}$`)}function Pe(r){let e=`${ye}T${_e(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function De(r,e){return!!((e==="v4"||!e)&&je.test(r)||(e==="v6"||!e)&&Ee.test(r))}function Ue(r,e){if(!Ne.test(r))return!1;try{const[t]=r.split(".");if(!t)return!1;const s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return!(typeof a!="object"||a===null||"typ"in a&&a?.typ!=="JWT"||!a.alg||e&&a.alg!==e)}catch{return!1}}function Be(r,e){return!!((e==="v4"||!e)&&Ie.test(r)||(e==="v6"||!e)&&$e.test(r))}class C extends _{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==u.string){const n=this._getOrReturnCtx(e);return c(n,{code:d.invalid_type,expected:u.string,received:n.parsedType}),p}const s=new k;let a;for(const n of this._def.checks)if(n.kind==="min")e.data.length<n.value&&(a=this._getOrReturnCtx(e,a),c(a,{code:d.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="max")e.data.length>n.value&&(a=this._getOrReturnCtx(e,a),c(a,{code:d.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="length"){const i=e.data.length>n.value,o=e.data.length<n.value;(i||o)&&(a=this._getOrReturnCtx(e,a),i?c(a,{code:d.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):o&&c(a,{code:d.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),s.dirty())}else if(n.kind==="email")Ze.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"email",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="emoji")J||(J=new RegExp(Re,"u")),J.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"emoji",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="uuid")Oe.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"uuid",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="nanoid")Se.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"nanoid",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid")we.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"cuid",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid2")Te.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"cuid2",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="ulid")Ce.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"ulid",code:d.invalid_string,message:n.message}),s.dirty());else if(n.kind==="url")try{new URL(e.data)}catch{a=this._getOrReturnCtx(e,a),c(a,{validation:"url",code:d.invalid_string,message:n.message}),s.dirty()}else n.kind==="regex"?(n.regex.lastIndex=0,n.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"regex",code:d.invalid_string,message:n.message}),s.dirty())):n.kind==="trim"?e.data=e.data.trim():n.kind==="includes"?e.data.includes(n.value,n.position)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),s.dirty()):n.kind==="toLowerCase"?e.data=e.data.toLowerCase():n.kind==="toUpperCase"?e.data=e.data.toUpperCase():n.kind==="startsWith"?e.data.startsWith(n.value)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:{startsWith:n.value},message:n.message}),s.dirty()):n.kind==="endsWith"?e.data.endsWith(n.value)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:{endsWith:n.value},message:n.message}),s.dirty()):n.kind==="datetime"?Pe(n).test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:"datetime",message:n.message}),s.dirty()):n.kind==="date"?Le.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:"date",message:n.message}),s.dirty()):n.kind==="time"?ze(n).test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{code:d.invalid_string,validation:"time",message:n.message}),s.dirty()):n.kind==="duration"?Ae.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"duration",code:d.invalid_string,message:n.message}),s.dirty()):n.kind==="ip"?De(e.data,n.version)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"ip",code:d.invalid_string,message:n.message}),s.dirty()):n.kind==="jwt"?Ue(e.data,n.alg)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"jwt",code:d.invalid_string,message:n.message}),s.dirty()):n.kind==="cidr"?Be(e.data,n.version)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"cidr",code:d.invalid_string,message:n.message}),s.dirty()):n.kind==="base64"?Ve.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"base64",code:d.invalid_string,message:n.message}),s.dirty()):n.kind==="base64url"?Me.test(e.data)||(a=this._getOrReturnCtx(e,a),c(a,{validation:"base64url",code:d.invalid_string,message:n.message}),s.dirty()):g.assertNever(n);return{status:s.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:d.invalid_string,...l.errToObj(s)})}_addCheck(e){return new C({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...l.errToObj(e)})}url(e){return this._addCheck({kind:"url",...l.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...l.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...l.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...l.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...l.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...l.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...l.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...l.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...l.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...l.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...l.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...l.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...l.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...l.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...l.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...l.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...l.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...l.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...l.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...l.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...l.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...l.errToObj(t)})}nonempty(e){return this.min(1,l.errToObj(e))}trim(){return new C({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new C({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new C({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}C.create=r=>new C({checks:[],typeName:m.ZodString,coerce:r?.coerce??!1,...y(r)});function Fe(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s,n=Number.parseInt(r.toFixed(a).replace(".","")),i=Number.parseInt(e.toFixed(a).replace(".",""));return n%i/10**a}class I extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==u.number){const n=this._getOrReturnCtx(e);return c(n,{code:d.invalid_type,expected:u.number,received:n.parsedType}),p}let s;const a=new k;for(const n of this._def.checks)n.kind==="int"?g.isInteger(e.data)||(s=this._getOrReturnCtx(e,s),c(s,{code:d.invalid_type,expected:"integer",received:"float",message:n.message}),a.dirty()):n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):n.kind==="multipleOf"?Fe(e.data,n.value)!==0&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):n.kind==="finite"?Number.isFinite(e.data)||(s=this._getOrReturnCtx(e,s),c(s,{code:d.not_finite,message:n.message}),a.dirty()):g.assertNever(n);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,l.toString(t))}gt(e,t){return this.setLimit("min",e,!1,l.toString(t))}lte(e,t){return this.setLimit("max",e,!0,l.toString(t))}lt(e,t){return this.setLimit("max",e,!1,l.toString(t))}setLimit(e,t,s,a){return new I({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:l.toString(a)}]})}_addCheck(e){return new I({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:l.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:l.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:l.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:l.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:l.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:l.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:l.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:l.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:l.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&g.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}I.create=r=>new I({checks:[],typeName:m.ZodNumber,coerce:r?.coerce||!1,...y(r)});class z extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==u.bigint)return this._getInvalidInput(e);let s;const a=new k;for(const n of this._def.checks)n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):n.kind==="multipleOf"?e.data%n.value!==BigInt(0)&&(s=this._getOrReturnCtx(e,s),c(s,{code:d.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):g.assertNever(n);return{status:a.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return c(t,{code:d.invalid_type,expected:u.bigint,received:t.parsedType}),p}gte(e,t){return this.setLimit("min",e,!0,l.toString(t))}gt(e,t){return this.setLimit("min",e,!1,l.toString(t))}lte(e,t){return this.setLimit("max",e,!0,l.toString(t))}lt(e,t){return this.setLimit("max",e,!1,l.toString(t))}setLimit(e,t,s,a){return new z({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:l.toString(a)}]})}_addCheck(e){return new z({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:l.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:l.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:l.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:l.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:l.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}z.create=r=>new z({checks:[],typeName:m.ZodBigInt,coerce:r?.coerce??!1,...y(r)});class H extends _{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==u.boolean){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.boolean,received:s.parsedType}),p}return b(e.data)}}H.create=r=>new H({typeName:m.ZodBoolean,coerce:r?.coerce||!1,...y(r)});class P extends _{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==u.date){const n=this._getOrReturnCtx(e);return c(n,{code:d.invalid_type,expected:u.date,received:n.parsedType}),p}if(Number.isNaN(e.data.getTime())){const n=this._getOrReturnCtx(e);return c(n,{code:d.invalid_date}),p}const s=new k;let a;for(const n of this._def.checks)n.kind==="min"?e.data.getTime()<n.value&&(a=this._getOrReturnCtx(e,a),c(a,{code:d.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),s.dirty()):n.kind==="max"?e.data.getTime()>n.value&&(a=this._getOrReturnCtx(e,a),c(a,{code:d.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),s.dirty()):g.assertNever(n);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new P({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:l.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:l.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}P.create=r=>new P({checks:[],coerce:r?.coerce||!1,typeName:m.ZodDate,...y(r)});class ie extends _{_parse(e){if(this._getType(e)!==u.symbol){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.symbol,received:s.parsedType}),p}return b(e.data)}}ie.create=r=>new ie({typeName:m.ZodSymbol,...y(r)});class oe extends _{_parse(e){if(this._getType(e)!==u.undefined){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.undefined,received:s.parsedType}),p}return b(e.data)}}oe.create=r=>new oe({typeName:m.ZodUndefined,...y(r)});class de extends _{_parse(e){if(this._getType(e)!==u.null){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.null,received:s.parsedType}),p}return b(e.data)}}de.create=r=>new de({typeName:m.ZodNull,...y(r)});class ce extends _{constructor(){super(...arguments),this._any=!0}_parse(e){return b(e.data)}}ce.create=r=>new ce({typeName:m.ZodAny,...y(r)});class G extends _{constructor(){super(...arguments),this._unknown=!0}_parse(e){return b(e.data)}}G.create=r=>new G({typeName:m.ZodUnknown,...y(r)});class A extends _{_parse(e){const t=this._getOrReturnCtx(e);return c(t,{code:d.invalid_type,expected:u.never,received:t.parsedType}),p}}A.create=r=>new A({typeName:m.ZodNever,...y(r)});class ue extends _{_parse(e){if(this._getType(e)!==u.undefined){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.void,received:s.parsedType}),p}return b(e.data)}}ue.create=r=>new ue({typeName:m.ZodVoid,...y(r)});class w extends _{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==u.array)return c(t,{code:d.invalid_type,expected:u.array,received:t.parsedType}),p;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,o=t.data.length<a.exactLength.value;(i||o)&&(c(t,{code:i?d.too_big:d.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(c(t,{code:d.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(c(t,{code:d.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>a.type._parseAsync(new T(t,i,t.path,o)))).then(i=>k.mergeArray(s,i));const n=[...t.data].map((i,o)=>a.type._parseSync(new T(t,i,t.path,o)));return k.mergeArray(s,n)}get element(){return this._def.type}min(e,t){return new w({...this._def,minLength:{value:e,message:l.toString(t)}})}max(e,t){return new w({...this._def,maxLength:{value:e,message:l.toString(t)}})}length(e,t){return new w({...this._def,exactLength:{value:e,message:l.toString(t)}})}nonempty(e){return this.min(1,e)}}w.create=(r,e)=>new w({type:r,minLength:null,maxLength:null,exactLength:null,typeName:m.ZodArray,...y(e)});function R(r){if(r instanceof x){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=N.create(R(s))}return new x({...r._def,shape:()=>e})}else return r instanceof w?new w({...r._def,type:R(r.element)}):r instanceof N?N.create(R(r.unwrap())):r instanceof V?V.create(R(r.unwrap())):r instanceof Z?Z.create(r.items.map(e=>R(e))):r}class x extends _{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=g.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==u.object){const f=this._getOrReturnCtx(e);return c(f,{code:d.invalid_type,expected:u.object,received:f.parsedType}),p}const{status:s,ctx:a}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof A&&this._def.unknownKeys==="strip"))for(const f in a.data)i.includes(f)||o.push(f);const h=[];for(const f of i){const v=n[f],M=a.data[f];h.push({key:{status:"valid",value:f},value:v._parse(new T(a,M,a.path,f)),alwaysSet:f in a.data})}if(this._def.catchall instanceof A){const f=this._def.unknownKeys;if(f==="passthrough")for(const v of o)h.push({key:{status:"valid",value:v},value:{status:"valid",value:a.data[v]}});else if(f==="strict")o.length>0&&(c(a,{code:d.unrecognized_keys,keys:o}),s.dirty());else if(f!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const f=this._def.catchall;for(const v of o){const M=a.data[v];h.push({key:{status:"valid",value:v},value:f._parse(new T(a,M,a.path,v)),alwaysSet:v in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const f=[];for(const v of h){const M=await v.key,ve=await v.value;f.push({key:M,value:ve,alwaysSet:v.alwaysSet})}return f}).then(f=>k.mergeObjectSync(s,f)):k.mergeObjectSync(s,h)}get shape(){return this._def.shape()}strict(e){return l.errToObj,new x({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{const a=this._def.errorMap?.(t,s).message??s.defaultError;return t.code==="unrecognized_keys"?{message:l.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new x({...this._def,unknownKeys:"strip"})}passthrough(){return new x({...this._def,unknownKeys:"passthrough"})}extend(e){return new x({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new x({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:m.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new x({...this._def,catchall:e})}pick(e){const t={};for(const s of g.objectKeys(e))e[s]&&this.shape[s]&&(t[s]=this.shape[s]);return new x({...this._def,shape:()=>t})}omit(e){const t={};for(const s of g.objectKeys(this.shape))e[s]||(t[s]=this.shape[s]);return new x({...this._def,shape:()=>t})}deepPartial(){return R(this)}partial(e){const t={};for(const s of g.objectKeys(this.shape)){const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}return new x({...this._def,shape:()=>t})}required(e){const t={};for(const s of g.objectKeys(this.shape))if(e&&!e[s])t[s]=this.shape[s];else{let n=this.shape[s];for(;n instanceof N;)n=n._def.innerType;t[s]=n}return new x({...this._def,shape:()=>t})}keyof(){return ge(g.objectKeys(this.shape))}}x.create=(r,e)=>new x({shape:()=>r,unknownKeys:"strip",catchall:A.create(),typeName:m.ZodObject,...y(e)});x.strictCreate=(r,e)=>new x({shape:()=>r,unknownKeys:"strict",catchall:A.create(),typeName:m.ZodObject,...y(e)});x.lazycreate=(r,e)=>new x({shape:r,unknownKeys:"strip",catchall:A.create(),typeName:m.ZodObject,...y(e)});class B extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;function a(n){for(const o of n)if(o.result.status==="valid")return o.result;for(const o of n)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=n.map(o=>new O(o.ctx.common.issues));return c(t,{code:d.invalid_union,unionErrors:i}),p}if(t.common.async)return Promise.all(s.map(async n=>{const i={...t,common:{...t.common,issues:[]},parent:null};return{result:await n._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(a);{let n;const i=[];for(const h of s){const f={...t,common:{...t.common,issues:[]},parent:null},v=h._parseSync({data:t.data,path:t.path,parent:f});if(v.status==="valid")return v;v.status==="dirty"&&!n&&(n={result:v,ctx:f}),f.common.issues.length&&i.push(f.common.issues)}if(n)return t.common.issues.push(...n.ctx.common.issues),n.result;const o=i.map(h=>new O(h));return c(t,{code:d.invalid_union,unionErrors:o}),p}}get options(){return this._def.options}}B.create=(r,e)=>new B({options:r,typeName:m.ZodUnion,...y(e)});function Q(r,e){const t=S(r),s=S(e);if(r===e)return{valid:!0,data:r};if(t===u.object&&s===u.object){const a=g.objectKeys(e),n=g.objectKeys(r).filter(o=>a.indexOf(o)!==-1),i={...r,...e};for(const o of n){const h=Q(r[o],e[o]);if(!h.valid)return{valid:!1};i[o]=h.data}return{valid:!0,data:i}}else if(t===u.array&&s===u.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let n=0;n<r.length;n++){const i=r[n],o=e[n],h=Q(i,o);if(!h.valid)return{valid:!1};a.push(h.data)}return{valid:!0,data:a}}else return t===u.date&&s===u.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class F extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(n,i)=>{if(re(n)||re(i))return p;const o=Q(n.value,i.value);return o.valid?((ae(n)||ae(i))&&t.dirty(),{status:t.value,value:o.data}):(c(s,{code:d.invalid_intersection_types}),p)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([n,i])=>a(n,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}F.create=(r,e,t)=>new F({left:r,right:e,typeName:m.ZodIntersection,...y(t)});class Z extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==u.array)return c(s,{code:d.invalid_type,expected:u.array,received:s.parsedType}),p;if(s.data.length<this._def.items.length)return c(s,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),p;!this._def.rest&&s.data.length>this._def.items.length&&(c(s,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const n=[...s.data].map((i,o)=>{const h=this._def.items[o]||this._def.rest;return h?h._parse(new T(s,i,s.path,o)):null}).filter(i=>!!i);return s.common.async?Promise.all(n).then(i=>k.mergeArray(t,i)):k.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new Z({...this._def,rest:e})}}Z.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Z({items:r,typeName:m.ZodTuple,rest:null,...y(e)})};class W extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==u.object)return c(s,{code:d.invalid_type,expected:u.object,received:s.parsedType}),p;const a=[],n=this._def.keyType,i=this._def.valueType;for(const o in s.data)a.push({key:n._parse(new T(s,o,s.path,o)),value:i._parse(new T(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?k.mergeObjectAsync(t,a):k.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return t instanceof _?new W({keyType:e,valueType:t,typeName:m.ZodRecord,...y(s)}):new W({keyType:C.create(),valueType:e,typeName:m.ZodRecord,...y(t)})}}class le extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==u.map)return c(s,{code:d.invalid_type,expected:u.map,received:s.parsedType}),p;const a=this._def.keyType,n=this._def.valueType,i=[...s.data.entries()].map(([o,h],f)=>({key:a._parse(new T(s,o,s.path,[f,"key"])),value:n._parse(new T(s,h,s.path,[f,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const h of i){const f=await h.key,v=await h.value;if(f.status==="aborted"||v.status==="aborted")return p;(f.status==="dirty"||v.status==="dirty")&&t.dirty(),o.set(f.value,v.value)}return{status:t.value,value:o}})}else{const o=new Map;for(const h of i){const f=h.key,v=h.value;if(f.status==="aborted"||v.status==="aborted")return p;(f.status==="dirty"||v.status==="dirty")&&t.dirty(),o.set(f.value,v.value)}return{status:t.value,value:o}}}}le.create=(r,e,t)=>new le({valueType:e,keyType:r,typeName:m.ZodMap,...y(t)});class D extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==u.set)return c(s,{code:d.invalid_type,expected:u.set,received:s.parsedType}),p;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(c(s,{code:d.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(c(s,{code:d.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const n=this._def.valueType;function i(h){const f=new Set;for(const v of h){if(v.status==="aborted")return p;v.status==="dirty"&&t.dirty(),f.add(v.value)}return{status:t.value,value:f}}const o=[...s.data.values()].map((h,f)=>n._parse(new T(s,h,s.path,f)));return s.common.async?Promise.all(o).then(h=>i(h)):i(o)}min(e,t){return new D({...this._def,minSize:{value:e,message:l.toString(t)}})}max(e,t){return new D({...this._def,maxSize:{value:e,message:l.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}D.create=(r,e)=>new D({valueType:r,minSize:null,maxSize:null,typeName:m.ZodSet,...y(e)});class he extends _{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}he.create=(r,e)=>new he({getter:r,typeName:m.ZodLazy,...y(e)});class fe extends _{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return c(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),p}return{status:"valid",value:e.data}}get value(){return this._def.value}}fe.create=(r,e)=>new fe({value:r,typeName:m.ZodLiteral,...y(e)});function ge(r,e){return new E({values:r,typeName:m.ZodEnum,...y(e)})}class E extends _{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return c(t,{expected:g.joinValues(s),received:t.parsedType,code:d.invalid_type}),p}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return c(t,{received:t.data,code:d.invalid_enum_value,options:s}),p}return b(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return E.create(e,{...this._def,...t})}exclude(e,t=this._def){return E.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}E.create=ge;class me extends _{_parse(e){const t=g.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==u.string&&s.parsedType!==u.number){const a=g.objectValues(t);return c(s,{expected:g.joinValues(a),received:s.parsedType,code:d.invalid_type}),p}if(this._cache||(this._cache=new Set(g.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const a=g.objectValues(t);return c(s,{received:s.data,code:d.invalid_enum_value,options:a}),p}return b(e.data)}get enum(){return this._def.values}}me.create=(r,e)=>new me({values:r,typeName:m.ZodNativeEnum,...y(e)});class q extends _{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==u.promise&&t.common.async===!1)return c(t,{code:d.invalid_type,expected:u.promise,received:t.parsedType}),p;const s=t.parsedType===u.promise?t.data:Promise.resolve(t.data);return b(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}q.create=(r,e)=>new q({type:r,typeName:m.ZodPromise,...y(e)});class $ extends _{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===m.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,n={addIssue:i=>{c(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(n.addIssue=n.addIssue.bind(n),a.type==="preprocess"){const i=a.transform(s.data,n);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return p;const h=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return h.status==="aborted"?p:h.status==="dirty"||t.value==="dirty"?L(h.value):h});{if(t.value==="aborted")return p;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?p:o.status==="dirty"||t.value==="dirty"?L(o.value):o}}if(a.type==="refinement"){const i=o=>{const h=a.refinement(o,n);if(s.common.async)return Promise.resolve(h);if(h instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?p:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?p:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(a.type==="transform")if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!j(i))return p;const o=a.transform(i.value,n);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>j(i)?Promise.resolve(a.transform(i.value,n)).then(o=>({status:t.value,value:o})):p);g.assertNever(a)}}$.create=(r,e,t)=>new $({schema:r,typeName:m.ZodEffects,effect:e,...y(t)});$.createWithPreprocess=(r,e,t)=>new $({schema:e,effect:{type:"preprocess",transform:r},typeName:m.ZodEffects,...y(t)});class N extends _{_parse(e){return this._getType(e)===u.undefined?b(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}N.create=(r,e)=>new N({innerType:r,typeName:m.ZodOptional,...y(e)});class V extends _{_parse(e){return this._getType(e)===u.null?b(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}V.create=(r,e)=>new V({innerType:r,typeName:m.ZodNullable,...y(e)});class X extends _{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===u.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}X.create=(r,e)=>new X({innerType:r,typeName:m.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...y(e)});class K extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return U(a)?a.then(n=>({status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new O(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new O(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}K.create=(r,e)=>new K({innerType:r,typeName:m.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...y(e)});class pe extends _{_parse(e){if(this._getType(e)!==u.nan){const s=this._getOrReturnCtx(e);return c(s,{code:d.invalid_type,expected:u.nan,received:s.parsedType}),p}return{status:"valid",value:e.data}}}pe.create=r=>new pe({typeName:m.ZodNaN,...y(r)});class We extends _{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class te extends _{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const n=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return n.status==="aborted"?p:n.status==="dirty"?(t.dirty(),L(n.value)):this._def.out._parseAsync({data:n.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?p:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new te({in:e,out:t,typeName:m.ZodPipeline})}}class ee extends _{_parse(e){const t=this._def.innerType._parse(e),s=a=>(j(a)&&(a.value=Object.freeze(a.value)),a);return U(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}ee.create=(r,e)=>new ee({innerType:r,typeName:m.ZodReadonly,...y(e)});var m;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(m||(m={}));const qe=C.create,Je=I.create,Ye=H.create,He=P.create,Ge=G.create;A.create;const Qe=w.create,Xe=x.create,Ke=B.create;F.create;Z.create;const et=W.create,tt=E.create;q.create;N.create;V.create;export{Ge as a,Ye as b,Qe as c,He as d,tt as e,Je as n,Xe as o,et as r,qe as s,Ke as u};
