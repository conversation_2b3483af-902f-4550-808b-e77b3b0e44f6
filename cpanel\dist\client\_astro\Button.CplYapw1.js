import{s as o}from"./index.DV5zenSI.js";import{_ as i,p as d}from"./utils.BWEB-mtU.js";import{d as l,g as s,i as m,r as b,o as u,w as c,j as y,k as x}from"./index.CpC-7sc3.js";import{r as v,b as g}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";const f=l({__name:"Button",setup(r,{expose:t}){t();const e={theme:v({root:`inline-flex cursor-pointer select-none items-center justify-center overflow-hidden relative
        px-3 py-2 gap-2 rounded-md disabled:pointer-events-none disabled:opacity-60 transition-colors duration-200
        bg-primary enabled:hover:bg-primary-emphasis enabled:active:bg-primary-emphasis-alt text-primary-contrast
        border border-primary enabled:hover:border-primary-emphasis enabled:active:border-primary-emphasis-alt
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
        p-vertical:flex-col p-fluid:w-full p-fluid:p-icon-only:w-10
        p-icon-only:w-10 p-icon-only:px-0 p-icon-only:gap-0
        p-icon-only:p-rounded:rounded-full p-icon-only:p-rounded:h-10
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-[1.125rem] p-large:px-[0.875rem] p-large:py-[0.625rem]
        p-raised:shadow-sm p-rounded:rounded-[2rem]
        p-outlined:bg-transparent enabled:hover:p-outlined:bg-primary-50 enabled:active:p-outlined:bg-primary-100
        p-outlined:border-primary-200 enabled:hover:p-outlined:border-primary-200 enabled:active:p-outlined:border-primary-200
        p-outlined:text-primary enabled:hover:p-outlined:text-primary enabled:active:p-outlined:text-primary
        dark:p-outlined:bg-transparent dark:enabled:hover:p-outlined:bg-primary/5 dark:enabled:active:p-outlined:bg-primary/15
        dark:p-outlined:border-primary-700 dark:enabled:hover:p-outlined:border-primary-700 dark:enabled:active:p-outlined:border-primary-700
        dark:p-outlined:text-primary dark:enabled:hover:p-outlined:text-primary dark:enabled:active:p-outlined:text-primary
        p-text:bg-transparent enabled:hover:p-text:bg-primary-50 enabled:active:p-text:bg-primary-100
        p-text:border-transparent enabled:hover:p-text:border-transparent enabled:active:p-text:border-transparent
        p-text:text-primary enabled:hover:p-text:text-primary enabled:active:p-text:text-primary
        dark:p-text:bg-transparent dark:enabled:hover:p-text:bg-primary/5 dark:enabled:active:p-text:bg-primary/15
        dark:p-text:border-transparent dark:enabled:hover:p-text:border-transparent dark:enabled:active:p-text:border-transparent
        dark:p-text:text-primary dark:enabled:hover:p-text:text-primary dark:enabled:active:p-text:text-primary
    `,loadingIcon:"animate-spin",icon:"p-right:order-1 p-bottom:order-2",label:`font-medium p-icon-only:invisible p-icon-only:w-0
        p-small:text-sm p-large:text-[1.125rem]`,pcBadge:{root:"min-w-4 h-4 leading-4 bg-primary-contrast rounded-full text-primary text-xs font-bold"}}),get Button(){return o},get ptViewMerge(){return d}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function h(r,t,p,e,k,_){return u(),s(e.Button,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},m({_:2},[b(r.$slots,(w,a)=>({name:a,fn:c(n=>[y(r.$slots,a,g(x(n??{})))])}))]),1032,["pt","ptOptions"])}const V=i(f,[["render",h]]);export{V as default};
