import v from"./Card.DllwvXut.js";import b from"./Button.CplYapw1.js";import{S as T}from"./Select.B5f7pqRM.js";import{V as A}from"./AutoComplete.WqgqstcD.js";import{D as w,s as k}from"./index.ClGz6GkZ.js";import{n as f}from"./router.DKcY2uv6.js";import{u as B}from"./useTrpc.CcBnDuWb.js";import{_ as D}from"./utils.BWEB-mtU.js";import{d as E,c as S,e as o,w as l,n as h,o as x,a as d}from"./index.CpC-7sc3.js";import{r as n}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CwY1vywt.js";import"./index.DqILEIKx.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./index.hiPlcmdl.js";import"./index.DUcQAuYR.js";import"./index.CyH7ziOX.js";import"./index.C2Xch34u.js";import"./SecondaryButton.ImrBLtmY.js";import"./trpc.CMxyjkwB.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";const O=E({__name:"TemplatesList",setup(C,{expose:t}){t();const r=B(),e=n({}),c=n(""),i=n([]),u=[{label:"Все",value:void 0},{label:"CATEGORY",value:"CATEGORY"},{label:"PART",value:"PART"},{label:"CATALOG_ITEM",value:"CATALOG_ITEM"}],m=n([]);async function s(){const a=await r.pageTemplates.list.query(e.value);m.value=a}function g(){f("/admin/templates/new")}function V(a){r.crud.partCategory.findMany.query({where:{name:{contains:a.query}},take:10}).then(y=>i.value=y)}function _(a){e.value.partCategoryId=a.value?.id,s()}h(s);const p={trpc:r,filters:e,categoryQuery:c,categorySuggestions:i,kindOptions:u,rows:m,load:s,onCreate:g,onCompleteCategory:V,onSelectCategory:_,VCard:v,VButton:b,VSelect:T,VAutoComplete:A,VDataTable:w,get VColumn(){return k},get navigate(){return f}};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}}),L={class:"w-full max-w-7xl"},F={class:"flex items-center justify-between"},G={class:"flex items-center gap-2"};function I(C,t,r,e,c,i){return x(),S("div",L,[o(e.VCard,null,{header:l(()=>[d("div",F,[t[2]||(t[2]=d("h2",{class:"text-lg font-semibold"},"Шаблоны страниц",-1)),d("div",G,[o(e.VSelect,{modelValue:e.filters.kind,"onUpdate:modelValue":t[0]||(t[0]=u=>e.filters.kind=u),options:e.kindOptions,placeholder:"Тип",optionLabel:"label",optionValue:"value",class:"w-44"},null,8,["modelValue"]),o(e.VAutoComplete,{modelValue:e.categoryQuery,"onUpdate:modelValue":t[1]||(t[1]=u=>e.categoryQuery=u),suggestions:e.categorySuggestions,optionLabel:"name",placeholder:"Категория",class:"w-72",onComplete:e.onCompleteCategory,onItemSelect:e.onSelectCategory},null,8,["modelValue","suggestions"]),o(e.VButton,{label:"Создать шаблон",icon:"",onClick:e.onCreate})])])]),content:l(()=>[o(e.VDataTable,{value:e.rows,dataKey:"id",paginator:!0,rows:20},{default:l(()=>[o(e.VColumn,{field:"name",header:"Название"}),o(e.VColumn,{field:"kind",header:"Тип"}),o(e.VColumn,{field:"isDefault",header:"Дефолт"}),o(e.VColumn,{field:"isActive",header:"Активен"}),o(e.VColumn,{header:"Действия"},{body:l(u=>[o(e.VButton,{label:"Редактировать",icon:"",onClick:m=>e.navigate(`/admin/templates/${u.data.id}`)},null,8,["onClick"])]),_:1})]),_:1},8,["value"])]),_:1})])}const fe=D(O,[["render",I]]);export{fe as default};
