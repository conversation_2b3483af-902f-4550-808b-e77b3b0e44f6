import{t as a}from"./trpc.CMxyjkwB.js";import{u as G}from"./useToast.Cyn6G0qw.js";import{r as M}from"./reactivity.esm-bundler.Bx7uHohy.js";import{h as b}from"./index.CpC-7sc3.js";const l=M([]),C=50,N=()=>{const c=G(),y=()=>`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,p=(s,r,i,u)=>({id:y(),code:s,message:r,details:i,field:u,timestamp:new Date}),d=s=>{l.value.unshift(s),l.value.length>C&&(l.value=l.value.slice(0,C))},I=(s,r=!0)=>{console.error("tRPC Error:",s);const i=s?.data?.code||s?.shape?.code||s?.name||"UNKNOWN_ERROR",u=s?.data?.zodError?.fieldErrors||s?.zodError?.fieldErrors;let o=s?.message;if(!o)switch(i){case"UNAUTHORIZED":o="Требуется авторизация";break;case"FORBIDDEN":o="Недостаточно прав для выполнения операции";break;case"NOT_FOUND":o="Ресурс не найден";break;case"BAD_REQUEST":o="Некорректный запрос";break;case"CONFLICT":o="Конфликт данных";break;case"PRECONDITION_FAILED":o="Нарушены условия выполнения операции";break;case"INTERNAL_SERVER_ERROR":o="Внутренняя ошибка сервера";break;case"TIMEOUT":o="Превышено время ожидания";break;default:o="Произошла ошибка при выполнении запроса"}if(u&&typeof u=="object"){const E=Object.entries(u).flatMap(([w,q])=>(Array.isArray(q)?q:[q]).filter(Boolean).map(O=>`${w}: ${O}`)).slice(0,5).join(`
`);E&&(o=`Ошибка валидации:
${E}`)}const m={...p(i,o,s),trpcCode:i,zodErrors:u};return d(m),r&&c.error("Ошибка",o),m},g=(s,r,i=!0)=>{console.error("Network Error:",s);const u=s?.status||s?.response?.status||0,o=s?.message||"Ошибка сети",m=u>=500||u===0||u===408||u===429,E={...p("NETWORK_ERROR",o,s),status:u,retryable:m,url:r};return d(E),i&&(u===0?c.error("Ошибка сети","Проверьте подключение к интернету"):u>=500?c.error("Ошибка сервера","Попробуйте повторить запрос позже"):c.error("Ошибка сети",o)),E},t=(s,r,i,u=!0)=>{const o=`Поле "${s}": ${i}`,m={...p("VALIDATION_ERROR",o,{value:r,constraint:i},s),field:s,value:r,constraint:i};return d(m),u&&c.warn("Ошибка валидации",o),m},e=(s,r,i=!0)=>{console.error("Generic Error:",s);const u=s?.message||"Произошла неизвестная ошибка",o=s?.code||s?.name||"GENERIC_ERROR",m=p(o,r?`${r}: ${u}`:u,s);return d(m),i&&c.error("Ошибка",m.message),m},n=(s,r={})=>{const{context:i,showToast:u=!0,url:o}=r;return s?.data?.code||s?.shape?.code?I(s,u):s?.status||s?.response?.status?g(s,o,u):e(s,i,u)},f=()=>{l.value=[]},R=s=>{const r=l.value.findIndex(i=>i.id===s);r!==-1&&l.value.splice(r,1)},h=s=>l.value.filter(r=>r.code===s),T=(s=10)=>l.value.slice(0,s),v=b(()=>{const s=["INTERNAL_SERVER_ERROR","UNAUTHORIZED","FORBIDDEN"];return l.value.some(r=>s.includes(r.code))}),A=b(()=>{const s={};return l.value.forEach(r=>{s[r.code]=(s[r.code]||0)+1}),s}),P=(s="Запись",r)=>{r?n(r,{context:`Сохранение ${s.toLowerCase()}`}):c.error("Ошибка сохранения",`Не удалось сохранить ${s.toLowerCase()}`)},x=(s="Запись",r)=>{r?n(r,{context:`Удаление ${s.toLowerCase()}`}):c.error("Ошибка удаления",`Не удалось удалить ${s.toLowerCase()}`)},S=(s="Данные",r)=>{r?n(r,{context:`Загрузка ${s.toLowerCase()}`}):c.error("Ошибка загрузки",`Не удалось загрузить ${s.toLowerCase()}`)};return{errors:b(()=>l.value),hasCriticalErrors:v,errorStats:A,handleError:n,handleTRPCError:I,handleNetworkError:g,handleValidationError:t,handleGenericError:e,clearErrors:f,removeError:R,getErrorsByType:h,getRecentErrors:T,showSaveError:P,showDeleteError:x,showLoadError:S}};function U(){const c=M(!1),y=M(null),p=N(),d=e=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("app:toast",{detail:e}))},I=e=>{const n=p.handleTRPCError(e,!1);y.value=n.message,d({severity:"error",summary:"Ошибка",detail:n.message})},g=()=>{y.value=null},t=async(e,n)=>{try{c.value=!0,g();const f=await e();return n?.success&&d({severity:"success",summary:n.success.title??"Успешно",detail:n.success.message}),f}catch(f){return I(f),null}finally{c.value=!1}};return{loading:b(()=>c.value),error:b(()=>y.value),clearError:g,execute:t,client:a,parts:{findMany:e=>t(()=>a.crud.part.findMany.query(e)),findUnique:e=>t(()=>a.crud.part.findUnique.query(e)),create:e=>t(()=>a.crud.part.create.mutate(e),{success:{title:"Сохранено",message:"Запчасть создана"}}),update:e=>t(()=>a.crud.part.update.mutate(e),{success:{title:"Сохранено",message:"Запчасть обновлена"}}),delete:e=>t(()=>a.crud.part.delete.mutate(e),{success:{title:"Удалено",message:"Запчасть удалена"}})},catalogItems:{findMany:e=>t(()=>a.crud.catalogItem.findMany.query(e)),findUnique:e=>t(()=>a.crud.catalogItem.findUnique.query(e)),create:e=>t(()=>a.crud.catalogItem.create.mutate(e),{success:{title:"Сохранено",message:"Позиция создана"}}),update:e=>t(()=>a.crud.catalogItem.update.mutate(e),{success:{title:"Сохранено",message:"Позиция обновлена"}}),delete:e=>t(()=>a.crud.catalogItem.delete.mutate(e),{success:{title:"Удалено",message:"Позиция удалена"}})},matching:{findMatchingParts:e=>t(()=>a.matching.findMatchingParts.query(e)),findMatchingCatalogItems:e=>t(()=>a.matching.findMatchingCatalogItems.query(e)),proposeLink:e=>t(()=>a.matching.proposeLink.mutate(e)),listProposals:e=>t(()=>a.matching.listProposals.query(e)),approveProposal:e=>t(()=>a.matching.approveProposal.mutate(e)),rejectProposal:e=>t(()=>a.matching.rejectProposal.mutate(e)),generateProposals:e=>t(()=>a.matching.generateProposals.mutate(e)),createPartFromItems:e=>t(()=>a.matching.createPartFromItems.mutate(e))},brands:{findMany:e=>t(()=>a.crud.brand.findMany.query(e)),create:e=>t(()=>a.crud.brand.create.mutate(e),{success:{title:"Сохранено",message:"Бренд создан"}}),update:e=>t(()=>a.crud.brand.update.mutate(e),{success:{title:"Сохранено",message:"Бренд обновлён"}}),delete:e=>t(()=>a.crud.brand.delete.mutate(e),{success:{title:"Удалено",message:"Бренд удалён"}})},partCategories:{findMany:e=>t(()=>a.crud.partCategory.findMany.query(e)),create:e=>t(()=>a.crud.partCategory.create.mutate(e),{success:{title:"Сохранено",message:"Категория создана"}}),update:e=>t(()=>a.crud.partCategory.update.mutate(e),{success:{title:"Сохранено",message:"Категория обновлена"}}),delete:e=>t(()=>a.crud.partCategory.delete.mutate(e),{success:{title:"Удалено",message:"Категория удалена"}})},media:{uploadPartImage:e=>t(()=>a.upload.uploadPartImage.mutate(e),{success:{title:"Загружено",message:"Изображение запчасти обновлено"}}),deletePartImage:e=>t(()=>a.upload.deletePartImage.mutate(e),{success:{title:"Удалено",message:"Изображение запчасти удалено"}}),uploadPartMedia:e=>t(()=>a.upload.uploadPartMedia.mutate(e),{success:{title:"Загружено",message:"Медиа добавлено в галерею запчасти"}}),removePartMedia:e=>t(()=>a.upload.removePartMedia.mutate(e),{success:{title:"Удалено",message:"Медиа удалено из галереи запчасти"}}),uploadPartCategoryImage:e=>t(()=>a.upload.uploadPartCategoryImage.mutate(e),{success:{title:"Загружено",message:"Изображение категории обновлено"}}),deletePartCategoryImage:e=>t(()=>a.upload.deletePartCategoryImage.mutate(e),{success:{title:"Удалено",message:"Изображение категории удалено"}}),uploadCatalogItemImage:e=>t(()=>a.upload.uploadCatalogItemImage.mutate(e),{success:{title:"Загружено",message:"Изображение позиции обновлено"}}),deleteCatalogItemImage:e=>t(()=>a.upload.deleteCatalogItemImage.mutate(e),{success:{title:"Удалено",message:"Изображение позиции удалено"}}),uploadCatalogItemMedia:e=>t(()=>a.upload.uploadCatalogItemMedia.mutate(e),{success:{title:"Загружено",message:"Медиа добавлено в галерею позиции"}}),removeCatalogItemMedia:e=>t(()=>a.upload.removeCatalogItemMedia.mutate(e),{success:{title:"Удалено",message:"Медиа удалено из галереи позиции"}})},equipmentModels:{findMany:e=>t(()=>a.crud.equipmentModel.findMany.query(e)),findUnique:e=>t(()=>a.crud.equipmentModel.findUnique.query(e)),create:e=>t(()=>a.crud.equipmentModel.create.mutate(e)),update:e=>t(()=>a.crud.equipmentModel.update.mutate(e)),delete:e=>t(()=>a.crud.equipmentModel.delete.mutate(e))},partAttributes:{findByPartId:e=>t(()=>a.partAttributes.findByPartId.query(e)),create:e=>t(()=>a.partAttributes.create.mutate(e),{success:{title:"Сохранено",message:"Атрибут добавлен"}}),update:e=>t(()=>a.partAttributes.update.mutate(e),{success:{title:"Сохранено",message:"Атрибут обновлён"}}),delete:e=>t(()=>a.partAttributes.delete.mutate(e),{success:{title:"Удалено",message:"Атрибут удалён"}}),bulkCreate:e=>t(()=>a.partAttributes.bulkCreate.mutate(e))},attributeTemplates:{findMany:e=>t(()=>a.attributeTemplates.findMany.query(e)),findById:e=>t(()=>a.attributeTemplates.findById.query(e)),create:e=>t(()=>a.attributeTemplates.create.mutate(e),{success:{title:"Сохранено",message:"Шаблон создан"}}),update:e=>t(()=>a.attributeTemplates.update.mutate(e),{success:{title:"Сохранено",message:"Шаблон обновлён"}}),delete:e=>t(()=>a.attributeTemplates.delete.mutate(e),{success:{title:"Удалено",message:"Шаблон удалён"}}),forceDelete:e=>t(()=>a.attributeTemplates.forceDelete.mutate(e),{success:{title:"Удалено",message:"Шаблон принудительно удалён со всеми связями"}}),findAllGroups:()=>t(()=>a.attributeTemplates.findAllGroups.query({})),findGroupsHierarchy:()=>t(()=>a.attributeTemplates.findGroupsHierarchy.query({})),createGroup:e=>t(()=>a.attributeTemplates.createGroup.mutate(e),{success:{title:"Сохранено",message:"Группа создана"}}),updateGroup:e=>t(()=>a.attributeTemplates.updateGroup.mutate(e),{success:{title:"Сохранено",message:"Группа обновлена"}}),deleteGroup:e=>t(()=>a.attributeTemplates.deleteGroup.mutate(e),{success:{title:"Удалено",message:"Группа удалена"}})},attributeSynonyms:{groups:{findMany:e=>t(()=>a.attributeSynonyms.groups.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.groups.create.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов создана"}}),update:e=>t(()=>a.attributeSynonyms.groups.update.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов обновлена"}}),delete:e=>t(()=>a.attributeSynonyms.groups.delete.mutate(e),{success:{title:"Удалено",message:"Группа синонимов удалена"}})},synonyms:{findMany:e=>t(()=>a.attributeSynonyms.synonyms.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.synonyms.create.mutate(e),{success:{title:"Сохранено",message:"Синоним добавлен"}}),update:e=>t(()=>a.attributeSynonyms.synonyms.update.mutate(e),{success:{title:"Сохранено",message:"Синоним обновлён"}}),delete:e=>t(()=>a.attributeSynonyms.synonyms.delete.mutate(e),{success:{title:"Удалено",message:"Синоним удалён"}})},utils:{findGroupByValue:e=>t(()=>a.attributeSynonyms.utils.findGroupByValue.query(e)),findGroupSynonymsByValue:e=>t(()=>a.attributeSynonyms.utils.findGroupSynonymsByValue.query(e))}},partApplicability:{upsert:e=>t(()=>a.crud.partApplicability.upsert.mutate(e),{success:{title:"Сохранено",message:"Применимость обновлена"}}),findMany:e=>t(()=>a.crud.partApplicability.findMany.query(e)),create:e=>t(()=>a.crud.partApplicability.create.mutate(e)),update:e=>t(()=>a.crud.partApplicability.update.mutate(e)),findFirst:e=>t(()=>a.crud.partApplicability.findFirst.query(e)),delete:e=>t(()=>a.crud.partApplicability.delete.mutate(e),{success:{title:"Удалено",message:"Связь с группой удалена"}})},equipmentApplicability:{upsert:e=>t(()=>a.crud.equipmentApplicability.upsert.mutate(e)),findMany:e=>t(()=>a.crud.equipmentApplicability.findMany.query(e)),create:e=>t(()=>a.crud.equipmentApplicability.create.mutate(e),{success:{title:"Сохранено",message:"Применимость техники добавлена"}}),update:e=>t(()=>a.crud.equipmentApplicability.update.mutate(e),{success:{title:"Сохранено",message:"Применимость техники обновлена"}}),findFirst:e=>t(()=>a.crud.equipmentApplicability.findFirst.query(e))},importExport:{exportXlsx:e=>t(()=>a.importExport.exportXlsx.mutate(e)),exportTemplate:e=>t(()=>a.importExport.exportTemplate.mutate(e)),simplifiedImport:e=>t(()=>a.importExport.simplifiedImport.mutate(e),{success:{title:"Успешно",message:"Упрощенный импорт выполнен"}}),createSimplifiedTemplate:e=>t(()=>a.importExport.createSimplifiedTemplate.mutate(e)),validateSimplifiedImport:e=>t(()=>a.importExport.validateSimplifiedImport.mutate(e))},excelImport:{dryRun:e=>t(()=>a.import.dryRun.mutate(e)),execute:e=>t(()=>a.import.execute.mutate(e))}}}export{U as u};
