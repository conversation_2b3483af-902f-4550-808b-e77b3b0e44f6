<template>
  <div class="w-full max-w-7xl">
    <VCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">Шаблоны страниц</h2>
          <div class="flex items-center gap-2">
            <VSelect
              v-model="filters.kind"
              :options="kindOptions"
              placeholder="Тип"
              optionLabel="label"
              optionValue="value"
              class="w-44"
            />
            <VAutoComplete
              v-model="categoryQuery"
              :suggestions="categorySuggestions"
              optionLabel="name"
              placeholder="Категория"
              class="w-72"
              @complete="onCompleteCategory"
              @item-select="onSelectCategory"
            />
            <VButton label="Создать шаблон" icon="" @click="onCreate" />
          </div>
        </div>
      </template>
      <template #content>
        <VDataTable :value="rows" dataKey="id" :paginator="true" :rows="20">
          <VColumn field="name" header="Название" />
          <VColumn field="kind" header="Тип" />
          <VColumn field="isDefault" header="Дефолт" />
          <VColumn field="isActive" header="Активен" />
          <VColumn header="Действия" >
            <template #body="slotProps">
              <VButton
                label="Редактировать"
                icon=""
                @click="navigate(`/admin/templates/${slotProps.data.id}`)"
              />
            </template>
          </VColumn>
        </VDataTable>
      </template>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VSelect from "@/volt/Select.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VDataTable from "@/volt/DataTable.vue";
import VColumn from "primevue/column";
import { navigate } from "astro:transitions/client";
import { useTrpc } from "@/composables/useTrpc";

const trpc = useTrpc();

const filters = ref<{
  kind?: "CATEGORY" | "PART" | "CATALOG_ITEM";
  partCategoryId?: number;
}>({});
const categoryQuery = ref("");
const categorySuggestions = ref<any[]>([]);

const kindOptions = [
  { label: "Все", value: undefined },
  { label: "CATEGORY", value: "CATEGORY" },
  { label: "PART", value: "PART" },
  { label: "CATALOG_ITEM", value: "CATALOG_ITEM" },
];

const rows = ref<any[]>([]);

async function load() {
  try {
    const data = await (trpc.client.pageTemplates.list as any).query(filters.value);
    rows.value = data || [];
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error);
    rows.value = [];
  }
}

function onCreate() {
  navigate("/admin/templates/new");
}

function onCompleteCategory(e: any) {
  // простая подгрузка категорий
  (trpc.client.crud.partCategory.findMany as any)
    .query({ where: { name: { contains: e.query } }, take: 10 })
    .then((res: any) => (categorySuggestions.value = res || []))
    .catch((error: any) => {
      console.error('Ошибка загрузки категорий:', error);
      categorySuggestions.value = [];
    });
}
function onSelectCategory(e: any) {
  filters.value.partCategoryId = e.value?.id;
  load();
}


onMounted(load);
</script>
