import{u as i}from"./runtime-dom.esm-bundler.0NVQG2L5.js";/* empty css                            */import{_ as l}from"./utils.BWEB-mtU.js";import{d as p,c as F,o as m,m as _,h as r}from"./index.CpC-7sc3.js";const f=p({__name:"Icon",props:{name:{},size:{default:16},color:{},class:{}},setup(t,{expose:s}){s(),i(C=>({"45b0056e":c.value}));const e=t,o={plus:"➕",edit:"✏️",trash:"🗑️",search:"🔍",filter:"🔽",check:"✅",x:"❌","chevron-up":"⬆️","chevron-down":"⬇️",folder:"📁",ruler:"📏",tag:"🏷️",template:"📋",info:"ℹ️",target:"🎯",grid:"⊞",list:"☰"},n=r(()=>"inline-block"),u=r(()=>({fontSize:typeof e.size=="number"?`${e.size}px`:e.size,color:e.color})),c=r(()=>o[e.name]||"?"),a={props:e,iconMap:o,iconClass:n,iconStyle:u,iconContent:c};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function d(t,s,e,o,n,u){return m(),F("i",_({class:o.iconClass,style:o.iconStyle},t.$attrs),null,16)}const v=l(f,[["render",d],["__scopeId","data-v-1ba1abfe"]]);export{v as I};
