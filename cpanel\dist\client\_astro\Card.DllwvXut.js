import{s as b,_ as g,p as y}from"./utils.BWEB-mtU.js";import{B as $,c as o,o as a,b as n,a as p,m as t,j as s,d as h,g as v,i as k,r as C,w,k as x}from"./index.CpC-7sc3.js";import{r as B,b as S}from"./reactivity.esm-bundler.Bx7uHohy.js";var P=`
    .p-card {
        background: dt('card.background');
        color: dt('card.color');
        box-shadow: dt('card.shadow');
        border-radius: dt('card.border.radius');
        display: flex;
        flex-direction: column;
    }

    .p-card-caption {
        display: flex;
        flex-direction: column;
        gap: dt('card.caption.gap');
    }

    .p-card-body {
        padding: dt('card.body.padding');
        display: flex;
        flex-direction: column;
        gap: dt('card.body.gap');
    }

    .p-card-title {
        font-size: dt('card.title.font.size');
        font-weight: dt('card.title.font.weight');
    }

    .p-card-subtitle {
        color: dt('card.subtitle.color');
    }
`,V={root:"p-card p-component",header:"p-card-header",body:"p-card-body",caption:"p-card-caption",title:"p-card-title",subtitle:"p-card-subtitle",content:"p-card-content",footer:"p-card-footer"},z=$.extend({name:"card",style:P,classes:V}),M={name:"BaseCard",extends:b,style:z,provide:function(){return{$pcCard:this,$parentInstance:this}}},c={name:"Card",extends:M,inheritAttrs:!1};function O(e,d,l,r,u,f){return a(),o("div",t({class:e.cx("root")},e.ptmi("root")),[e.$slots.header?(a(),o("div",t({key:0,class:e.cx("header")},e.ptm("header")),[s(e.$slots,"header")],16)):n("",!0),p("div",t({class:e.cx("body")},e.ptm("body")),[e.$slots.title||e.$slots.subtitle?(a(),o("div",t({key:0,class:e.cx("caption")},e.ptm("caption")),[e.$slots.title?(a(),o("div",t({key:0,class:e.cx("title")},e.ptm("title")),[s(e.$slots,"title")],16)):n("",!0),e.$slots.subtitle?(a(),o("div",t({key:1,class:e.cx("subtitle")},e.ptm("subtitle")),[s(e.$slots,"subtitle")],16)):n("",!0)],16)):n("",!0),p("div",t({class:e.cx("content")},e.ptm("content")),[s(e.$slots,"content")],16),e.$slots.footer?(a(),o("div",t({key:1,class:e.cx("footer")},e.ptm("footer")),[s(e.$slots,"footer")],16)):n("",!0)],16)],16)}c.render=O;const j=h({__name:"Card",setup(e,{expose:d}){d();const r={theme:B({root:`flex flex-col rounded-xl
        bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0
        shadow-md`,header:"",body:"p-5 flex flex-col gap-2",caption:"flex flex-col gap-2",title:"font-medium text-xl",subtitle:"text-surface-500 dark:text-surface-400",content:"",footer:""}),get Card(){return c},get ptViewMerge(){return y}};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}});function A(e,d,l,r,u,f){return a(),v(r.Card,{unstyled:"",pt:r.theme,ptOptions:{mergeProps:r.ptViewMerge}},k({_:2},[C(e.$slots,(E,i)=>({name:i,fn:w(m=>[s(e.$slots,i,S(x(m??{})))])}))]),1032,["pt","ptOptions"])}const R=g(j,[["render",A]]);export{R as default};
