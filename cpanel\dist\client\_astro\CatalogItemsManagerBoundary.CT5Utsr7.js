import{E as j4}from"./ErrorBoundary.aYPzjr8z.js";import{u as y4}from"./useTrpc.CcBnDuWb.js";import{u as q4}from"./useUrlParams.ByBmVuCY.js";import{T as H4,u as G4,C as W4}from"./ConfirmDialog.Cn649_Oc.js";import{u as D4}from"./useToast.Cyn6G0qw.js";import o4 from"./Card.DllwvXut.js";import{I as x4}from"./InputText.CPqCR4in.js";import{V as k4}from"./AutoComplete.WqgqstcD.js";import l4 from"./Button.CplYapw1.js";import{D as b4}from"./Dialog.DjvB895c.js";import{M as Q4}from"./MultiSelect.fjeRlUGk.js";import K4 from"./Toast.BFjVikSW.js";import{D as X4,s as J4}from"./index.ClGz6GkZ.js";import{T as C4}from"./Tag.BtN2Bjhy.js";import{s as Y4,_ as $}from"./utils.BWEB-mtU.js";import{B as Z4,c as i,o as l,m as v4,a as t,d as u4,p as i4,g as E,w as o,e as s,b as _,F as Y,r as Z,q as r4,f as U,h as g4,l as F4,n as B4}from"./index.CpC-7sc3.js";import{I as I4}from"./Icon.DGPcirKX.js";import{L as E4}from"./link.DoOSpg3_.js";import{T as $4,a as w4,P as A4}from"./trash.D7SMYTt1.js";import{c as V4}from"./createLucideIcon.CxvjiKko.js";import{t as f,n as p4,r as g,a as uu}from"./reactivity.esm-bundler.Bx7uHohy.js";import{w as eu}from"./runtime-dom.esm-bundler.0NVQG2L5.js";import{V as T4}from"./Textarea.C8hcWg9_.js";import{V as tu}from"./Checkbox.Czip7_Ii.js";import{Q as au,A as su,F as ru}from"./QuickCreateBrand.ZBe-Tlwy.js";import{f as h4,r as lu}from"./utils.D8YPi1lO.js";import{S as nu}from"./Select.B5f7pqRM.js";/* empty css                            */import{M as ou,u as iu}from"./MatchingDetailsGrid.5afoSyQe.js";import{M as du,a as cu,R as S4}from"./MatchingLoadingState.CWg9RiuK.js";import{S as mu}from"./search.ChgJir38.js";import"./trpc.CMxyjkwB.js";import"./router.DKcY2uv6.js";import"./schemas.BR5-L2eu.js";import"./index.enLFHxHc.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./SecondaryButton.ImrBLtmY.js";import"./DangerButton.DyBZF5lv.js";import"./index.2frgj6Y9.js";import"./index.DqILEIKx.js";import"./index.DBjPSdxz.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CwY1vywt.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./index.C2Xch34u.js";import"./index.CyH7ziOX.js";import"./index.BtgaEm74.js";import"./index.hiPlcmdl.js";import"./index.DUcQAuYR.js";import"./AttributeValueInput.CrEMZkDz.js";import"./InputNumber.8Wyucp72.js";import"./index.B3SmZZpj.js";import"./Message.DXe4eGzY.js";import"./ToggleSwitch.DRF-wltF.js";import"./info.D2H3qZLp.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=V4("scan-eye",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0",key:"11ak4c"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vu=V4("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var gu=`
    .p-progressspinner {
        position: relative;
        margin: 0 auto;
        width: 100px;
        height: 100px;
        display: inline-block;
    }

    .p-progressspinner::before {
        content: '';
        display: block;
        padding-top: 100%;
    }

    .p-progressspinner-spin {
        height: 100%;
        transform-origin: center center;
        width: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        animation: p-progressspinner-rotate 2s linear infinite;
    }

    .p-progressspinner-circle {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: 0;
        stroke: dt('progressspinner.colorOne');
        animation:
            p-progressspinner-dash 1.5s ease-in-out infinite,
            p-progressspinner-color 6s ease-in-out infinite;
        stroke-linecap: round;
    }

    @keyframes p-progressspinner-rotate {
        100% {
            transform: rotate(360deg);
        }
    }
    @keyframes p-progressspinner-dash {
        0% {
            stroke-dasharray: 1, 200;
            stroke-dashoffset: 0;
        }
        50% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -35px;
        }
        100% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -124px;
        }
    }
    @keyframes p-progressspinner-color {
        100%,
        0% {
            stroke: dt('progressspinner.color.one');
        }
        40% {
            stroke: dt('progressspinner.color.two');
        }
        66% {
            stroke: dt('progressspinner.color.three');
        }
        80%,
        90% {
            stroke: dt('progressspinner.color.four');
        }
    }
`,yu={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},bu=Z4.extend({name:"progressspinner",style:gu,classes:yu}),Cu={name:"BaseProgressSpinner",extends:Y4,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:bu,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},M4={name:"ProgressSpinner",extends:Cu,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},Eu=["fill","stroke-width"];function _u(m,e,r,u,p,v){return l(),i("div",v4({class:m.cx("root"),role:"progressbar"},m.ptmi("root")),[(l(),i("svg",v4({class:m.cx("spin"),viewBox:"25 25 50 50",style:v.svgStyle},m.ptm("spin")),[t("circle",v4({class:m.cx("circle"),cx:"50",cy:"50",r:"20",fill:m.fill,"stroke-width":m.strokeWidth,strokeMiterlimit:"10"},m.ptm("circle")),null,16,Eu)],16))],16)}M4.render=_u;const pu=u4({__name:"CatalogItemsTable",props:{items:{},loading:{type:Boolean},totalRecords:{},rows:{},first:{}},emits:["page","sort","edit","delete","view-details","match"],setup(m,{expose:e}){e();const u={formatDate:p=>{if(!p)return"";const v=new Date(p);return new Intl.DateTimeFormat("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(v)},VCard:o4,VDataTable:X4,get VColumn(){return J4},VButton:l4,VTag:C4,get VProgressSpinner(){return M4},get PencilIcon(){return w4},get ScanEyeIcon(){return fu},get TrashIcon(){return $4},Icon:I4,get LinkIcon(){return E4}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}}),hu={class:"flex items-center gap-2"},Du={class:"font-mono font-medium"},xu={class:"flex items-center gap-2"},ku={class:"font-medium"},Fu={class:"max-w-xs"},Bu=["title"],Iu={key:1,class:"text-surface-400 italic"},wu={class:"flex flex-wrap gap-1"},Au={class:"text-center"},Vu={key:1,class:"text-surface-400 text-sm"},Tu={key:0,class:"text-surface-600 dark:text-surface-400 text-sm"},Su={key:1,class:"text-surface-400 italic"},Mu={class:"text-surface-600 dark:text-surface-400 font-mono text-sm"},Pu={class:"flex gap-1"},Lu={class:"py-8 text-center"},Ru={class:"py-8 text-center"};function Uu(m,e,r,u,p,v){const d=i4("tooltip");return l(),E(u.VCard,null,{content:o(()=>[s(u.VDataTable,{value:r.items,loading:r.loading,paginator:!0,rows:r.rows,"total-records":r.totalRecords,first:r.first,lazy:!0,sortable:!0,"paginator-template":"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown","current-page-report-template":"Показано {first} - {last} из {totalRecords} записей","rows-per-page-options":[10,20,50],onPage:e[0]||(e[0]=a=>m.$emit("page",a)),onSort:e[1]||(e[1]=a=>m.$emit("sort",a))},{empty:o(()=>[t("div",Lu,[s(u.Icon,{name:"pi pi-inbox",class:"text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"}),e[2]||(e[2]=t("p",{class:"text-surface-500 dark:text-surface-400 mb-2 text-lg"},"Каталожные позиции не найдены",-1)),e[3]||(e[3]=t("p",{class:"text-surface-400 dark:text-surface-500 text-sm"},"Попробуйте изменить параметры поиска или создайте новую позицию",-1))])]),loading:o(()=>[t("div",Ru,[s(u.VProgressSpinner,{size:"50"}),e[4]||(e[4]=t("p",{class:"text-surface-500 dark:text-surface-400 mt-4"},"Загрузка каталожных позиций...",-1))])]),default:o(()=>[s(u.VColumn,{field:"sku",header:"Артикул",sortable:!0,class:"min-w-32"},{body:o(({data:a})=>[t("div",hu,[t("span",Du,f(a.sku),1),a.isPublic?_("",!0):(l(),E(u.VTag,{key:0,value:"Приватная",severity:"warning",size:"small"}))])]),_:1}),s(u.VColumn,{field:"brand.name",header:"Бренд",sortable:!0,class:"min-w-32"},{body:o(({data:a})=>[t("div",xu,[t("span",ku,f(a.brand?.name||"Не указан"),1),a.brand?.isOem?(l(),E(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):_("",!0)])]),_:1}),s(u.VColumn,{field:"description",header:"Описание",class:"min-w-48"},{body:o(({data:a})=>[t("div",Fu,[a.description?(l(),i("p",{key:0,class:"text-surface-600 dark:text-surface-400 truncate text-sm",title:a.description},f(a.description),9,Bu)):(l(),i("span",Iu,"Нет описания"))])]),_:1}),s(u.VColumn,{header:"Атрибуты",class:"min-w-32"},{body:o(({data:a})=>[t("div",wu,[(l(!0),i(Y,null,Z(a.attributes?.slice(0,3),y=>(l(),E(u.VTag,{key:y.id,value:`${y.template?.title}: ${y.value}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"]))),128)),a.attributes?.length>3?(l(),E(u.VTag,{key:0,value:`+${a.attributes.length-3}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"])):_("",!0)])]),_:1}),s(u.VColumn,{header:"Применимость",class:"min-w-24"},{body:o(({data:a})=>[t("div",Au,[a.applicabilities?.length>0?(l(),E(u.VTag,{key:0,value:`${a.applicabilities.length} групп`,severity:"success",size:"small"},null,8,["value"])):(l(),i("span",Vu,"Не назначена"))])]),_:1}),s(u.VColumn,{field:"source",header:"Источник",class:"min-w-32"},{body:o(({data:a})=>[a.source?(l(),i("span",Tu,f(a.source),1)):(l(),i("span",Su,"Не указан"))]),_:1}),s(u.VColumn,{field:"id",header:"ID",sortable:!0,class:"min-w-20"},{body:o(({data:a})=>[t("span",Mu," #"+f(a.id),1)]),_:1}),s(u.VColumn,{header:"Действия",class:"min-w-32"},{body:o(({data:a})=>[t("div",Pu,[r4((l(),E(u.VButton,{onClick:y=>m.$emit("match",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.LinkIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[d,"Найти группу взаимозаменяемости"]]),s(u.VButton,{onClick:y=>m.$emit("view-details",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.ScanEyeIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:y=>m.$emit("edit",a),severity:"secondary",size:"small",text:""},{default:o(()=>[s(u.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:y=>m.$emit("delete",a),severity:"danger",size:"small",text:""},{default:o(()=>[s(u.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","first"])]),_:1})}const zu=$(pu,[["render",Uu]]),Ou=u4({__name:"CatalogItemForm",props:{item:{}},emits:["save","cancel"],setup(m,{expose:e,emit:r}){e();const u=m,p=r,v=g({sku:"",selectedBrand:null,description:"",source:"",isPublic:!0,attributes:[]}),d=g({sku:"",brandId:""}),a=g(!1),y=g(!1),S=g([]),k=()=>(d.value.sku="",v.value.sku.trim()?v.value.sku.length<2?(d.value.sku="Артикул должен содержать минимум 2 символа",!1):v.value.sku.length>64?(d.value.sku="Артикул не может быть длиннее 64 символов",!1):!0:(d.value.sku="Артикул обязателен",!1)),L=()=>(d.value.brandId="",v.value.selectedBrand?!0:(d.value.brandId="Бренд обязателен",!1)),W=g4(()=>v.value.sku.trim()&&v.value.selectedBrand&&!d.value.sku&&!d.value.brandId),h=g(u.item||null),M=g4(()=>h.value?.image?.url||null),D=g(null),B=g(!1),I=g(null),j=g(!1),{media:C,brands:x,loading:q,client:F}=y4(),w=b=>{const A=b.target;D.value=A.files&&A.files[0]||null},z=async()=>{if(!(!h.value?.id||!D.value)){B.value=!0;try{const b=await h4(D.value);await C.uploadCatalogItemImage({catalogItemId:h.value.id,fileName:D.value.name,fileData:b,mimeType:D.value.type||"image/png"}),await Q(),D.value=null}finally{B.value=!1}}},K=async()=>{h.value?.id&&(await C.deleteCatalogItemImage({catalogItemId:h.value.id}),await Q())},X=b=>{const A=b.target;I.value=A.files||null},e4=async()=>{if(!(!h.value?.id||!I.value||I.value.length===0)){j.value=!0;try{for(const b of Array.from(I.value)){const A=await h4(b);await C.uploadCatalogItemMedia({catalogItemId:h.value.id,fileName:b.name,fileData:A,mimeType:b.type||"application/octet-stream"})}await Q(),I.value=null}finally{j.value=!1}}},t4=async b=>{h.value?.id&&(await C.removeCatalogItemMedia({catalogItemId:h.value.id,mediaId:b}),await Q())};async function Q(){if(!h.value?.id)return;const b=await F.crud.catalogItem.findUnique.query({where:{id:h.value.id},include:{image:!0,mediaAssets:!0,brand:!0}});h.value=b}const O=async b=>{try{const A=b.query.toLowerCase(),G=await x.findMany({where:{name:{contains:A,mode:"insensitive"}},take:10});G&&(S.value=G)}catch(A){console.error("Ошибка поиска брендов:",A)}},H=b=>{v.value.selectedBrand=b,S.value=[b,...S.value]},V=b=>{v.value.attributes.splice(b,1)},d4=b=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[b]||b,J=()=>{const b=k(),A=L();if(!b||!A)return;const G={sku:v.value.sku.toUpperCase().trim(),brandId:v.value.selectedBrand.id,description:v.value.description.trim()||void 0,source:v.value.source.trim()||void 0,isPublic:v.value.isPublic},s4=v.value.attributes?v.value.attributes.filter(T=>{const c4=T.value&&String(T.value).trim()!=="",m4=T.templateId||T.template?.id;return c4&&m4}):[];s4.length>0?u.item?G.attributes={deleteMany:{},create:s4.map(T=>({templateId:T.templateId||T.template?.id,value:T.value}))}:G.attributes={create:s4.map(T=>({templateId:T.templateId||T.template?.id,value:T.value}))}:u.item&&(G.attributes={deleteMany:{}}),p("save",G)},a4=()=>{u.item&&(v.value={sku:u.item.sku||"",selectedBrand:u.item.brand||null,description:u.item.description||"",source:u.item.source||"",isPublic:u.item.isPublic??!0,attributes:u.item.attributes||[]})};F4(()=>u.item,a4,{immediate:!0}),B4(()=>{a4()});const n4={props:u,emit:p,formData:v,errors:d,showAttributeManager:a,showCreateBrand:y,brandSuggestions:S,validateSku:k,validateBrand:L,isFormValid:W,currentItem:h,itemImageUrl:M,selectedImage:D,uploadingImage:B,selectedMedia:I,uploadingMedia:j,media:C,brands:x,loading:q,client:F,onSelectItemImage:w,uploadItemImage:z,removeItemImage:K,onSelectItemMedia:X,uploadItemMedia:e4,removeItemMedia:t4,refreshCurrentItem:Q,searchBrands:O,onBrandCreated:H,removeAttribute:V,getUnitLabel:d4,onSubmit:J,loadItemData:a4,VInputText:x4,VTextarea:T4,VAutoComplete:k4,VCheckbox:tu,VButton:l4,VDialog:b4,AttributeManager:su,QuickCreateBrand:au,get PlusIcon(){return A4},get Trash2Icon(){return H4},get FileTextIcon(){return ru},get resolveMediaUrl(){return lu}};return Object.defineProperty(n4,"__isScriptSetup",{enumerable:!1,value:!0}),n4}}),Nu={class:"catalog-item-form"},ju={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},qu={key:0,class:"p-error"},Hu={class:"flex gap-2"},Gu={key:0,class:"p-error"},Wu={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Qu={class:"flex items-center gap-4"},Ku={class:"w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},Xu=["src"],Ju={key:1,class:"text-surface-500 text-sm"},Yu={class:"flex flex-col gap-2"},Zu={class:"flex gap-2"},$u={key:0,class:"text-surface-500 text-xs"},u0={class:"flex items-start gap-4"},e0={class:"flex-1 grid grid-cols-2 gap-3"},t0=["src","alt"],a0=["href"],s0={class:"text-sm truncate"},r0={class:"p-2 border-t flex justify-end"},l0={class:"flex flex-col gap-2 w-64"},n0={key:0,class:"text-surface-500 text-xs"},o0={class:"flex items-center gap-3"},i0={class:"flex items-center justify-between mb-4"},d0={key:0,class:"space-y-3"},c0={class:"flex-1"},m0={class:"font-medium text-surface-900 dark:text-surface-0"},f0={key:0,class:"text-red-500 ml-1"},v0={class:"text-sm text-surface-600 dark:text-surface-400"},g0={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},y0={key:1,class:"text-center py-6 text-surface-500"},b0={class:"flex justify-end gap-3 pt-4 border-t border-surface-200 dark:border-surface-700"};function C0(m,e,r,u,p,v){const d=i4("tooltip");return l(),i("div",Nu,[t("form",{onSubmit:eu(u.onSubmit,["prevent"]),class:"space-y-6"},[t("div",ju,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),s(u.VInputText,{modelValue:u.formData.sku,"onUpdate:modelValue":e[0]||(e[0]=a=>u.formData.sku=a),placeholder:"Например: 12345-ABC",class:p4(["w-full",{"p-invalid":u.errors.sku}]),onBlur:u.validateSku},null,8,["modelValue","class"]),u.errors.sku?(l(),i("small",qu,f(u.errors.sku),1)):_("",!0)]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),t("div",Hu,[s(u.VAutoComplete,{modelValue:u.formData.selectedBrand,"onUpdate:modelValue":e[1]||(e[1]=a=>u.formData.selectedBrand=a),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:p4(["flex-1",{"p-invalid":u.errors.brandId}]),dropdown:""},null,8,["modelValue","suggestions","class"]),r4((l(),E(u.VButton,{onClick:e[2]||(e[2]=a=>u.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:o(()=>[s(u.PlusIcon,{class:"w-5 h-5"})]),_:1})),[[d,"Создать новый бренд"]])]),u.errors.brandId?(l(),i("small",Gu,f(u.errors.brandId),1)):_("",!0)])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),s(u.VTextarea,{modelValue:u.formData.description,"onUpdate:modelValue":e[3]||(e[3]=a=>u.formData.description=a),placeholder:"Описание каталожной позиции...",rows:"3",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Источник информации ",-1)),s(u.VInputText,{modelValue:u.formData.source,"onUpdate:modelValue":e[4]||(e[4]=a=>u.formData.source=a),placeholder:"Например: Официальный каталог, Данные клиента",class:"w-full"},null,8,["modelValue"])]),t("div",Wu,[t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Основное изображение",-1)),t("div",Qu,[t("div",Ku,[u.itemImageUrl?(l(),i("img",{key:0,src:u.resolveMediaUrl(u.itemImageUrl),alt:"Изображение позиции",class:"object-cover w-full h-full"},null,8,Xu)):(l(),i("span",Ju,"Нет изображения"))]),t("div",Yu,[t("input",{type:"file",accept:"image/*",onChange:u.onSelectItemImage},null,32),t("div",Zu,[s(u.VButton,{size:"small",disabled:!u.selectedImage||u.uploadingImage,onClick:u.uploadItemImage},{default:o(()=>e[16]||(e[16]=[U("Загрузить")])),_:1,__:[16]},8,["disabled"]),s(u.VButton,{size:"small",severity:"danger",outlined:"",disabled:!u.itemImageUrl||u.uploadingImage,onClick:u.removeItemImage},{default:o(()=>e[17]||(e[17]=[U("Удалить")])),_:1,__:[17]},8,["disabled"])]),u.uploadingImage?(l(),i("div",$u,"Загрузка...")):_("",!0)])])]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Дополнительные медиа (изображения, PDF)",-1)),t("div",u0,[t("div",e0,[(l(!0),i(Y,null,Z(u.currentItem?.mediaAssets||[],a=>(l(),i("div",{key:a.id,class:"border rounded overflow-hidden"},[a.mimeType?.startsWith("image/")?(l(),i("img",{key:0,src:u.resolveMediaUrl(a.url),alt:a.fileName,class:"w-full h-32 object-cover"},null,8,t0)):(l(),i("a",{key:1,href:u.resolveMediaUrl(a.url),target:"_blank",class:"flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"},[s(u.FileTextIcon,{class:"text-red-600 w-4 h-4"}),t("span",s0,f(a.fileName),1)],8,a0)),t("div",r0,[s(u.VButton,{size:"small",severity:"danger",text:"",onClick:y=>u.removeItemMedia(a.id)},{default:o(()=>e[19]||(e[19]=[U("Удалить")])),_:2,__:[19]},1032,["onClick"])])]))),128))]),t("div",l0,[t("input",{type:"file",multiple:"",accept:"image/*,application/pdf",onChange:u.onSelectItemMedia},null,32),s(u.VButton,{size:"small",disabled:!u.selectedMedia||u.uploadingMedia,onClick:u.uploadItemMedia},{default:o(()=>e[20]||(e[20]=[U("Загрузить выбранные")])),_:1,__:[20]},8,["disabled"]),u.uploadingMedia?(l(),i("div",n0,"Загрузка...")):_("",!0)])])])]),t("div",o0,[s(u.VCheckbox,{modelValue:u.formData.isPublic,"onUpdate:modelValue":e[5]||(e[5]=a=>u.formData.isPublic=a),"input-id":"isPublic",binary:""},null,8,["modelValue"]),e[22]||(e[22]=t("label",{for:"isPublic",class:"text-sm font-medium text-surface-700 dark:text-surface-300"}," Публичная позиция (видна всем пользователям) ",-1))]),t("div",null,[t("div",i0,[e[23]||(e[23]=t("h4",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Атрибуты ",-1)),s(u.VButton,{onClick:e[6]||(e[6]=a=>u.showAttributeManager=!0),severity:"secondary",outlined:"",size:"small",label:"Добавить атрибут"},{icon:o(()=>[s(u.PlusIcon,{class:"w-5 h-5"})]),_:1})]),u.formData.attributes.length>0?(l(),i("div",d0,[(l(!0),i(Y,null,Z(u.formData.attributes,(a,y)=>(l(),i("div",{key:y,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",c0,[t("div",m0,[U(f(a.template?.title||a.templateTitle)+" ",1),a.template?.isRequired?(l(),i("span",f0,"*")):_("",!0)]),t("div",v0,[U(f(a.value)+" "+f(a.template?.unit?u.getUnitLabel(a.template.unit):"")+" ",1),a.template?.group?.name?(l(),i("span",g0,f(a.template.group.name),1)):_("",!0)])]),s(u.VButton,{onClick:S=>u.removeAttribute(y),severity:"danger",size:"small",text:""},{default:o(()=>[s(u.Trash2Icon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])]))),128))])):(l(),i("div",y0," Атрибуты не добавлены "))]),t("div",b0,[s(u.VButton,{onClick:e[7]||(e[7]=a=>m.$emit("cancel")),severity:"secondary",outlined:"",label:"Отмена"}),s(u.VButton,{type:"submit",loading:u.loading,disabled:!u.isFormValid,label:r.item?"Сохранить изменения":"Создать позицию"},null,8,["loading","disabled","label"])])],32),s(u.VDialog,{visible:u.showAttributeManager,"onUpdate:visible":e[10]||(e[10]=a=>u.showAttributeManager=a),modal:"",header:"Управление атрибутами",class:"w-auto flex"},{default:o(()=>[s(u.AttributeManager,{modelValue:u.formData.attributes,"onUpdate:modelValue":e[8]||(e[8]=a=>u.formData.attributes=a),onClose:e[9]||(e[9]=a=>u.showAttributeManager=!1)},null,8,["modelValue"])]),_:1},8,["visible"]),s(u.QuickCreateBrand,{visible:u.showCreateBrand,"onUpdate:visible":e[11]||(e[11]=a=>u.showCreateBrand=a),onCreated:u.onBrandCreated},null,8,["visible"])])}const E0=$(Ou,[["render",C0]]),_0=u4({__name:"CatalogItemCard",props:{item:{}},emits:["edit","close","match","unlink"],setup(m,{expose:e}){e();const d={getUnitLabel:a=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[a]||a,getDataTypeLabel:a=>a?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[a]||a:"",getAccuracyLabel:a=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[a]||a,getAccuracySeverity:a=>({EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"})[a]||"secondary",VCard:o4,VButton:l4,VTag:C4,Icon:I4,get PencilIcon(){return w4},get LinkIcon(){return E4}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}}),p0={class:"catalog-item-card"},h0={class:"flex items-start justify-between mb-6"},D0={class:"flex items-center gap-4"},x0={class:"text-2xl font-bold text-surface-900 dark:text-surface-0 font-mono"},k0={class:"flex flex-wrap items-center gap-2 mt-1"},F0={class:"flex gap-2"},B0={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"},I0={class:"space-y-4"},w0={class:"p-4"},A0={key:0,class:"text-surface-700 dark:text-surface-300"},V0={key:1,class:"text-surface-400 italic"},T0={class:"p-4 space-y-3"},S0={class:"flex justify-between"},M0={class:"font-medium"},P0={class:"flex justify-between"},L0={class:"flex justify-between"},R0={class:"font-medium font-mono"},U0={class:"space-y-4"},z0={class:"p-4 space-y-3"},O0={class:"flex justify-between"},N0={class:"font-medium"},j0={class:"flex justify-between"},q0={key:0,class:"flex justify-between"},H0={class:"font-medium"},G0={class:"p-4 space-y-3"},W0={class:"flex justify-between"},Q0={class:"flex justify-between"},K0={class:"p-4 border-b border-surface-200 dark:border-surface-700"},X0={class:"font-semibold text-surface-900 dark:text-surface-0"},J0={class:"p-4"},Y0={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Z0={class:"font-medium text-surface-900 dark:text-surface-0 mb-1"},$0={key:0,class:"text-red-500 ml-1"},ue={class:"text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1"},ee={class:"flex items-center gap-2"},te={class:"text-xs text-surface-500"},ae={class:"p-4 border-b border-surface-200 dark:border-surface-700"},se={class:"font-semibold text-surface-900 dark:text-surface-0"},re={class:"p-4"},le={class:"space-y-3"},ne={class:"flex-1 min-w-0"},oe={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},ie=["href"],de={key:1},ce={class:"flex flex-wrap items-center gap-2 mt-1"},me={key:0,class:"text-sm text-surface-600 dark:text-surface-400"},fe={class:"ml-3 shrink-0"};function ve(m,e,r,u,p,v){const d=i4("tooltip");return l(),i("div",p0,[t("div",h0,[t("div",D0,[t("div",null,[t("h2",x0,f(r.item.sku),1),t("div",k0,[s(u.VTag,{value:r.item.brand?.name||"Бренд не указан",severity:r.item.brand?.isOem?"info":"secondary"},null,8,["value","severity"]),r.item.brand?.isOem?(l(),E(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):_("",!0),r.item.isPublic?_("",!0):(l(),E(u.VTag,{key:1,value:"Приватная",severity:"warning",size:"small"})),s(u.VTag,{value:"#"+r.item.id,severity:"secondary",size:"small"},null,8,["value"]),r.item.source?(l(),E(u.VTag,{key:2,value:"Источник: "+r.item.source,severity:"secondary",size:"small"},null,8,["value"])):_("",!0),s(u.VTag,{value:"Атрибутов: "+(r.item.attributes?.length||0),severity:"secondary",size:"small"},null,8,["value"]),s(u.VTag,{value:"Применимостей: "+(r.item.applicabilities?.length||0),severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),t("div",F0,[s(u.VButton,{onClick:e[0]||(e[0]=a=>m.$emit("edit")),severity:"secondary",outlined:"",size:"small"},{default:o(()=>[s(u.PencilIcon,{class:"w-5 h-5"})]),_:1}),s(u.VButton,{onClick:e[1]||(e[1]=a=>m.$emit("match")),severity:"secondary",outlined:"",size:"small",label:"Подобрать"},{default:o(()=>[s(u.LinkIcon,{class:"w-5 h-5"})]),_:1})])]),t("div",B0,[t("div",I0,[s(u.VCard,null,{header:o(()=>e[2]||(e[2]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Описание ")],-1)])),content:o(()=>[t("div",w0,[r.item.description?(l(),i("p",A0,f(r.item.description),1)):(l(),i("span",V0," Описание не указано "))])]),_:1}),s(u.VCard,null,{header:o(()=>e[3]||(e[3]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Метаданные ")],-1)])),content:o(()=>[t("div",T0,[t("div",S0,[e[4]||(e[4]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Источник:",-1)),t("span",M0,f(r.item.source||"Не указан"),1)]),t("div",P0,[e[5]||(e[5]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Видимость:",-1)),s(u.VTag,{value:r.item.isPublic?"Публичная":"Приватная",severity:r.item.isPublic?"success":"warning",size:"small"},null,8,["value","severity"])]),t("div",L0,[e[6]||(e[6]=t("span",{class:"text-surface-600 dark:text-surface-400"},"ID:",-1)),t("span",R0," #"+f(r.item.id),1)])])]),_:1})]),t("div",U0,[r.item.brand?(l(),E(u.VCard,{key:0},{header:o(()=>e[7]||(e[7]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Информация о бренде ")],-1)])),content:o(()=>[t("div",z0,[t("div",O0,[e[8]||(e[8]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Название:",-1)),t("span",N0,f(r.item.brand.name),1)]),t("div",j0,[e[9]||(e[9]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Тип:",-1)),s(u.VTag,{value:r.item.brand.isOem?"OEM производитель":"Aftermarket",severity:r.item.brand.isOem?"info":"secondary",size:"small"},null,8,["value","severity"])]),r.item.brand.country?(l(),i("div",q0,[e[10]||(e[10]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Страна:",-1)),t("span",H0,f(r.item.brand.country),1)])):_("",!0)])]),_:1})):_("",!0),s(u.VCard,null,{header:o(()=>e[11]||(e[11]=[t("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[t("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Статистика ")],-1)])),content:o(()=>[t("div",G0,[t("div",W0,[e[12]||(e[12]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Атрибутов:",-1)),s(u.VTag,{value:r.item.attributes?.length||0,severity:"secondary",size:"small"},null,8,["value"])]),t("div",Q0,[e[13]||(e[13]=t("span",{class:"text-surface-600 dark:text-surface-400"},"Групп применимости:",-1)),s(u.VTag,{value:r.item.applicabilities?.length||0,severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),_:1})])]),r.item.attributes?.length>0?(l(),E(u.VCard,{key:0,class:"mb-6"},{header:o(()=>[t("div",K0,[t("h3",X0," Атрибуты ("+f(r.item.attributes.length)+") ",1)])]),content:o(()=>[t("div",J0,[t("div",Y0,[(l(!0),i(Y,null,Z(r.item.attributes,a=>(l(),i("div",{key:a.id,class:"p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",Z0,[U(f(a.template?.title||"Неизвестный атрибут")+" ",1),a.template?.isRequired?(l(),i("span",$0,"*")):_("",!0)]),t("div",ue,f(a.value)+" "+f(a.template?.unit?u.getUnitLabel(a.template.unit):""),1),t("div",ee,[a.template?.group?.name?(l(),E(u.VTag,{key:0,value:a.template.group.name,severity:"secondary",size:"small"},null,8,["value"])):_("",!0),t("span",te,f(u.getDataTypeLabel(a.template?.dataType)),1)])]))),128))])])]),_:1})):_("",!0),r.item.applicabilities?.length>0?(l(),E(u.VCard,{key:1},{header:o(()=>[t("div",ae,[t("h3",se," Применимость ("+f(r.item.applicabilities.length)+") ",1)])]),content:o(()=>[t("div",re,[t("div",le,[(l(!0),i(Y,null,Z(r.item.applicabilities,a=>(l(),i("div",{key:a.id,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",ne,[t("div",oe,[a.part?.id?(l(),i("a",{key:0,class:"hover:text-primary-600 dark:hover:text-primary-400 underline-offset-2 hover:underline",href:`/admin/parts/${a.part.id}`,target:"_blank",rel:"noopener"},f(a.part?.name||`Группа #${a.part?.id}`),9,ie)):(l(),i("span",de,f(a.part?.name||`Группа #${a.part?.id}`),1))]),t("div",ce,[s(u.VTag,{value:u.getAccuracyLabel(a.accuracy),severity:u.getAccuracySeverity(a.accuracy),size:"small"},null,8,["value","severity"]),a.notes?(l(),i("span",me,f(a.notes),1)):_("",!0)])]),t("div",fe,[r4((l(),E(u.VButton,{severity:"danger",text:"",size:"small",onClick:y=>m.$emit("unlink",a)},{icon:o(()=>[s(u.Icon,{name:"pi pi-trash",class:"w-4 h-4"})]),_:2},1032,["onClick"])),[[d,"Отвязать"]])])]))),128))])])]),_:1})):_("",!0)])}const ge=$(_0,[["render",ve]]),ye=u4({__name:"MatchingResults",props:{item:{},results:{},loading:{type:Boolean}},emits:["refresh","link"],setup(m,{expose:e,emit:r}){e();const u=m,p=r,{getAccuracyLabel:v,getAccuracySeverity:d}=iu(),a=g(!1),y=g(null),S=g(!1),k=uu({accuracy:"EXACT_MATCH",notes:""}),L=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],W=C=>{y.value=C,k.accuracy=C.accuracySuggestion,k.notes="";const x=(C.details||[]).find(F=>String(F.kind).includes("NEAR")||String(F.kind).includes("LEGACY"));x?.notes&&(k.notes=x.notes),(C.details||[]).find(F=>F.kind==="NUMBER_WITHIN_TOLERANCE")&&!k.notes&&(k.notes="Совпадение по допуску"),a.value=!0},h=()=>{a.value=!1,y.value=null,k.accuracy="EXACT_MATCH",k.notes=""},M=()=>{y.value&&(S.value=!0,p("link",{partId:y.value.part.id,accuracy:k.accuracy,notes:k.notes||void 0}),S.value=!1,h())},{matching:D}=y4(),B=D4(),j={props:u,emit:p,getAccuracyLabel:v,getAccuracySeverity:d,showConfirmDialog:a,selectedCandidate:y,linking:S,confirmForm:k,accuracyOptions:L,openConfirmDialog:W,closeConfirmDialog:h,confirmLink:M,matching:D,toast:B,queueProposal:async C=>{try{const x={partId:C.part.id,accuracy:C.accuracySuggestion,notes:void 0},q=(C.details||[]).find(w=>String(w.kind).includes("NEAR")||String(w.kind).includes("LEGACY"));q?.notes&&(x.notes=q.notes),(C.details||[]).find(w=>w.kind==="NUMBER_WITHIN_TOLERANCE")&&!x.notes&&(x.notes="Совпадение по допуску"),await D.proposeLink({catalogItemId:u.item.id,partId:x.partId,accuracySuggestion:x.accuracy,notesSuggestion:x.notes,details:C.details})}catch{B.error("Ошибка","Не удалось добавить предложение")}},VButton:l4,VCard:o4,VTag:C4,VDialog:b4,VSelect:nu,VTextarea:T4,MatchingDetailsGrid:ou,MatchingEmptyState:cu,get RefreshCcwIcon(){return S4},get LinkIcon(){return E4},get SendIcon(){return vu},MatchingLoadingState:du};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}}),be={class:"p-4 space-y-4"},Ce={class:"flex items-center justify-between"},Ee={class:"text-lg font-mono font-semibold"},_e={class:"flex gap-2"},pe={key:2,class:"space-y-3"},he={class:"p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"},De={class:"md:col-span-1"},xe={class:"font-semibold text-surface-900 dark:text-surface-0"},ke={class:"mt-2"},Fe={class:"mt-3"},Be={class:"md:col-span-2"},Ie={key:0,class:"space-y-4"},we={class:"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded"},Ae={class:"font-semibold"},Ve={class:"text-sm"},Te={class:"font-semibold"},Se={class:"space-y-3"},Me={class:"flex justify-between"};function Pe(m,e,r,u,p,v){const d=i4("tooltip");return l(),i("div",be,[t("div",Ce,[t("div",null,[e[5]||(e[5]=t("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),t("div",Ee,[U(f(r.item.sku)+" ",1),e[4]||(e[4]=t("span",{class:"text-surface-500"},"—",-1)),U(" "+f(r.item.brand?.name),1)])]),t("div",_e,[s(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:r.loading,onClick:e[0]||(e[0]=a=>m.$emit("refresh"))},{default:o(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])]),r.loading?(l(),E(u.MatchingLoadingState,{key:0})):!r.results||r.results.length===0?(l(),E(u.MatchingEmptyState,{key:1})):(l(),i("div",pe,[(l(!0),i(Y,null,Z(r.results,a=>(l(),E(u.VCard,{key:a.part.id,class:"border"},{content:o(()=>[t("div",he,[t("div",De,[t("div",xe,f(a.part.name||"Группа #"+a.part.id),1),t("div",ke,[s(u.VTag,{value:u.getAccuracyLabel(a.accuracySuggestion),severity:u.getAccuracySeverity(a.accuracySuggestion)},null,8,["value","severity"])]),t("div",Fe,[r4((l(),E(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.openConfirmDialog(a)},{default:o(()=>[e[6]||(e[6]=U(" Привязать ")),s(u.LinkIcon,{class:"w-5 h-5"})]),_:2,__:[6]},1032,["onClick"])),[[d,"Создать связь"]]),r4((l(),E(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.queueProposal(a),class:"ml-2"},{default:o(()=>[e[7]||(e[7]=U(" В очередь ")),s(u.SendIcon,{class:"w-5 h-5"})]),_:2,__:[7]},1032,["onClick"])),[[d,"В очередь предложений"]])])]),t("div",Be,[e[8]||(e[8]=t("div",{class:"text-sm text-surface-500 mb-2"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:a.details,controls:!1},null,8,["details"])])])]),_:2},1024))),128))])),s(u.VDialog,{visible:u.showConfirmDialog,"onUpdate:visible":e[3]||(e[3]=a=>u.showConfirmDialog=a),modal:"",header:"Подтверждение связи",class:"w-full max-w-3xl"},{footer:o(()=>[t("div",Me,[s(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeConfirmDialog}),s(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLink,loading:u.linking},null,8,["loading"])])]),default:o(()=>[u.selectedCandidate?(l(),i("div",Ie,[t("div",we,[t("div",null,[e[9]||(e[9]=t("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),t("div",Ae,f(r.item.sku),1),t("div",Ve,f(r.item.brand?.name),1)]),t("div",null,[e[10]||(e[10]=t("div",{class:"text-sm text-surface-500"},"Группа взаимозаменяемости",-1)),t("div",Te,f(u.selectedCandidate.part.name||`Группа #${u.selectedCandidate.part.id}`),1)])]),t("div",null,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold mb-3"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:u.selectedCandidate.details,controls:!1},null,8,["details"])]),t("div",Se,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность совпадения ",-1)),s(u.VSelect,{modelValue:u.confirmForm.accuracy,"onUpdate:modelValue":e[1]||(e[1]=a=>u.confirmForm.accuracy=a),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),s(u.VTextarea,{modelValue:u.confirmForm.notes,"onUpdate:modelValue":e[2]||(e[2]=a=>u.confirmForm.notes=a),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),e[14]||(e[14]=t("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):_("",!0)]),_:1},8,["visible"])])}const Le=$(ye,[["render",Pe]]),Re=u4({__name:"CatalogItemsManager",setup(m,{expose:e}){e();const{catalogItems:r,brands:u,loading:p,error:v,matching:d,partApplicability:a,client:y}=y4(),S=y.crud.catalogItemAttribute,k=G4(),L=D4(),W=g([]),h=g(0),M=g(1),D=g(20),B=g(""),I=g(null),j=g([]),C=q4({q:"",brandId:void 0,page:1,rows:20,sortField:"",sortOrder:1},{prefix:"ci_",numberParams:["brandId","page","rows"],debounceMs:300}),x=g(!1),q=g(!1),F=g(null),w=g(null),z=g(null),K=g(!1),X=g(null),e4=g(!1),t4=g([]),Q=g4(()=>{const n=z.value;if(!n||!Array.isArray(n.attributes))return[];const c=new Set,P=[];for(const N of n.attributes){const R=N.template;R&&(c.has(R.id)||(c.add(R.id),P.push({id:R.id,title:R.title||R.name||`#${R.id}`})))}return P.sort((N,R)=>N.title.localeCompare(R.title))}),O=g(""),H=g(1),V=async()=>{try{const n={skip:(M.value-1)*D.value,take:D.value,include:{brand:!0,image:!0,mediaAssets:!0,attributes:{include:{template:!0}},applicabilities:{include:{part:!0}}}};if(B.value.trim()&&(n.where={OR:[{sku:{contains:B.value.trim(),mode:"insensitive"}},{description:{contains:B.value.trim(),mode:"insensitive"}},{brand:{name:{contains:B.value.trim(),mode:"insensitive"}}}]}),I.value){const N={brandId:I.value.id};n.where?n.where={AND:[n.where,N]}:n.where=N}O.value?n.orderBy={[O.value]:H.value===1?"asc":"desc"}:n.orderBy={id:"desc"};const c=await r.findMany(n);Array.isArray(c)?W.value=c:W.value=[];const P=await r.findMany({where:n.where,select:{id:!0}});h.value=Array.isArray(P)?P.length:0}catch(n){console.error("Ошибка загрузки каталожных позиций:",n),L.error("Ошибка","Не удалось загрузить каталожные позиции")}},d4=async n=>{try{const c=n.query.toLowerCase(),P=await u.findMany({where:{name:{contains:c,mode:"insensitive"}},take:10});j.value=Array.isArray(P)?P:[]}catch(c){console.error("Ошибка поиска брендов:",c)}};let J;const a4=()=>{clearTimeout(J),J=setTimeout(()=>{M.value=1,C.updateFilters({q:B.value||void 0,page:1}),V()},300)},n4=()=>{M.value=1,C.updateFilters({brandId:I.value?.id,page:1}),V()},b=n=>{M.value=Math.floor(n.first/n.rows)+1,D.value=n.rows,C.updateFilters({page:M.value,rows:D.value}),V()},A=n=>{O.value=n.sortField,H.value=n.sortOrder,C.updateFilters({sortField:O.value,sortOrder:H.value}),V()},G=()=>{V()},s4=n=>{F.value={...n},x.value=!0},T=n=>{k.confirmDelete(`позицию "${n.sku}" (${n.brand?.name})`,async()=>{try{await r.delete({where:{id:n.id}}),L.success("Успешно","Позиция удалена"),V()}catch(c){console.error("Ошибка удаления:",c),L.error("Ошибка","Не удалось удалить позицию")}})},c4=n=>{w.value=n,q.value=!0},m4=n=>{z.value=n,K.value=!0,f4()},f4=async()=>{if(z.value){e4.value=!0,X.value=null;try{const n=await d.findMatchingParts({catalogItemId:z.value.id,requiredTemplateIds:t4.value.length?t4.value:void 0});X.value=n?n.candidates||[]:[]}catch(n){console.error("Ошибка подбора:",n),X.value=[]}finally{e4.value=!1}}},P4=async n=>{if(z.value)try{await a.upsert({where:{partId_catalogItemId:{partId:n.partId,catalogItemId:z.value.id}},create:{partId:n.partId,catalogItemId:z.value.id,accuracy:n.accuracy,notes:n.notes},update:{accuracy:n.accuracy,notes:n.notes}}),K.value=!1,V()}catch(c){console.error("Ошибка привязки:",c),L.error("Ошибка","Не удалось привязать позицию")}},L4=async n=>{try{const{attributes:c,...P}=n;let N=null;if(F.value?(N=await r.update({where:{id:F.value.id},data:P}),c?.deleteMany!==void 0&&await S.deleteMany.mutate({where:{catalogItemId:F.value.id}})):N=await r.create({data:P}),c?.create&&c.create.length>0)for(const R of c.create)try{await S.create.mutate({data:{value:String(R.value),catalogItem:{connect:{id:N.id}},template:{connect:{id:R.templateId}}}})}catch(N4){console.error("Ошибка создания атрибута:",N4)}x.value=!1,F.value=null,V()}catch(c){console.error("❌ Ошибка сохранения:",c),L.error("Ошибка","Не удалось сохранить позицию")}},R4=()=>{x.value=!1,F.value=null},U4=()=>{F.value={...w.value},q.value=!1,x.value=!0},z4=()=>{z.value=w.value,K.value=!0,f4()},O4=async n=>{n?.id&&k.show({header:"Удалить связь?",message:`Отвязать позицию от группы #${n.part?.id||""}?`,icon:"pi pi-trash",acceptLabel:"Отвязать",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:async()=>{try{await a.delete({where:{id:n.id}}),w.value&&(w.value={...w.value,applicabilities:(w.value.applicabilities||[]).filter(c=>c.id!==n.id)}),V()}catch(c){console.error("Ошибка отвязки:",c),L.error("Ошибка","Не удалось отвязать позицию")}}})};B4(()=>{const n=C.filters.value;B.value=n.q||"",I.value=n.brandId?{id:n.brandId}:null,M.value=n.page||1,D.value=n.rows||20,O.value=n.sortField||"",H.value=n.sortOrder===-1?-1:1,V()}),F4(C.filters,n=>{const c=n,P=c.brandId?{id:c.brandId}:null;B.value!==(c.q||"")&&(B.value=c.q||""),(I.value?.id||null)!==(c.brandId??null)&&(I.value=P),M.value!==(c.page||1)&&(M.value=c.page||1),D.value!==(c.rows||20)&&(D.value=c.rows||20),O.value!==(c.sortField||"")&&(O.value=c.sortField||""),H.value!==(c.sortOrder===-1?-1:1)&&(H.value=c.sortOrder===-1?-1:1),V()});const _4={catalogItemsApi:r,brands:u,loading:p,error:v,matching:d,partApplicability:a,client:y,catalogItemAttributesApi:S,confirm:k,toast:L,catalogItems:W,totalRecords:h,currentPage:M,pageSize:D,searchQuery:B,selectedBrand:I,brandSuggestions:j,urlSync:C,showCreateDialog:x,showDetailsDialog:q,editingItem:F,selectedItem:w,matchingItem:z,showMatchingDialog:K,matchingResults:X,matchingLoading:e4,requiredTemplateIds:t4,attributeOptions:Q,sortField:O,sortOrder:H,loadCatalogItems:V,searchBrands:d4,get searchTimeout(){return J},set searchTimeout(n){J=n},debouncedSearch:a4,onBrandFilterChange:n4,onPageChange:b,onSort:A,refreshData:G,onEdit:s4,onDelete:T,onViewDetails:c4,onMatch:m4,runMatching:f4,linkToPart:P4,onSave:L4,onCancel:R4,onEditFromDetails:U4,onMatchFromCard:z4,onUnlink:O4,VCard:o4,VInputText:x4,VAutoComplete:k4,VButton:l4,VDialog:b4,VConfirmDialog:W4,VMultiSelect:Q4,Toast:K4,CatalogItemsTable:zu,CatalogItemForm:E0,CatalogItemCard:ge,MatchingResults:Le,get SearchIcon(){return mu},get PlusIcon(){return A4},get RefreshCcwIcon(){return S4}};return Object.defineProperty(_4,"__isScriptSetup",{enumerable:!1,value:!0}),_4}}),Ue={class:"catalog-items-manager"},ze={class:"p-6"},Oe={class:"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between"},Ne={class:"flex flex-col sm:flex-row gap-4 flex-1"},je={class:"flex-1"},qe={class:"flex gap-2"},He={key:0,class:"p-4 space-y-4"},Ge={class:"flex flex-col md:flex-row md:items-end gap-3"},We={class:"flex-1"},Qe={class:"flex gap-2"};function Ke(m,e,r,u,p,v){return l(),i("div",Ue,[s(u.VCard,{class:"mb-6"},{content:o(()=>[t("div",ze,[t("div",Oe,[t("div",Ne,[t("div",je,[s(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":e[0]||(e[0]=d=>u.searchQuery=d),placeholder:"Поиск по артикулу, описанию или бренду...",class:"w-full",onInput:u.debouncedSearch},{prefix:o(()=>[s(u.SearchIcon)]),_:1},8,["modelValue"])]),s(u.VAutoComplete,{modelValue:u.selectedBrand,"onUpdate:modelValue":e[1]||(e[1]=d=>u.selectedBrand=d),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Фильтр по бренду",class:"w-full sm:w-64",onChange:u.onBrandFilterChange,dropdown:""},null,8,["modelValue","suggestions"])]),t("div",qe,[s(u.VButton,{onClick:e[2]||(e[2]=d=>u.showCreateDialog=!0),severity:"secondary",outlined:"",label:"Добавить позицию"},{default:o(()=>[s(u.PlusIcon)]),_:1}),s(u.VButton,{onClick:u.refreshData,severity:"secondary",outlined:"",loading:u.loading},{default:o(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])])])]),_:1}),s(u.CatalogItemsTable,{items:u.catalogItems,loading:u.loading,"total-records":u.totalRecords,rows:u.pageSize,first:(u.currentPage-1)*u.pageSize,onPage:u.onPageChange,onSort:u.onSort,onEdit:u.onEdit,onDelete:u.onDelete,onViewDetails:u.onViewDetails,onMatch:u.onMatch},null,8,["items","loading","total-records","rows","first"]),s(u.VDialog,{visible:u.showCreateDialog,"onUpdate:visible":e[3]||(e[3]=d=>u.showCreateDialog=d),modal:"",header:u.editingItem?"Редактировать позицию":"Создать позицию",class:"w-auto flex"},{default:o(()=>[s(u.CatalogItemForm,{item:u.editingItem,onSave:u.onSave,onCancel:u.onCancel},null,8,["item"])]),_:1},8,["visible","header"]),s(u.VDialog,{visible:u.showDetailsDialog,"onUpdate:visible":e[5]||(e[5]=d=>u.showDetailsDialog=d),modal:"",header:"Детали позиции",class:"w-auto"},{default:o(()=>[u.selectedItem?(l(),E(u.CatalogItemCard,{key:0,item:u.selectedItem,onEdit:u.onEditFromDetails,onMatch:u.onMatchFromCard,onClose:e[4]||(e[4]=d=>u.showDetailsDialog=!1),onUnlink:u.onUnlink},null,8,["item"])):_("",!0)]),_:1},8,["visible"]),s(u.VDialog,{visible:u.showMatchingDialog,"onUpdate:visible":e[7]||(e[7]=d=>u.showMatchingDialog=d),modal:"",header:"Подбор взаимозаменяемых групп",class:"w-auto"},{default:o(()=>[u.matchingItem?(l(),i("div",He,[t("div",Ge,[t("div",We,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Обязательные атрибуты для совпадения ",-1)),s(u.VMultiSelect,{modelValue:u.requiredTemplateIds,"onUpdate:modelValue":e[6]||(e[6]=d=>u.requiredTemplateIds=d),options:u.attributeOptions,"option-label":"title","option-value":"id",placeholder:"Выберите атрибуты (рекомендуется 2+)",class:"w-full"},null,8,["modelValue","options"]),e[9]||(e[9]=t("small",{class:"text-surface-500"},"Чтобы исключить ложные совпадения по одному признаку — выберите ключевые атрибуты.",-1))]),t("div",Qe,[s(u.VButton,{severity:"secondary",outlined:"",loading:u.matchingLoading,onClick:u.runMatching},{default:o(()=>e[10]||(e[10]=[U("Найти")])),_:1,__:[10]},8,["loading"])])]),s(u.MatchingResults,{item:u.matchingItem,results:u.matchingResults,loading:u.matchingLoading,onRefresh:u.runMatching,onLink:u.linkToPart},null,8,["item","results","loading"])])):_("",!0)]),_:1},8,["visible"]),s(u.VConfirmDialog),s(u.Toast)])}const Xe=$(Re,[["render",Ke]]),Je=u4({__name:"CatalogItemsManagerBoundary",setup(m,{expose:e}){e();const r=g(0),p={key:r,onRetry:()=>{r.value++},ErrorBoundary:j4,CatalogItemsManager:Xe};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}});function Ye(m,e,r,u,p,v){return l(),E(u.ErrorBoundary,{variant:"detailed",title:"Ошибка каталожных позиций",message:"Не удалось загрузить или отрисовать таблицу. Попробуйте повторить.",onRetry:u.onRetry},{default:o(()=>[(l(),E(u.CatalogItemsManager,{key:u.key}))]),_:1})}const ra=$(Je,[["render",Ye]]);export{ra as default};
