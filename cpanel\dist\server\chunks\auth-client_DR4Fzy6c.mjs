import { createAuthClient } from 'better-auth/vue';
import { adminClient, phoneNumberClient, usernameClient, anonymousClient } from 'better-auth/client/plugins';
import { z } from 'zod';

const RoleSchema = z.enum(["GUEST", "USER", "SHOP", "ADMIN"]);

const baseSchema = z.object(
  {
    id: z.string(),
    name: z.string().nullish(),
    email: z.string(),
    emailVerified: z.boolean().default(false),
    image: z.string().nullish(),
    role: RoleSchema,
    banned: z.boolean().default(false),
    banReason: z.string().nullish(),
    banExpires: z.coerce.date().nullish(),
    createdAt: z.coerce.date().default(() => /* @__PURE__ */ new Date()),
    updatedAt: z.coerce.date()
  }
).strict();
const relationSchema = z.object(
  {
    accounts: z.array(z.unknown()).optional(),
    sessions: z.array(z.unknown()).optional()
  }
);
const UserScalarSchema = baseSchema;
UserScalarSchema.merge(relationSchema.partial());
baseSchema.partial().passthrough();
z.object({
  id: z.string(),
  name: z.string().nullish(),
  email: z.string(),
  emailVerified: z.boolean().default(false),
  image: z.string().nullish(),
  role: RoleSchema,
  banned: z.boolean().default(false),
  banReason: z.string().nullish(),
  banExpires: z.coerce.date().nullish(),
  createdAt: z.coerce.date().default(() => /* @__PURE__ */ new Date()),
  updatedAt: z.coerce.date()
}).partial().passthrough();
baseSchema.partial({
  id: true,
  emailVerified: true,
  role: true,
  banned: true,
  createdAt: true,
  updatedAt: true
});
const UserCreateSchema = baseSchema.partial({
  id: true,
  emailVerified: true,
  role: true,
  banned: true,
  createdAt: true,
  updatedAt: true
});
baseSchema.partial();

const __vite_import_meta_env__ = {"ASSETS_PREFIX": undefined, "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SITE": undefined, "SSR": true};
const getBaseURL = () => {
  if (typeof window === "undefined") {
    const internalApiUrl = process.env.INTERNAL_API_URL || "http://api:3000";
    const publicApiUrl = Object.assign(__vite_import_meta_env__, { NODE: process.env.NODE, NODE_ENV: process.env.NODE_ENV, PUBLIC: process.env.PUBLIC, _: process.env._ })?.PUBLIC_API_URL || process.env.PUBLIC_API_URL || "http://localhost:3000";
    if (process.env.NODE_ENV === "production" && internalApiUrl.includes("api:")) {
      return internalApiUrl;
    }
    return publicApiUrl;
  } else {
    return Object.assign(__vite_import_meta_env__, { NODE: process.env.NODE, NODE_ENV: process.env.NODE_ENV, PUBLIC: process.env.PUBLIC, _: process.env._ })?.PUBLIC_API_URL || window.__PUBLIC_API_URL__ || "http://localhost:3000";
  }
};
const config = {
  baseURL: getBaseURL(),
  fetchOptions: {
    timeout: 1e4,
    retries: 3
  }
};
const authClient = createAuthClient({
  baseURL: config.baseURL,
  plugins: [
    adminClient(),
    phoneNumberClient(),
    usernameClient(),
    anonymousClient()
  ],
  fetchOptions: {
    timeout: config.fetchOptions?.timeout,
    retry: config.fetchOptions?.retries,
    onRequest: (ctx) => {
      if (process.env.NODE_ENV === "development") {
        console.log("🔄 Auth request:", ctx.url);
      }
      if (typeof window === "undefined") {
        console.log("🌐 Server auth request to:", ctx.url, "baseURL:", config.baseURL);
      }
    },
    onResponse: (ctx) => {
      if (process.env.NODE_ENV === "development") {
        console.log("✅ Auth response:", ctx.response.status);
      }
    },
    onError: (ctx) => {
      console.error("❌ Auth error:", ctx.error);
      if (ctx.response?.status === 401) {
        console.warn("🔒 Unauthorized - redirecting to login");
        if (typeof window !== "undefined" && !window.location.pathname.includes("/login")) ;
      } else if (ctx.response?.status === 403) {
        console.warn("🚫 Forbidden - insufficient permissions");
      } else if (ctx.response?.status >= 500) {
        console.error("🔥 Server error - check API status");
      }
    }
  }
});
const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  $ERROR_CODES
} = authClient;
authClient.admin;
const hasRole = (user, role) => {
  return user?.role === role;
};
const isAdmin = (user) => {
  return hasRole(user, "ADMIN");
};
const isShopOwner = (user) => {
  return hasRole(user, "SHOP");
};
const canAccessAdmin = (user) => {
  return isAdmin(user) || isShopOwner(user);
};
const getErrorMessage = (error, fallback = "Произошла ошибка") => {
  if (typeof error === "string") return error;
  if (error?.message) return error.message;
  if (error?.error?.message) return error.error.message;
  return fallback;
};

export { UserCreateSchema as U, authClient as a, isShopOwner as b, canAccessAdmin as c, getErrorMessage as g, hasRole as h, isAdmin as i, useSession as u };
