import{_ as z}from"./utils.BWEB-mtU.js";import{d as L,c as l,o as d,j as N,a as s,b,e as v,f as y,ad as T,h as r}from"./index.CpC-7sc3.js";import{n as t,t as x,r as C}from"./reactivity.esm-bundler.Bx7uHohy.js";import{c as h}from"./createLucideIcon.CxvjiKko.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=h("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=h("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=h("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),H=L({__name:"ErrorBoundary",props:{title:{},message:{},variant:{default:"default"},showActions:{type:Boolean,default:!0},showReload:{type:Boolean,default:!0},showDetails:{type:Boolean,default:!1},onRetry:{}},emits:["error","retry"],setup(m,{expose:o,emit:n}){o();const e=m,i=n,c=C(!1),u=C("");T(a=>(c.value=!0,u.value=a.stack||a.message,i("error",a),!1));const p=()=>{c.value=!1,u.value="",e.onRetry&&e.onRetry(),i("retry")},w=()=>{window.location.reload()},k=r(()=>`w-full ${{default:"p-6 bg-[--color-card] border border-[--color-border] rounded-lg",minimal:"p-4",detailed:"p-8 bg-[--color-card] border border-[--color-border] rounded-xl shadow-sm"}[e.variant]}`),_=r(()=>e.variant==="minimal"?"flex items-start gap-3":"flex flex-col items-center text-center"),B=r(()=>`flex-shrink-0 ${{default:"text-[--color-danger] mb-4",minimal:"text-[--color-danger] mt-0.5",detailed:"text-[--color-danger] mb-6 p-3 bg-red-50 dark:bg-red-950/20 rounded-full"}[e.variant]}`),R=r(()=>({default:"w-8 h-8",minimal:"w-5 h-5",detailed:"w-12 h-12"})[e.variant]),M=r(()=>`font-semibold text-[--color-foreground] ${{default:"text-lg mb-2",minimal:"text-base mb-1",detailed:"text-xl mb-3"}[e.variant]}`),E=r(()=>`text-[--color-muted] ${{default:"text-sm",minimal:"text-sm",detailed:"text-base max-w-md"}[e.variant]}`),A=r(()=>"mt-2 p-3 bg-[--color-background] border border-[--color-border] rounded text-xs text-[--color-muted] overflow-auto max-h-32"),D=r(()=>"inline-flex items-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium"),S=r(()=>"inline-flex items-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium"),g={props:e,emit:i,hasError:c,errorDetails:u,retry:p,reload:w,errorContainerClasses:k,errorContentClasses:_,iconClasses:B,iconSizeClasses:R,titleClasses:M,messageClasses:E,detailsClasses:A,retryButtonClasses:D,reloadButtonClasses:S,get AlertTriangle(){return q},get RotateCcw(){return j},get RefreshCw(){return V}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}}),I={class:"flex-1"},O={key:0,class:"mt-4"},P={key:0,class:"flex gap-2 mt-4"};function F(m,o,n,e,i,c){return d(),l("div",null,[e.hasError?(d(),l("div",{key:0,class:t(e.errorContainerClasses)},[s("div",{class:t(e.errorContentClasses)},[s("div",{class:t(e.iconClasses)},[v(e.AlertTriangle,{class:t(e.iconSizeClasses)},null,8,["class"])],2),s("div",I,[s("h3",{class:t(e.titleClasses)},x(n.title||"Произошла ошибка"),3),s("p",{class:t(e.messageClasses)},x(n.message||"Что-то пошло не так. Пожалуйста, попробуйте обновить страницу."),3),n.showDetails&&e.errorDetails?(d(),l("details",O,[o[0]||(o[0]=s("summary",{class:"cursor-pointer text-sm text-[--color-muted] hover:text-[--color-foreground]"}," Подробности ошибки ",-1)),s("pre",{class:t(e.detailsClasses)},x(e.errorDetails),3)])):b("",!0)]),n.showActions?(d(),l("div",P,[s("button",{onClick:e.retry,class:t(e.retryButtonClasses)},[v(e.RotateCcw,{class:"w-4 h-4"}),o[1]||(o[1]=y(" Повторить "))],2),n.showReload?(d(),l("button",{key:0,onClick:e.reload,class:t(e.reloadButtonClasses)},[v(e.RefreshCw,{class:"w-4 h-4"}),o[2]||(o[2]=y(" Обновить страницу "))],2)):b("",!0)])):b("",!0)],2)],2)):N(m.$slots,"default",{key:1})])}const U=z(H,[["render",F]]);export{U as E,V as R};
