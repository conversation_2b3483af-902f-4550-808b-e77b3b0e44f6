import{R as <PERSON>,<PERSON> as <PERSON>}from"./ErrorBoundary.aYPzjr8z.js";import{w as Z,c as Ju,b as Fe}from"./runtime-dom.esm-bundler.0NVQG2L5.js";import{u as me}from"./useTrpc.CcBnDuWb.js";import{u as Qu}from"./useUrlParams.ByBmVuCY.js";import eu from"./Card.DllwvXut.js";import Ce from"./Button.CplYapw1.js";import{I as Te}from"./InputText.CPqCR4in.js";import{V as Ke}from"./Textarea.C8hcWg9_.js";import{S as Ne}from"./Select.B5f7pqRM.js";import{D as uu,s as tu,a as Zu}from"./index.ClGz6GkZ.js";import{T as lu}from"./Tag.BtN2Bjhy.js";import{D as Be}from"./Dialog.DjvB895c.js";import{a as ou,e as $u,d as e4,b as nu}from"./index.DBjPSdxz.js";import{s as au}from"./index.DUcQAuYR.js";import{B as u4,a2 as t4,v as l4,ax as _e,af as qe,ao as He,$ as ee,p as o4,c as h,o as d,a as n,b as w,q as ru,g as D,m as z,F as ae,a0 as ie,w as g,f as fe,r as pe,j as ye,e as a,d as ue,i as n4,k as a4,h as j,aL as r4,l as H,n as ke,N as i4}from"./index.CpC-7sc3.js";import{a as iu}from"./index.DV5zenSI.js";import{s as s4}from"./index.DqILEIKx.js";import{s as su,_ as te,p as d4}from"./utils.BWEB-mtU.js";import{s as c4,a as f4}from"./index.CwY1vywt.js";import{a as m4,s as p4}from"./index.CyH7ziOX.js";import{R as g4,f as Xe}from"./index.CMLtULFQ.js";import{n as L,t as E,b as v4,r as v,y as y4}from"./reactivity.esm-bundler.Bx7uHohy.js";import{u as Ve}from"./useToast.Cyn6G0qw.js";import{u as b4}from"./useAuth.yxvNvvlH.js";/* empty css                            */import{V as h4}from"./InputNumber.8Wyucp72.js";import{V as x4}from"./Checkbox.Czip7_Ii.js";import{V as Pe}from"./AutoComplete.WqgqstcD.js";import{D as Oe}from"./DangerButton.DyBZF5lv.js";import{T as Le,a as je,P as du}from"./trash.D7SMYTt1.js";import{c as de}from"./createLucideIcon.CxvjiKko.js";import{C as cu}from"./chevron-down.bWdMazzL.js";/* empty css                            */import{T as fu}from"./tags.HHlfcBcj.js";import{I as C4}from"./Icon.DGPcirKX.js";import"./trpc.CMxyjkwB.js";import"./router.DKcY2uv6.js";import"./schemas.BR5-L2eu.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.hiPlcmdl.js";import"./index.CbINUYrU.js";import"./index.C2Xch34u.js";import"./index.Cl2VmfYg.js";import"./index.BuLnfHxv.js";import"./SecondaryButton.ImrBLtmY.js";import"./index.2frgj6Y9.js";import"./auth-client.1y76axwe.js";import"./coerce.CW6lkyCY.js";import"./types.FgRm47Sn.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=de("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k4=de("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D4=de("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pu=de("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E4=de("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w4=de("pencil-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}],["path",{d:"m15 5 3 3",key:"1w25hb"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F4=de("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);var T4=`
    .p-tree {
        display: block;
        background: dt('tree.background');
        color: dt('tree.color');
        padding: dt('tree.padding');
    }

    .p-tree-root-children,
    .p-tree-node-children {
        display: flex;
        list-style-type: none;
        flex-direction: column;
        margin: 0;
        gap: dt('tree.gap');
    }

    .p-tree-root-children {
        padding: 0;
        padding-block-start: dt('tree.gap');
    }

    .p-tree-node-children {
        padding: 0;
        padding-block-start: dt('tree.gap');
        padding-inline-start: dt('tree.indent');
    }

    .p-tree-node {
        padding: 0;
        outline: 0 none;
    }

    .p-tree-node-content {
        border-radius: dt('tree.node.border.radius');
        padding: dt('tree.node.padding');
        display: flex;
        align-items: center;
        outline-color: transparent;
        color: dt('tree.node.color');
        gap: dt('tree.node.gap');
        transition:
            background dt('tree.transition.duration'),
            color dt('tree.transition.duration'),
            outline-color dt('tree.transition.duration'),
            box-shadow dt('tree.transition.duration');
    }

    .p-tree-node:focus-visible > .p-tree-node-content {
        box-shadow: dt('tree.node.focus.ring.shadow');
        outline: dt('tree.node.focus.ring.width') dt('tree.node.focus.ring.style') dt('tree.node.focus.ring.color');
        outline-offset: dt('tree.node.focus.ring.offset');
    }

    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {
        background: dt('tree.node.hover.background');
        color: dt('tree.node.hover.color');
    }

    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {
        color: dt('tree.node.icon.hover.color');
    }

    .p-tree-node-content.p-tree-node-selected {
        background: dt('tree.node.selected.background');
        color: dt('tree.node.selected.color');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {
        color: inherit;
    }

    .p-tree-node-toggle-button {
        cursor: pointer;
        user-select: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        position: relative;
        flex-shrink: 0;
        width: dt('tree.node.toggle.button.size');
        height: dt('tree.node.toggle.button.size');
        color: dt('tree.node.toggle.button.color');
        border: 0 none;
        background: transparent;
        border-radius: dt('tree.node.toggle.button.border.radius');
        transition:
            background dt('tree.transition.duration'),
            color dt('tree.transition.duration'),
            border-color dt('tree.transition.duration'),
            outline-color dt('tree.transition.duration'),
            box-shadow dt('tree.transition.duration');
        outline-color: transparent;
        padding: 0;
    }

    .p-tree-node-toggle-button:enabled:hover {
        background: dt('tree.node.toggle.button.hover.background');
        color: dt('tree.node.toggle.button.hover.color');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {
        background: dt('tree.node.toggle.button.selected.hover.background');
        color: dt('tree.node.toggle.button.selected.hover.color');
    }

    .p-tree-root {
        overflow: auto;
    }

    .p-tree-node-selectable {
        cursor: pointer;
        user-select: none;
    }

    .p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {
        visibility: hidden;
    }

    .p-tree-node-icon {
        color: dt('tree.node.icon.color');
        transition: color dt('tree.transition.duration');
    }

    .p-tree-node-content.p-tree-node-selected .p-tree-node-icon {
        color: dt('tree.node.icon.selected.color');
    }

    .p-tree-filter {
        margin: dt('tree.filter.margin');
    }

    .p-tree-filter-input {
        width: 100%;
    }

    .p-tree-loading {
        position: relative;
        height: 100%;
    }

    .p-tree-loading-icon {
        font-size: dt('tree.loading.icon.size');
        width: dt('tree.loading.icon.size');
        height: dt('tree.loading.icon.size');
    }

    .p-tree .p-tree-mask {
        position: absolute;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .p-tree-flex-scrollable {
        display: flex;
        flex: 1;
        height: 100%;
        flex-direction: column;
    }

    .p-tree-flex-scrollable .p-tree-root {
        flex: 1;
    }
`,B4={root:function(u){var o=u.props;return["p-tree p-component",{"p-tree-selectable":o.selectionMode!=null,"p-tree-loading":o.loading,"p-tree-flex-scrollable":o.scrollHeight==="flex"}]},mask:"p-tree-mask p-overlay-mask",loadingIcon:"p-tree-loading-icon",pcFilterContainer:"p-tree-filter",pcFilterInput:"p-tree-filter-input",wrapper:"p-tree-root",rootChildren:"p-tree-root-children",node:function(u){var o=u.instance;return["p-tree-node",{"p-tree-node-leaf":o.leaf}]},nodeContent:function(u){var o=u.instance;return["p-tree-node-content",o.node.styleClass,{"p-tree-node-selectable":o.selectable,"p-tree-node-selected":o.checkboxMode&&o.$parentInstance.highlightOnSelect?o.checked:o.selected}]},nodeToggleButton:"p-tree-node-toggle-button",nodeToggleIcon:"p-tree-node-toggle-icon",nodeCheckbox:"p-tree-node-checkbox",nodeIcon:"p-tree-node-icon",nodeLabel:"p-tree-node-label",nodeChildren:"p-tree-node-children"},V4=u4.extend({name:"tree",style:T4,classes:B4}),I4={name:"BaseTree",extends:su,props:{value:{type:null,default:null},expandedKeys:{type:null,default:null},selectionKeys:{type:null,default:null},selectionMode:{type:String,default:null},metaKeySelection:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},loadingIcon:{type:String,default:void 0},loadingMode:{type:String,default:"mask"},filter:{type:Boolean,default:!1},filterBy:{type:[String,Function],default:"label"},filterMode:{type:String,default:"lenient"},filterPlaceholder:{type:String,default:null},filterLocale:{type:String,default:void 0},highlightOnSelect:{type:Boolean,default:!1},scrollHeight:{type:String,default:null},level:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:V4,provide:function(){return{$pcTree:this,$parentInstance:this}}};function be(t){"@babel/helpers - typeof";return be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},be(t)}function We(t,u){var o=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=gu(t))||u){o&&(t=o);var e=0,s=function(){};return{s,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(m){throw m},f:s}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var r,l=!0,f=!1;return{s:function(){o=o.call(t)},n:function(){var m=o.next();return l=m.done,m},e:function(m){f=!0,r=m},f:function(){try{l||o.return==null||o.return()}finally{if(f)throw r}}}}function Ye(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function Je(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?Ye(Object(o),!0).forEach(function(e){S4(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):Ye(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function S4(t,u,o){return(u=_4(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function _4(t){var u=A4(t,"string");return be(u)=="symbol"?u:u+""}function A4(t,u){if(be(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(be(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}function ve(t){return K4(t)||M4(t)||gu(t)||G4()}function G4(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gu(t,u){if(t){if(typeof t=="string")return Ge(t,u);var o={}.toString.call(t).slice(8,-1);return o==="Object"&&t.constructor&&(o=t.constructor.name),o==="Map"||o==="Set"?Array.from(t):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Ge(t,u):void 0}}function M4(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function K4(t){if(Array.isArray(t))return Ge(t)}function Ge(t,u){(u==null||u>t.length)&&(u=t.length);for(var o=0,e=Array(u);o<u;o++)e[o]=t[o];return e}var vu={name:"TreeNode",hostName:"Tree",extends:su,emits:["node-toggle","node-click","checkbox-change"],props:{node:{type:null,default:null},expandedKeys:{type:null,default:null},loadingMode:{type:String,default:"mask"},selectionKeys:{type:null,default:null},selectionMode:{type:String,default:null},templates:{type:null,default:null},level:{type:Number,default:null},index:null},nodeTouched:!1,toggleClicked:!1,mounted:function(){this.setAllNodesTabIndexes()},methods:{toggle:function(){this.$emit("node-toggle",this.node),this.toggleClicked=!0},label:function(u){return typeof u.label=="function"?u.label():u.label},onChildNodeToggle:function(u){this.$emit("node-toggle",u)},getPTOptions:function(u){return this.ptm(u,{context:{node:this.node,index:this.index,expanded:this.expanded,selected:this.selected,checked:this.checked,partialChecked:this.partialChecked,leaf:this.leaf}})},onClick:function(u){if(this.toggleClicked||_e(u.target,'[data-pc-section="nodetogglebutton"]')||_e(u.target.parentElement,'[data-pc-section="nodetogglebutton"]')){this.toggleClicked=!1;return}this.isCheckboxSelectionMode()?this.node.selectable!=!1&&this.toggleCheckbox():this.$emit("node-click",{originalEvent:u,nodeTouched:this.nodeTouched,node:this.node}),this.nodeTouched=!1},onChildNodeClick:function(u){this.$emit("node-click",u)},onTouchEnd:function(){this.nodeTouched=!0},onKeyDown:function(u){if(this.isSameNode(u))switch(u.code){case"Tab":this.onTabKey(u);break;case"ArrowDown":this.onArrowDown(u);break;case"ArrowUp":this.onArrowUp(u);break;case"ArrowRight":this.onArrowRight(u);break;case"ArrowLeft":this.onArrowLeft(u);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(u);break}},onArrowDown:function(u){var o=u.target.getAttribute("data-pc-section")==="nodetogglebutton"?u.target.closest('[role="treeitem"]'):u.target,e=o.children[1];if(e)this.focusRowChange(o,e.children[0]);else if(o.nextElementSibling)this.focusRowChange(o,o.nextElementSibling);else{var s=this.findNextSiblingOfAncestor(o);s&&this.focusRowChange(o,s)}u.preventDefault()},onArrowUp:function(u){var o=u.target;if(o.previousElementSibling)this.focusRowChange(o,o.previousElementSibling,this.findLastVisibleDescendant(o.previousElementSibling));else{var e=this.getParentNodeElement(o);e&&this.focusRowChange(o,e)}u.preventDefault()},onArrowRight:function(u){var o=this;this.leaf||this.expanded||(u.currentTarget.tabIndex=-1,this.$emit("node-toggle",this.node),this.$nextTick(function(){o.onArrowDown(u)}))},onArrowLeft:function(u){var o=qe(u.currentTarget,'[data-pc-section="nodetogglebutton"]');if(this.level===0&&!this.expanded)return!1;if(this.expanded&&!this.leaf)return o.click(),!1;var e=this.findBeforeClickableNode(u.currentTarget);e&&this.focusRowChange(u.currentTarget,e)},onEnterKey:function(u){this.setTabIndexForSelectionMode(u,this.nodeTouched),this.onClick(u),u.preventDefault()},onTabKey:function(){this.setAllNodesTabIndexes()},setAllNodesTabIndexes:function(){var u=He(this.$refs.currentNode.closest('[data-pc-section="rootchildren"]'),'[role="treeitem"]'),o=ve(u).some(function(s){return s.getAttribute("aria-selected")==="true"||s.getAttribute("aria-checked")==="true"});if(ve(u).forEach(function(s){s.tabIndex=-1}),o){var e=ve(u).filter(function(s){return s.getAttribute("aria-selected")==="true"||s.getAttribute("aria-checked")==="true"});e[0].tabIndex=0;return}ve(u)[0].tabIndex=0},setTabIndexForSelectionMode:function(u,o){if(this.selectionMode!==null){var e=ve(He(this.$refs.currentNode.parentElement,'[role="treeitem"]'));u.currentTarget.tabIndex=o===!1?-1:0,e.every(function(s){return s.tabIndex===-1})&&(e[0].tabIndex=0)}},focusRowChange:function(u,o,e){u.tabIndex="-1",o.tabIndex="0",this.focusNode(e||o)},findBeforeClickableNode:function(u){var o=u.closest("ul").closest("li");if(o){var e=qe(o,"button");return e&&e.style.visibility!=="hidden"?o:this.findBeforeClickableNode(u.previousElementSibling)}return null},toggleCheckbox:function(){var u=this.selectionKeys?Je({},this.selectionKeys):{},o=!this.checked;this.propagateDown(this.node,o,u),this.$emit("checkbox-change",{node:this.node,check:o,selectionKeys:u})},propagateDown:function(u,o,e){if(o&&u.selectable!=!1?e[u.key]={checked:!0,partialChecked:!1}:delete e[u.key],u.children&&u.children.length){var s=We(u.children),r;try{for(s.s();!(r=s.n()).done;){var l=r.value;this.propagateDown(l,o,e)}}catch(f){s.e(f)}finally{s.f()}}},propagateUp:function(u){var o=u.check,e=Je({},u.selectionKeys),s=0,r=!1,l=We(this.node.children),f;try{for(l.s();!(f=l.n()).done;){var c=f.value;e[c.key]&&e[c.key].checked?s++:e[c.key]&&e[c.key].partialChecked&&(r=!0)}}catch(m){l.e(m)}finally{l.f()}o&&s===this.node.children.length?e[this.node.key]={checked:!0,partialChecked:!1}:(o||delete e[this.node.key],r||s>0&&s!==this.node.children.length?e[this.node.key]={checked:!1,partialChecked:!0}:delete e[this.node.key]),this.$emit("checkbox-change",{node:u.node,check:u.check,selectionKeys:e})},onChildCheckboxChange:function(u){this.$emit("checkbox-change",u)},findNextSiblingOfAncestor:function(u){var o=this.getParentNodeElement(u);return o?o.nextElementSibling?o.nextElementSibling:this.findNextSiblingOfAncestor(o):null},findLastVisibleDescendant:function(u){var o=u.children[1];if(o){var e=o.children[o.children.length-1];return this.findLastVisibleDescendant(e)}else return u},getParentNodeElement:function(u){var o=u.parentElement.parentElement;return _e(o,"role")==="treeitem"?o:null},focusNode:function(u){u.focus()},isCheckboxSelectionMode:function(){return this.selectionMode==="checkbox"},isSameNode:function(u){return u.currentTarget&&(u.currentTarget.isSameNode(u.target)||u.currentTarget.isSameNode(u.target.closest('[role="treeitem"]')))}},computed:{hasChildren:function(){return this.node.children&&this.node.children.length>0},expanded:function(){return this.expandedKeys&&this.expandedKeys[this.node.key]===!0},leaf:function(){return this.node.leaf===!1?!1:!(this.node.children&&this.node.children.length)},selectable:function(){return this.node.selectable===!1?!1:this.selectionMode!=null},selected:function(){return this.selectionMode&&this.selectionKeys?this.selectionKeys[this.node.key]===!0:!1},checkboxMode:function(){return this.selectionMode==="checkbox"&&this.node.selectable!==!1},checked:function(){return this.selectionKeys?this.selectionKeys[this.node.key]&&this.selectionKeys[this.node.key].checked:!1},partialChecked:function(){return this.selectionKeys?this.selectionKeys[this.node.key]&&this.selectionKeys[this.node.key].partialChecked:!1},ariaChecked:function(){return this.selectionMode==="single"||this.selectionMode==="multiple"?this.selected:void 0},ariaSelected:function(){return this.checkboxMode?this.checked:void 0}},components:{Checkbox:p4,ChevronDownIcon:nu,ChevronRightIcon:au,CheckIcon:c4,MinusIcon:m4,SpinnerIcon:iu},directives:{ripple:g4}},N4=["aria-label","aria-selected","aria-expanded","aria-setsize","aria-posinset","aria-level","aria-checked","tabindex"],P4=["data-p-selected","data-p-selectable"],O4=["data-p-leaf"];function L4(t,u,o,e,s,r){var l=ee("SpinnerIcon"),f=ee("Checkbox"),c=ee("TreeNode",!0),m=o4("ripple");return d(),h("li",z({ref:"currentNode",class:t.cx("node"),role:"treeitem","aria-label":r.label(o.node),"aria-selected":r.ariaSelected,"aria-expanded":r.expanded,"aria-setsize":o.node.children?o.node.children.length:0,"aria-posinset":o.index+1,"aria-level":o.level,"aria-checked":r.ariaChecked,tabindex:o.index===0?0:-1,onKeydown:u[4]||(u[4]=function(){return r.onKeyDown&&r.onKeyDown.apply(r,arguments)})},r.getPTOptions("node")),[n("div",z({class:t.cx("nodeContent"),onClick:u[2]||(u[2]=function(){return r.onClick&&r.onClick.apply(r,arguments)}),onTouchend:u[3]||(u[3]=function(){return r.onTouchEnd&&r.onTouchEnd.apply(r,arguments)}),style:o.node.style},r.getPTOptions("nodeContent"),{"data-p-selected":r.checkboxMode?r.checked:r.selected,"data-p-selectable":r.selectable}),[ru((d(),h("button",z({type:"button",class:t.cx("nodeToggleButton"),onClick:u[0]||(u[0]=function(){return r.toggle&&r.toggle.apply(r,arguments)}),tabindex:"-1","data-p-leaf":r.leaf},r.getPTOptions("nodeToggleButton")),[o.node.loading&&o.loadingMode==="icon"?(d(),h(ae,{key:0},[o.templates.nodetoggleicon||o.templates.nodetogglericon?(d(),D(ie(o.templates.nodetoggleicon||o.templates.nodetogglericon),{key:0,node:o.node,expanded:r.expanded,class:L(t.cx("nodeToggleIcon"))},null,8,["node","expanded","class"])):(d(),D(l,z({key:1,spin:"",class:t.cx("nodeToggleIcon")},r.getPTOptions("nodeToggleIcon")),null,16,["class"]))],64)):(d(),h(ae,{key:1},[o.templates.nodetoggleicon||o.templates.togglericon?(d(),D(ie(o.templates.nodetoggleicon||o.templates.togglericon),{key:0,node:o.node,expanded:r.expanded,class:L(t.cx("nodeToggleIcon"))},null,8,["node","expanded","class"])):r.expanded?(d(),D(ie(o.node.expandedIcon?"span":"ChevronDownIcon"),z({key:1,class:t.cx("nodeToggleIcon")},r.getPTOptions("nodeToggleIcon")),null,16,["class"])):(d(),D(ie(o.node.collapsedIcon?"span":"ChevronRightIcon"),z({key:2,class:t.cx("nodeToggleIcon")},r.getPTOptions("nodeToggleIcon")),null,16,["class"]))],64))],16,O4)),[[m]]),r.checkboxMode?(d(),D(f,{key:0,defaultValue:r.checked,binary:!0,indeterminate:r.partialChecked,class:L(t.cx("nodeCheckbox")),tabindex:-1,unstyled:t.unstyled,pt:r.getPTOptions("pcNodeCheckbox"),"data-p-partialchecked":r.partialChecked},{icon:g(function(I){return[o.templates.checkboxicon?(d(),D(ie(o.templates.checkboxicon),{key:0,checked:I.checked,partialChecked:r.partialChecked,class:L(I.class)},null,8,["checked","partialChecked","class"])):w("",!0)]}),_:1},8,["defaultValue","indeterminate","class","unstyled","pt","data-p-partialchecked"])):w("",!0),o.templates.nodeicon?(d(),D(ie(o.templates.nodeicon),z({key:1,node:o.node,class:[t.cx("nodeIcon")]},r.getPTOptions("nodeIcon")),null,16,["node","class"])):(d(),h("span",z({key:2,class:[t.cx("nodeIcon"),o.node.icon]},r.getPTOptions("nodeIcon")),null,16)),n("span",z({class:t.cx("nodeLabel")},r.getPTOptions("nodeLabel"),{onKeydown:u[1]||(u[1]=Z(function(){},["stop"]))}),[o.templates[o.node.type]||o.templates.default?(d(),D(ie(o.templates[o.node.type]||o.templates.default),{key:0,node:o.node,expanded:r.expanded,selected:r.checkboxMode?r.checked:r.selected},null,8,["node","expanded","selected"])):(d(),h(ae,{key:1},[fe(E(r.label(o.node)),1)],64))],16)],16,P4),r.hasChildren&&r.expanded?(d(),h("ul",z({key:0,class:t.cx("nodeChildren"),role:"group"},t.ptm("nodeChildren")),[(d(!0),h(ae,null,pe(o.node.children,function(I){return d(),D(c,{key:I.key,node:I,templates:o.templates,level:o.level+1,loadingMode:o.loadingMode,expandedKeys:o.expandedKeys,onNodeToggle:r.onChildNodeToggle,onNodeClick:r.onChildNodeClick,selectionMode:o.selectionMode,selectionKeys:o.selectionKeys,onCheckboxChange:r.propagateUp,unstyled:t.unstyled,pt:t.pt},null,8,["node","templates","level","loadingMode","expandedKeys","onNodeToggle","onNodeClick","selectionMode","selectionKeys","onCheckboxChange","unstyled","pt"])}),128))],16)):w("",!0)],16,N4)}vu.render=L4;function he(t){"@babel/helpers - typeof";return he=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},he(t)}function Ae(t,u){var o=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=yu(t))||u){o&&(t=o);var e=0,s=function(){};return{s,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(m){throw m},f:s}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var r,l=!0,f=!1;return{s:function(){o=o.call(t)},n:function(){var m=o.next();return l=m.done,m},e:function(m){f=!0,r=m},f:function(){try{l||o.return==null||o.return()}finally{if(f)throw r}}}}function j4(t){return z4(t)||R4(t)||yu(t)||U4()}function U4(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yu(t,u){if(t){if(typeof t=="string")return Me(t,u);var o={}.toString.call(t).slice(8,-1);return o==="Object"&&t.constructor&&(o=t.constructor.name),o==="Map"||o==="Set"?Array.from(t):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Me(t,u):void 0}}function R4(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function z4(t){if(Array.isArray(t))return Me(t)}function Me(t,u){(u==null||u>t.length)&&(u=t.length);for(var o=0,e=Array(u);o<u;o++)e[o]=t[o];return e}function Qe(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function se(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?Qe(Object(o),!0).forEach(function(e){q4(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):Qe(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function q4(t,u,o){return(u=H4(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function H4(t){var u=X4(t,"string");return he(u)=="symbol"?u:u+""}function X4(t,u){if(he(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(he(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}var bu={name:"Tree",extends:I4,inheritAttrs:!1,emits:["node-expand","node-collapse","update:expandedKeys","update:selectionKeys","node-select","node-unselect","filter"],data:function(){return{d_expandedKeys:this.expandedKeys||{},filterValue:null}},watch:{expandedKeys:function(u){this.d_expandedKeys=u}},methods:{onNodeToggle:function(u){var o=u.key;this.d_expandedKeys[o]?(delete this.d_expandedKeys[o],this.$emit("node-collapse",u)):(this.d_expandedKeys[o]=!0,this.$emit("node-expand",u)),this.d_expandedKeys=se({},this.d_expandedKeys),this.$emit("update:expandedKeys",this.d_expandedKeys)},onNodeClick:function(u){if(this.selectionMode!=null&&u.node.selectable!==!1){var o=u.nodeTouched?!1:this.metaKeySelection,e=o?this.handleSelectionWithMetaKey(u):this.handleSelectionWithoutMetaKey(u);this.$emit("update:selectionKeys",e)}},onCheckboxChange:function(u){this.$emit("update:selectionKeys",u.selectionKeys),u.check?this.$emit("node-select",u.node):this.$emit("node-unselect",u.node)},handleSelectionWithMetaKey:function(u){var o=u.originalEvent,e=u.node,s=o.metaKey||o.ctrlKey,r=this.isNodeSelected(e),l;return r&&s?(this.isSingleSelectionMode()?l={}:(l=se({},this.selectionKeys),delete l[e.key]),this.$emit("node-unselect",e)):(this.isSingleSelectionMode()?l={}:this.isMultipleSelectionMode()&&(l=s?this.selectionKeys?se({},this.selectionKeys):{}:{}),l[e.key]=!0,this.$emit("node-select",e)),l},handleSelectionWithoutMetaKey:function(u){var o=u.node,e=this.isNodeSelected(o),s;return this.isSingleSelectionMode()?e?(s={},this.$emit("node-unselect",o)):(s={},s[o.key]=!0,this.$emit("node-select",o)):e?(s=se({},this.selectionKeys),delete s[o.key],this.$emit("node-unselect",o)):(s=this.selectionKeys?se({},this.selectionKeys):{},s[o.key]=!0,this.$emit("node-select",o)),s},isSingleSelectionMode:function(){return this.selectionMode==="single"},isMultipleSelectionMode:function(){return this.selectionMode==="multiple"},isNodeSelected:function(u){return this.selectionMode&&this.selectionKeys?this.selectionKeys[u.key]===!0:!1},isChecked:function(u){return this.selectionKeys?this.selectionKeys[u.key]&&this.selectionKeys[u.key].checked:!1},isNodeLeaf:function(u){return u.leaf===!1?!1:!(u.children&&u.children.length)},onFilterKeyup:function(u){(u.code==="Enter"||u.code==="NumpadEnter")&&u.preventDefault(),this.$emit("filter",{originalEvent:u,value:u.target.value})},findFilteredNodes:function(u,o){if(u){var e=!1;if(u.children){var s=j4(u.children);u.children=[];var r=Ae(s),l;try{for(r.s();!(l=r.n()).done;){var f=l.value,c=se({},f);this.isFilterMatched(c,o)&&(e=!0,u.children.push(c))}}catch(m){r.e(m)}finally{r.f()}}if(e)return!0}},isFilterMatched:function(u,o){var e=o.searchFields,s=o.filterText,r=o.strict,l=!1,f=Ae(e),c;try{for(f.s();!(c=f.n()).done;){var m=c.value,I=String(l4(u,m)).toLocaleLowerCase(this.filterLocale);I.indexOf(s)>-1&&(l=!0)}}catch(C){f.e(C)}finally{f.f()}return(!l||r&&!this.isNodeLeaf(u))&&(l=this.findFilteredNodes(u,{searchFields:e,filterText:s,strict:r})||l),l}},computed:{filteredValue:function(){var u=[],o=t4(this.filterBy)?[this.filterBy]:this.filterBy.split(","),e=this.filterValue.trim().toLocaleLowerCase(this.filterLocale),s=this.filterMode==="strict",r=Ae(this.value),l;try{for(r.s();!(l=r.n()).done;){var f=l.value,c=se({},f),m={searchFields:o,filterText:e,strict:s};(s&&(this.findFilteredNodes(c,m)||this.isFilterMatched(c,m))||!s&&(this.isFilterMatched(c,m)||this.findFilteredNodes(c,m)))&&u.push(c)}}catch(I){r.e(I)}finally{r.f()}return u},valueToRender:function(){return this.filterValue&&this.filterValue.trim().length>0?this.filteredValue:this.value},containerDataP:function(){return Xe({loading:this.loading,scrollable:this.scrollHeight==="flex"})},wrapperDataP:function(){return Xe({scrollable:this.scrollHeight==="flex"})}},components:{TreeNode:vu,InputText:s4,InputIcon:e4,IconField:$u,SearchIcon:ou,SpinnerIcon:iu}};function xe(t){"@babel/helpers - typeof";return xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},xe(t)}function Ze(t,u){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);u&&(e=e.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),o.push.apply(o,e)}return o}function $e(t){for(var u=1;u<arguments.length;u++){var o=arguments[u]!=null?arguments[u]:{};u%2?Ze(Object(o),!0).forEach(function(e){W4(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):Ze(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function W4(t,u,o){return(u=Y4(u))in t?Object.defineProperty(t,u,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[u]=o,t}function Y4(t){var u=J4(t,"string");return xe(u)=="symbol"?u:u+""}function J4(t,u){if(xe(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var e=o.call(t,u);if(xe(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(t)}var Q4=["data-p"],Z4=["data-p"],$4=["aria-labelledby","aria-label"];function e0(t,u,o,e,s,r){var l=ee("SpinnerIcon"),f=ee("InputText"),c=ee("SearchIcon"),m=ee("InputIcon"),I=ee("IconField"),C=ee("TreeNode");return d(),h("div",z({class:t.cx("root"),"data-p":r.containerDataP},t.ptmi("root")),[t.loading&&t.loadingMode==="mask"?(d(),h("div",z({key:0,class:t.cx("mask")},t.ptm("mask")),[ye(t.$slots,"loadingicon",{class:L(t.cx("loadingIcon"))},function(){return[t.loadingIcon?(d(),h("i",z({key:0,class:[t.cx("loadingIcon"),"pi-spin",t.loadingIcon]},t.ptm("loadingIcon")),null,16)):(d(),D(l,z({key:1,spin:"",class:t.cx("loadingIcon")},t.ptm("loadingIcon")),null,16,["class"]))]})],16)):w("",!0),t.filter?(d(),D(I,{key:1,unstyled:t.unstyled,pt:$e($e({},t.ptm("pcFilter")),t.ptm("pcFilterContainer")),class:L(t.cx("pcFilterContainer"))},{default:g(function(){return[a(f,{modelValue:s.filterValue,"onUpdate:modelValue":u[0]||(u[0]=function(B){return s.filterValue=B}),autocomplete:"off",class:L(t.cx("pcFilterInput")),placeholder:t.filterPlaceholder,unstyled:t.unstyled,onKeyup:r.onFilterKeyup,pt:t.ptm("pcFilterInput")},null,8,["modelValue","class","placeholder","unstyled","onKeyup","pt"]),a(m,{unstyled:t.unstyled,pt:t.ptm("pcFilterIconContainer")},{default:g(function(){return[ye(t.$slots,t.$slots.filtericon?"filtericon":"searchicon",{class:L(t.cx("filterIcon"))},function(){return[a(c,z({class:t.cx("filterIcon")},t.ptm("filterIcon")),null,16,["class"])]})]}),_:3},8,["unstyled","pt"])]}),_:3},8,["unstyled","pt","class"])):w("",!0),n("div",z({class:t.cx("wrapper"),style:{maxHeight:t.scrollHeight},"data-p":r.wrapperDataP},t.ptm("wrapper")),[ye(t.$slots,"header",{value:t.value,expandedKeys:t.expandedKeys,selectionKeys:t.selectionKeys}),n("ul",z({class:t.cx("rootChildren"),role:"tree","aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel},t.ptm("rootChildren")),[(d(!0),h(ae,null,pe(r.valueToRender,function(B,A){return d(),D(C,{key:B.key,node:B,templates:t.$slots,level:t.level+1,index:A,expandedKeys:s.d_expandedKeys,onNodeToggle:r.onNodeToggle,onNodeClick:r.onNodeClick,selectionMode:t.selectionMode,selectionKeys:t.selectionKeys,onCheckboxChange:r.onCheckboxChange,loadingMode:t.loadingMode,unstyled:t.unstyled,pt:t.pt},null,8,["node","templates","level","index","expandedKeys","onNodeToggle","onNodeClick","selectionMode","selectionKeys","onCheckboxChange","loadingMode","unstyled","pt"])}),128))],16,$4),ye(t.$slots,"footer",{value:t.value,expandedKeys:t.expandedKeys,selectionKeys:t.selectionKeys})],16,Z4)],16,Q4)}bu.render=e0;const u0=ue({__name:"Tree",setup(t,{expose:u}){u();const e={theme:v({root:`bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0 p-4
        p-scrollable:flex p-scrollable:flex-1 p-scrollable:h-full p-scrollable:flex-col`,pcFilterContainer:{root:"relative mb-2"},pcFilterInput:{root:`w-full appearance-none rounded-md outline-hidden
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            placeholder:text-surface-500 dark:placeholder:text-surface-400
            border border-surface-300 dark:border-surface-700
            hover:border-surface-400 dark:hover:border-surface-600
            focus:border-primary
            disabled:bg-surface-200 disabled:text-surface-500
            dark:disabled:bg-surface-700 dark:disabled:text-surface-400
            ps-3 pe-10 py-2 p-fluid:w-full
            transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`},pcFilterIconContainer:{root:"absolute top-1/2 -mt-2 leading-none end-3 z-1"},wrapper:"overflow-auto p-scrollable:flex-1",rootChildren:"flex flex-col list-none m-0 gap-[4px] pt-[2px] pb-0 px-0",node:"p-0 outline-none focus-visible:*:first:outline focus-visible:*:first:-outline-offset-1 focus-visible:*:first:outline-primary",nodeContent:`group rounded-md px-2 py-1 flex items-center text-surface-700 dark:text-surface-0 gap-1 transition-colors duration-200
        hover:p-selectable:not-p-selected:bg-surface-100 hover:p-selectable:not-p-selected:text-surface-700 
        dark:hover:p-selectable:not-p-selected:bg-surface-800 dark:hover:p-selectable:not-p-selected:text-surface-0
        p-selected:bg-highlight 
        p-selectable:cursor-pointer p-selectable:select-none`,nodeToggleButton:`cursor-pointer select-none inline-flex justify-center rounded-full items-center overflow-hidden relative flex-shrink-0
        w-7 h-7 p-0 p-leaf:invisible transition-colors duration-200 border-none
        bg-transparent hover:bg-surface-100 dark:hover:bg-surface-800
        group-p-selected:hover:bg-surface-0 dark:group-p-selected:hover:bg-surface-900 group-p-selected:hover:text-primary
        text-surface-500 dark:text-surface-400 hover:text-surface-600 dark:hover:text-surface-300
        group-p-selected:text-inherit`,nodeToggleIcon:"",nodeIcon:`text-surface-500 dark:text-surface-400 group-p-selected:text-primary
        group-hover:group-p-selectable:not-group-p-selected:text-surface-600 
        dark:group-hover:group-p-selectable:not-group-p-selected:text-surface-300
        transition-colors duration-200`,nodeLabel:"",pcNodeCheckbox:{root:"relative inline-flex select-none w-5 h-5 align-bottom",input:`peer cursor-pointer disabled:cursor-default appearance-none 
            absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
            border border-transparent rounded-xs`,box:`flex justify-center items-center rounded-sm w-5 h-5
            border border-surface-300 dark:border-surface-700
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
            p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
            peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
            shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200`,icon:"text-sm w-[0.875rem] h-[0.875rem] transition-none"},nodeChildren:"flex flex-col list-none m-0 gap-[2px] pt-[2px] pe-0 pb-0 ps-4",mask:"bg-black/50 text-surface-200 absolute z-10 flex items-center justify-center",loadingIcon:"text-[2rem] h-8 w-8"}),get ChevronDownIcon(){return nu},get ChevronRightIcon(){return au},get SearchIcon(){return ou},get Tree(){return bu},get ptViewMerge(){return d4}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function t0(t,u,o,e,s,r){return d(),D(e.Tree,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},n4({togglericon:g(({expanded:l})=>[l?(d(),D(e.ChevronDownIcon,{key:0})):(d(),D(e.ChevronRightIcon,{key:1}))]),filtericon:g(()=>[a(e.SearchIcon,{class:"text-surface-400"})]),_:2},[pe(t.$slots,(l,f)=>({name:f,fn:g(c=>[ye(t.$slots,f,v4(a4(c??{})))])}))]),1032,["pt","ptOptions"])}const hu=te(u0,[["render",t0]]),l0=`m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`,o0=`py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`,n0="leading-none",a0="ml-2 w-4 h-4 cursor-pointer",r0=`border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`,i0=ue({__name:"InputChips",props:{modelValue:{},separator:{},addOnBlur:{type:Boolean},allowDuplicate:{type:Boolean},max:{}},emits:["update:modelValue"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,r=j({get:()=>e.modelValue,set:A=>s("update:modelValue",A)}),l=v(""),f=v(null),c=()=>{if(l.value.trim()!==""){if(e.max&&r.value.length>=e.max)return;if(!e.allowDuplicate&&r.value.includes(l.value.trim())){l.value="";return}r.value=[...r.value,l.value.trim()],l.value=""}},m=A=>{r.value=r.value.filter((N,V)=>V!==A)},B={props:e,emit:s,model:r,inputValue:l,inputRef:f,addValue:c,removeValue:m,handleBackspace:()=>{l.value===""&&r.value.length>0&&m(r.value.length-1)},focusInput:async()=>{await r4(),f.value?.focus()},containerClass:l0,tokenClass:o0,labelClass:n0,removeIconClass:a0,inputClass:r0,get TimesIcon(){return f4}};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),s0=["onClick"],d0=["onKeydown"];function c0(t,u,o,e,s,r){return d(),h("div",{class:L(e.containerClass),onClick:e.focusInput},[(d(!0),h(ae,null,pe(e.model,(l,f)=>(d(),h("div",{key:f,class:L(e.tokenClass)},[n("span",{class:L(e.labelClass)},E(l),1),n("span",{class:L(e.removeIconClass),onClick:Z(c=>e.removeValue(f),["stop"])},[a(e.TimesIcon)],8,s0)]))),128)),ru(n("input",{ref:"inputRef",type:"text","onUpdate:modelValue":u[0]||(u[0]=l=>e.inputValue=l),class:L(e.inputClass),onKeydown:[Fe(Z(e.addValue,["prevent"]),["enter"]),Fe(e.handleBackspace,["backspace"])]},null,40,d0),[[Ju,e.inputValue]])])}const f0=te(i0,[["render",c0]]),m0=ue({__name:"EditSynonymGroupDialog",props:{visible:{type:Boolean},templateId:{},group:{}},emits:["update:visible","saved"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,{attributeSynonyms:r}=me(),l=Ve(),f=j({get:()=>e.visible,set:k=>s("update:visible",k)}),c=j(()=>!!e.group?.id),m=j(()=>c.value?"Редактировать группу":"Создать группу"),I=[{label:"EXACT",value:"EXACT"},{label:"NEAR",value:"NEAR"},{label:"LEGACY",value:"LEGACY"}],C=v({name:"",canonicalValue:"",parentId:null,description:"",compatibilityLevel:"EXACT",notes:""}),B=v([]),A=j(()=>{const k=Array.isArray(B.value)?B.value:[];if(!e.group?.id)return[{id:null,name:"Нет родителя"},...k];const X=new Map;for(const y of k)if(typeof y.parentId=="number"){const p=X.get(y.parentId)||[];p.push(y.id),X.set(y.parentId,p)}const U=new Set,R=[e.group.id];for(;R.length;){const y=R.pop(),p=X.get(y)||[];for(const T of p)U.has(T)||(U.add(T),R.push(T))}const O=k.filter(y=>y&&typeof y.id=="number"?y.id!==e.group.id&&!U.has(y.id):!0);return[{id:null,name:"Нет родителя"},...O]}),N=v({}),V=v(!1);H(()=>e.group,k=>{k?C.value={name:k.name||"",canonicalValue:k.canonicalValue||k.name||"",parentId:k.parentId??null,description:k.description||null,compatibilityLevel:k.compatibilityLevel||"EXACT",notes:k.notes||null}:C.value={name:"",canonicalValue:"",parentId:null,description:null,compatibilityLevel:"EXACT",notes:null}},{immediate:!0}),H(()=>e.templateId,async k=>{if(!k)return;const{attributeSynonyms:X}=me(),U=100;let R=0,O=[],y=0;for(;;){const p=await X.groups.findMany({templateId:k,limit:U,offset:R}),T=p?.groups??[];if(y=p?.total??y,O=O.concat(T),T.length<U||(R+=U,y&&R>=y))break}B.value=O},{immediate:!0});const F=()=>(N.value={},C.value.name.trim()||(N.value.name="Введите название"),!C.value.parentId&&!C.value.canonicalValue.trim()&&(N.value.canonicalValue="Введите каноническое значение"),Object.keys(N.value).length===0),K=()=>{f.value=!1},G={props:e,emit:s,attributeSynonyms:r,toast:l,visible:f,isEdit:c,dialogTitle:m,compatibilityOptions:I,form:C,parentOptions:B,parentSelectOptions:A,errors:N,saving:V,validate:F,close:K,save:async()=>{if(F()){V.value=!0;try{c.value?(await r.groups.update({id:e.group.id,...C.value}),l.success("Группа обновлена")):(await r.groups.create({templateId:e.templateId,name:C.value.name,canonicalValue:C.value.canonicalValue||void 0,parentId:C.value.parentId,description:C.value.description,compatibilityLevel:C.value.compatibilityLevel,notes:C.value.notes}),l.success("Группа создана")),s("saved"),K()}catch(k){l.error(k?.message||"Не удалось сохранить группу")}finally{V.value=!1}}},VDialog:Be,VInputText:Te,VTextarea:Ke,VButton:Ce,VSelect:Ne};return Object.defineProperty(G,"__isScriptSetup",{enumerable:!1,value:!0}),G}}),p0={class:"space-y-4"},g0={key:0,class:"p-error"},v0={key:0,class:"p-error"},y0={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function b0(t,u,o,e,s,r){return d(),D(e.VDialog,{visible:e.visible,"onUpdate:visible":u[6]||(u[6]=l=>e.visible=l),modal:"",header:e.dialogTitle,style:{width:"34rem"}},{footer:g(()=>[a(e.VButton,{label:"Отмена",severity:"secondary",onClick:e.close}),a(e.VButton,{label:e.isEdit?"Сохранить":"Создать",loading:e.saving,onClick:e.save},null,8,["label","loading"])]),default:g(()=>[n("div",p0,[n("div",null,[u[7]||(u[7]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Название *",-1)),a(e.VInputText,{modelValue:e.form.name,"onUpdate:modelValue":u[0]||(u[0]=l=>e.form.name=l),placeholder:"Стандартные типы уплотнений",class:L(["w-full",{"p-invalid":!!e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(d(),h("small",g0,E(e.errors.name),1)):w("",!0)]),n("div",null,[u[8]||(u[8]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Описание",-1)),a(e.VTextarea,{modelValue:e.form.description,"onUpdate:modelValue":u[1]||(u[1]=l=>e.form.description=l),rows:"2",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[9]||(u[9]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Каноническое значение",-1)),a(e.VInputText,{modelValue:e.form.canonicalValue,"onUpdate:modelValue":u[2]||(u[2]=l=>e.form.canonicalValue=l),placeholder:"Например: TC (стандарт)",class:L(["w-full",{"p-invalid":!!e.errors.canonicalValue}])},null,8,["modelValue","class"]),e.errors.canonicalValue?(d(),h("small",v0,E(e.errors.canonicalValue),1)):w("",!0)]),n("div",y0,[n("div",null,[u[10]||(u[10]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Родительская группа",-1)),a(e.VSelect,{modelValue:e.form.parentId,"onUpdate:modelValue":u[3]||(u[3]=l=>e.form.parentId=l),options:e.parentSelectOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Не выбрано"},null,8,["modelValue","options"])]),n("div",null,[u[11]||(u[11]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Уровень совместимости",-1)),a(e.VSelect,{modelValue:e.form.compatibilityLevel,"onUpdate:modelValue":u[4]||(u[4]=l=>e.form.compatibilityLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[12]||(u[12]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Заметки",-1)),a(e.VInputText,{modelValue:e.form.notes,"onUpdate:modelValue":u[5]||(u[5]=l=>e.form.notes=l),placeholder:"Например: для старых спецификаций",class:"w-full"},null,8,["modelValue"])])])])]),_:1},8,["visible","header"])}const h0=te(m0,[["render",b0]]),x0=ue({__name:"SynonymValueEditor",props:{groupId:{}},setup(t,{expose:u}){u();const o=t,{attributeSynonyms:e,brands:s}=me(),r=Ve(),l=v([]),f=v(!1),c=v(""),m=v(null),I=v(null),C=v(null),B=[{label:"EXACT",value:"EXACT"},{label:"NEAR",value:"NEAR"},{label:"LEGACY",value:"LEGACY"}],A=v([]),N=async()=>{const y=await s.findMany({});A.value=Array.isArray(y)?y.map(p=>({id:p.id,name:p.name})):[]},V=y=>A.value.find(p=>p.id===y)?.name||"",F=v(!1);H([c,m,I,C],()=>{const y=(c.value||"").trim();F.value=y.length>0&&!l.value.some(p=>p.value.toLowerCase()===y.toLowerCase())});const K=async()=>{f.value=!0;try{await N();const y=await e.synonyms.findMany({groupId:o.groupId});Array.isArray(y)&&(l.value=y)}catch(y){r.error(y?.message||"Не удалось загрузить значения")}finally{f.value=!1}},M=async()=>{const y=c.value.trim();if(y){if(l.value.some(p=>p.value.toLowerCase()===y.toLowerCase())){r.error("Дубликаты не допускаются");return}try{const p=await e.synonyms.create({groupId:o.groupId,value:y,notes:m.value,brandId:C.value??void 0,compatibilityLevel:I.value??void 0});p&&typeof p=="object"&&(l.value.push(p),c.value="",m.value=null,I.value=null,C.value=null)}catch(p){r.error(p?.message||"Не удалось добавить значение")}}},G=v(!1),k=v({id:0,notes:null,brandId:null,compatibilityLevel:null}),X=y=>{k.value={id:y.id,notes:y.notes??null,brandId:y.brandId??null,compatibilityLevel:y.compatibilityLevel??null},G.value=!0},U=async()=>{try{const y=await e.synonyms.update({id:k.value.id,notes:k.value.notes,brandId:k.value.brandId??void 0,compatibilityLevel:k.value.compatibilityLevel??void 0});if(y){const p=l.value.findIndex(T=>T.id===k.value.id);p!==-1&&(l.value[p]={...l.value[p],...y}),G.value=!1}}catch(y){r.error(y?.message||"Не удалось сохранить изменения")}},R=async y=>{if(confirm("Удалить значение?"))try{await e.synonyms.delete({id:y.id}),l.value=l.value.filter(p=>p.id!==y.id)}catch(p){r.error(p?.message||"Не удалось удалить значение")}};ke(K),H(()=>o.groupId,K);const O={props:o,attributeSynonyms:e,brands:s,toast:r,synonyms:l,loading:f,newValue:c,newNotes:m,newLevel:I,newBrandId:C,compatibilityOptions:B,brandOptions:A,loadBrands:N,brandName:V,canAdd:F,load:K,addValue:M,showEdit:G,editForm:k,editRow:X,saveEdit:U,removeValue:R,VInputText:Te,VButton:Ce,VDataTable:uu,VDialog:Be,VSelect:Ne,get Column(){return tu},get PlusIcon(){return du},get PencilIcon(){return je},get TrashIcon(){return Le},DangerButton:Oe};return Object.defineProperty(O,"__isScriptSetup",{enumerable:!1,value:!0}),O}}),C0={class:"space-y-4"},k0={class:"grid grid-cols-1 md:grid-cols-5 gap-2 items-end"},D0={class:"flex gap-2"},E0={class:"space-y-3"};function w0(t,u,o,e,s,r){return d(),h("div",C0,[n("div",k0,[a(e.VInputText,{modelValue:e.newValue,"onUpdate:modelValue":u[0]||(u[0]=l=>e.newValue=l),placeholder:"Значение",class:"w-full md:col-span-1",onKeyup:Fe(e.addValue,["enter"])},null,8,["modelValue"]),a(e.VInputText,{modelValue:e.newNotes,"onUpdate:modelValue":u[1]||(u[1]=l=>e.newNotes=l),placeholder:"Заметки (опц.)",class:"w-full md:col-span-1",onKeyup:Fe(e.addValue,["enter"])},null,8,["modelValue"]),a(e.VSelect,{modelValue:e.newBrandId,"onUpdate:modelValue":u[2]||(u[2]=l=>e.newBrandId=l),options:e.brandOptions,"option-label":"name","option-value":"id",class:"w-full md:col-span-1",placeholder:"Бренд (опц.)"},null,8,["modelValue","options"]),a(e.VSelect,{modelValue:e.newLevel,"onUpdate:modelValue":u[3]||(u[3]=l=>e.newLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full md:col-span-1"},null,8,["modelValue"]),a(e.VButton,{onClick:e.addValue,disabled:!e.canAdd},{default:g(()=>[a(e.PlusIcon,{class:"w-4 h-4"})]),_:1},8,["disabled"])]),a(e.VDataTable,{value:e.synonyms,loading:e.loading,class:"p-datatable-sm","table-style":"min-width: 44rem","striped-rows":""},{default:g(()=>[a(e.Column,{field:"value",header:"Значение"}),a(e.Column,{field:"brandId",header:"Бренд"},{body:g(({data:l})=>[n("span",null,E(e.brandName(l.brandId)),1)]),_:1}),a(e.Column,{field:"compatibilityLevel",header:"Уровень"}),a(e.Column,{field:"notes",header:"Заметки"}),a(e.Column,{header:"",style:{width:"120px"}},{body:g(({data:l})=>[n("div",D0,[a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:f=>e.editRow(l)},{default:g(()=>[a(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(e.DangerButton,{size:"small",outlined:"",onClick:f=>e.removeValue(l)},{default:g(()=>[a(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading"]),a(e.VDialog,{visible:e.showEdit,"onUpdate:visible":u[8]||(u[8]=l=>e.showEdit=l),modal:"",header:"Редактировать синоним",style:{width:"28rem"}},{footer:g(()=>[a(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[7]||(u[7]=l=>e.showEdit=!1)}),a(e.VButton,{label:"Сохранить",onClick:e.saveEdit})]),default:g(()=>[n("div",E0,[a(e.VSelect,{modelValue:e.editForm.brandId,"onUpdate:modelValue":u[4]||(u[4]=l=>e.editForm.brandId=l),options:e.brandOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Бренд (опц.)"},null,8,["modelValue","options"]),a(e.VInputText,{modelValue:e.editForm.notes,"onUpdate:modelValue":u[5]||(u[5]=l=>e.editForm.notes=l),placeholder:"Заметки",class:"w-full"},null,8,["modelValue"]),a(e.VSelect,{modelValue:e.editForm.compatibilityLevel,"onUpdate:modelValue":u[6]||(u[6]=l=>e.editForm.compatibilityLevel=l),options:e.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue"])])]),_:1},8,["visible"])])}const F0=te(x0,[["render",w0]]),T0=ue({__name:"AttributeSynonymManager",props:{template:{}},setup(t,{expose:u}){u();const o=t,e=Ve(),{attributeSynonyms:s}=me(),r=v([]),l=v(0),f=v(!1),c=v(null),m=v(!1),I=v(null),C=v({}),B=async()=>{if(o.template?.id){f.value=!0;try{let T=0,S=[],q=0;for(;;){const W=await s.groups.findMany({templateId:o.template.id,limit:100,offset:T}),Y=W?.groups??[];if(q=W?.total??q,S=S.concat(Y),Y.length<100||(T+=100,q&&T>=q))break}if(r.value=S,l.value=q||S.length,c.value){const W=r.value.find(Y=>Y.id===c.value.id);W&&(c.value=W)}U()}catch(p){e.error(p?.message||"Не удалось загрузить группы")}finally{f.value=!1}}},A=p=>{switch(p){case"EXACT":return"EXACT";case"NEAR":return"NEAR";case"LEGACY":return"LEGACY";default:return String(p)}},N=p=>p==="EXACT"?"success":p==="NEAR"?"warn":"secondary",V=p=>{c.value=p,U()},F=()=>{I.value=null,m.value=!0},K=p=>{I.value=p,m.value=!0},M=async p=>{if(confirm(`Удалить группу "${p.name}"?`))try{await s.groups.delete({id:p.id}),c.value?.id===p.id&&(c.value=null),B()}catch(T){e.error(T?.message||"Не удалось удалить группу")}},G=()=>{m.value=!1,B()},k=j(()=>{const p=new Map;for(const T of r.value)p.set(T.id,T);return p}),X=j(()=>{const p=new Map,T=[];for(const S of r.value)p.set(S.id,{key:String(S.id),label:S.name,data:S,children:[]});for(const S of r.value){const q=p.get(S.id);S.parentId&&p.has(S.parentId)?p.get(S.parentId).children.push(q):T.push(q)}return T});function U(){const p={};let T=c.value?k.value.get(c.value.id):null;for(;T&&T.parentId;)p[String(T.parentId)]=!0,T=k.value.get(T.parentId);C.value=p}const R=p=>C.value[String(p.id)]===!0,O=p=>{const T=String(p.id),S={...C.value};S[T]?delete S[T]:S[T]=!0,C.value=S};ke(()=>{B()});const y={props:o,toast:e,attributeSynonyms:s,groups:r,total:l,loadingGroups:f,selectedGroup:c,showGroupDialog:m,editingGroup:I,expandedKeys:C,loadGroups:B,compatibilityLabel:A,compatibilitySeverity:N,selectGroup:V,openCreateGroup:F,openEditGroup:K,deleteGroup:M,onGroupSaved:G,idMap:k,treeNodes:X,updateExpandedForSelection:U,isGroupExpanded:R,toggleGroupExpansion:O,VCard:eu,VButton:Ce,VTag:lu,VTree:hu,EditSynonymGroupDialog:h0,SynonymValueEditor:F0,get PlusCircleIcon(){return k4},get PencilIcon(){return je},get TrashIcon(){return Le},get ChevronDownIcon(){return cu},get ChevronRightIcon(){return mu},DangerButton:Oe};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),B0={class:"flex justify-center gap-4"},V0={class:"p-4 space-y-3"},I0={class:"flex items-center justify-between"},S0=["onClick"],_0={class:"font-medium"},A0={key:0,class:"text-surface-500"},G0={class:"flex gap-2"},M0={class:"p-4 space-y-4"},K0={class:"flex items-center justify-between"},N0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},P0={key:0,class:"flex items-center gap-2 mt-1"},O0={key:0,class:"text-surface-500"},L0={key:0,class:"flex gap-2"},j0={key:0,class:"text-surface-500"},U0={key:1};function R0(t,u,o,e,s,r){return d(),h("div",B0,[a(e.VCard,null,{content:g(()=>[n("div",V0,[n("div",I0,[u[6]||(u[6]=n("h3",{class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},"Группы синонимов",-1)),a(e.VButton,{size:"small",onClick:e.openCreateGroup},{default:g(()=>[a(e.PlusCircleIcon,{class:"w-4 h-4"})]),_:1})]),a(e.VTree,{value:e.treeNodes,expandedKeys:e.expandedKeys,"onUpdate:expandedKeys":u[1]||(u[1]=l=>e.expandedKeys=l),filter:"",filterPlaceholder:"Поиск по названию / описанию..."},{default:g(({node:l})=>[n("div",{class:"flex items-center justify-between w-full",onClick:u[0]||(u[0]=Z(()=>{},["stop"]))},[n("div",{class:L(["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors",{"bg-primary text-primary-contrast":e.selectedGroup?.id===l.data.id,"hover:bg-surface-100 dark:hover:bg-surface-800":e.selectedGroup?.id!==l.data.id}]),onClick:f=>e.selectGroup(l.data)},[n("span",_0,E(l.data.name),1),l.data.description?(d(),h("small",A0,E(l.data.description),1)):w("",!0),a(e.VTag,{value:e.compatibilityLabel(l.data.compatibilityLevel),severity:e.compatibilitySeverity(l.data.compatibilityLevel)},null,8,["value","severity"]),a(e.VTag,{value:l.data._count?.synonyms||0,severity:"secondary"},null,8,["value"])],10,S0),n("div",G0,[a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Z(f=>e.openEditGroup(l.data),["stop"])},{default:g(()=>[a(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(e.VButton,{size:"small",severity:"danger",outlined:"",onClick:Z(f=>e.deleteGroup(l.data),["stop"])},{default:g(()=>[a(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])])]),_:1},8,["value","expandedKeys"])])]),_:1}),a(e.VCard,null,{content:g(()=>[n("div",M0,[n("div",K0,[n("div",null,[n("h3",N0,E(e.selectedGroup?e.selectedGroup.name:"Выберите группу"),1),e.selectedGroup?(d(),h("div",P0,[a(e.VTag,{value:e.compatibilityLabel(e.selectedGroup.compatibilityLevel),severity:e.compatibilitySeverity(e.selectedGroup.compatibilityLevel)},null,8,["value","severity"]),e.selectedGroup.notes?(d(),h("small",O0,E(e.selectedGroup.notes),1)):w("",!0)])):w("",!0)]),e.selectedGroup?(d(),h("div",L0,[a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:u[2]||(u[2]=l=>e.toggleGroupExpansion(e.selectedGroup)),title:e.isGroupExpanded(e.selectedGroup)?"Свернуть группу":"Развернуть группу"},{default:g(()=>[e.isGroupExpanded(e.selectedGroup)?(d(),D(e.ChevronDownIcon,{key:0,class:"w-4 h-4"})):(d(),D(e.ChevronRightIcon,{key:1,class:"w-4 h-4"}))]),_:1},8,["title"]),a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:u[3]||(u[3]=l=>e.openEditGroup(e.selectedGroup))},{default:g(()=>[a(e.PencilIcon,{class:"w-4 h-4"})]),_:1}),a(e.DangerButton,{size:"small",outlined:"",onClick:u[4]||(u[4]=l=>e.deleteGroup(e.selectedGroup))},{default:g(()=>[a(e.TrashIcon,{class:"w-4 h-4"})]),_:1})])):w("",!0)]),e.selectedGroup?(d(),h("div",U0,[a(e.SynonymValueEditor,{"group-id":e.selectedGroup.id},null,8,["group-id"])])):(d(),h("div",j0,"Слева выберите группу, чтобы редактировать значения."))])]),_:1}),a(e.EditSynonymGroupDialog,{visible:e.showGroupDialog,"onUpdate:visible":u[5]||(u[5]=l=>e.showGroupDialog=l),"template-id":o.template.id,group:e.editingGroup,onSaved:e.onGroupSaved},null,8,["visible","template-id","group"])])}const xu=te(T0,[["render",R0]]),z0=ue({__name:"GroupTreeAutoComplete",props:{modelValue:{},groups:{},placeholder:{default:"Поиск группы..."},class:{default:""},invalid:{type:Boolean,default:!1}},emits:["update:modelValue","group-select","group-clear"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,r=v(null),l=v([]),f=v(null),c=j(()=>{let V="w-full";return e.class&&(V+=` ${e.class}`),e.invalid&&(V+=" p-invalid"),V}),m=(V,F=0,K="")=>{const M=[];for(const G of V){const k=K?`${K} / ${G.name}`:G.name;M.push({...G,level:F,displayName:k,hasChildren:G.children&&G.children.length>0,templatesCount:G._count?.templates||0}),G.children&&G.children.length>0&&M.push(...m(G.children,F+1,k))}return M},I=async V=>{console.log("searchGroups вызван в GroupTreeAutoComplete",{event:V,groupsLength:e.groups.length});const F=V.query||"";f.value&&clearTimeout(f.value);const K=m(e.groups);if(!F.trim()){l.value=K,console.log("Показываем все группы:",l.value.length);return}f.value=setTimeout(()=>{try{l.value=K.filter(M=>M.name.toLowerCase().includes(F.toLowerCase())||M.description&&M.description.toLowerCase().includes(F.toLowerCase())||M.displayName.toLowerCase().includes(F.toLowerCase())),console.log("Фильтруем группы:",l.value.length)}catch(M){console.error("Ошибка поиска групп:",M),l.value=K}},300)},C=V=>{const F=V.value;s("update:modelValue",F.id),s("group-select",F),r.value=F},B=()=>{s("update:modelValue",null),s("group-clear"),r.value=null},A=()=>{if(e.modelValue){const V=m(e.groups);r.value=V.find(F=>F.id===e.modelValue)||null}else r.value=null};H(()=>e.modelValue,()=>{A()},{immediate:!0}),H(()=>e.groups,()=>{const V=m(e.groups);l.value=V,A()},{immediate:!0}),ke(()=>{console.log("GroupTreeAutoComplete монтируется");const V=m(e.groups);l.value=V,A(),console.log("Инициализированы группы в GroupTreeAutoComplete:",{hierarchyGroups:e.groups.length,flatGroups:l.value.length})}),i4(()=>{f.value&&clearTimeout(f.value)});const N={props:e,emit:s,selectedGroup:r,filteredGroups:l,searchTimeout:f,inputClass:c,flattenGroupsHierarchy:m,searchGroups:I,onGroupSelect:C,onGroupClear:B,findSelectedGroup:A,VAutoComplete:Pe,get FolderIcon(){return pu},get FileIcon(){return D4}};return Object.defineProperty(N,"__isScriptSetup",{enumerable:!1,value:!0}),N}}),q0={class:"group-tree-autocomplete"},H0={class:"flex items-center gap-2 py-1"},X0={class:"flex-1"},W0={key:2,class:"text-xs text-surface-500 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded"};function Y0(t,u,o,e,s,r){return d(),h("div",q0,[a(e.VAutoComplete,{modelValue:e.selectedGroup,"onUpdate:modelValue":u[0]||(u[0]=l=>e.selectedGroup=l),suggestions:e.filteredGroups,onComplete:e.searchGroups,onDropdownClick:u[1]||(u[1]=()=>e.searchGroups({query:""})),"option-label":"displayName",placeholder:o.placeholder,class:L(e.inputClass),dropdown:"","dropdown-mode":"current","show-clear":"",onItemSelect:e.onGroupSelect,onClear:e.onGroupClear},{option:g(l=>[n("div",H0,[n("div",{style:y4({paddingLeft:`${l.option.level*16}px`}),class:"flex items-center gap-2 w-full"},[l.option.hasChildren?(d(),D(e.FolderIcon,{key:0,class:"w-4 h-4 text-surface-500"})):(d(),D(e.FileIcon,{key:1,class:"w-4 h-4 text-surface-400"})),n("span",X0,E(l.option.name),1),l.option.templatesCount>0?(d(),h("span",W0,E(l.option.templatesCount),1)):w("",!0)],4)])]),empty:g(()=>u[2]||(u[2]=[n("div",{class:"p-3 text-surface-500 text-center"}," Группы не найдены ",-1)])),_:1},8,["modelValue","suggestions","placeholder","class"])])}const J0=te(z0,[["render",Y0],["__scopeId","data-v-be3fab62"]]),Q0=ue({__name:"TemplateForm",props:{modelValue:{},groups:{},hierarchyGroups:{default:()=>[]},loading:{type:Boolean,default:!1}},emits:["update:modelValue","save","cancel","group-created"],setup(t,{expose:u,emit:o}){u();const e=t,s=o,{attributeTemplates:r}=me(),l=Ve(),{userRole:f}=b4(),c=j({get:()=>e.modelValue,set:x=>s("update:modelValue",x)}),m=v({}),I=v(!1),C=v(!1),B=v({name:"",description:""}),A=v(!1),N=()=>{if(!c.value.id){l.info("Сначала сохраните шаблон");return}A.value=!0},V=v(null),F=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],K=[{label:"мм",value:"MM"},{label:"дюймы",value:"INCH"},{label:"футы",value:"FT"},{label:"г",value:"G"},{label:"кг",value:"KG"},{label:"т",value:"T"},{label:"фунты",value:"LB"},{label:"мл",value:"ML"},{label:"л",value:"L"},{label:"галлоны",value:"GAL"},{label:"шт",value:"PCS"},{label:"комплект",value:"SET"},{label:"пара",value:"PAIR"},{label:"бар",value:"BAR"},{label:"PSI",value:"PSI"},{label:"кВт",value:"KW"},{label:"л.с.",value:"HP"},{label:"Н⋅м",value:"NM"},{label:"об/мин",value:"RPM"},{label:"°C",value:"C"},{label:"°F",value:"F"},{label:"%",value:"PERCENT"}],M=v(F),G=v(K),k=j({get:()=>F.find(x=>x.value===c.value.dataType)||null,set:x=>{c.value.dataType=x?x.value:null}}),X=j({get:()=>K.find(x=>x.value===c.value.unit)||null,set:x=>{c.value.unit=x?x.value:null}}),U=x=>{console.log("filterDataTypes вызван в TemplateForm",{event:x});const J=x.query?.toLowerCase()||"";J.trim()?(M.value=F.filter(oe=>oe.label.toLowerCase().includes(J)),console.log("Фильтруем типы данных:",M.value.length)):(M.value=[...F],console.log("Показываем все типы данных:",M.value.length))},R=x=>{console.log("filterUnits вызван в TemplateForm",{event:x});const J=x.query?.toLowerCase()||"";J.trim()?(G.value=K.filter(oe=>oe.label.toLowerCase().includes(J)),console.log("Фильтруем единицы:",G.value.length)):(G.value=[...K],console.log("Показываем все единицы:",G.value.length))},O=()=>{c.value.groupId?V.value=e.groups.find(x=>x.id===c.value.groupId)||null:V.value=null},y=j(()=>!!c.value.id),p=j(()=>c.value.name&&c.value.title&&c.value.dataType&&!Object.keys(m.value).length),T=j(()=>`w-full ${m.value.dataType?"p-invalid":""}`),S=()=>{m.value={},c.value.name?/^[a-z0-9_]+$/.test(c.value.name)||(m.value.name="Только строчные буквы, цифры и подчеркивания"):m.value.name="Системное имя обязательно",c.value.title||(m.value.title="Отображаемое название обязательно"),c.value.dataType||(m.value.dataType="Тип данных обязателен")},q=()=>{S(),p.value?s("save",c.value):l.error("Пожалуйста, исправьте ошибки в форме")},W=x=>{l.info(`Выбрана группа: ${x.name}`)},Y=()=>{l.info("Группа сброшена")},ge=async()=>{if(!B.value.name){l.error("Введите название группы");return}try{C.value=!0,l.info("Создание группы...");const x=await r.createGroup(B.value);x&&typeof x=="object"&&"id"in x&&(c.value.groupId=x.id,V.value=x,I.value=!1,B.value={name:"",description:""},s("group-created",x))}catch(x){console.error("Ошибка создания группы:",x),x.message?.includes("уже существует")?l.error("Группа с таким названием уже существует"):l.error(x.message||"Не удалось создать группу")}finally{C.value=!1}};H(()=>e.modelValue,x=>{if(!x){c.value={dataType:"STRING",isRequired:!1,allowedValues:[]};return}x.dataType||(c.value.dataType="STRING"),x.isRequired||(c.value.isRequired=!1),x.allowedValues||(c.value.allowedValues=[]),O()},{immediate:!0}),H(()=>e.groups,()=>{O()},{immediate:!0}),ke(()=>{console.log("TemplateForm монтируется"),M.value=[...F],G.value=[...K],console.log("Инициализированы автокомплиты в TemplateForm:",{dataTypes:M.value.length,units:G.value.length,groups:e.groups.length,hierarchyGroups:e.hierarchyGroups.length}),O()}),H(()=>c.value.name,()=>{m.value.name&&delete m.value.name}),H(()=>c.value.title,()=>{m.value.title&&delete m.value.title}),H(()=>c.value.dataType,()=>{m.value.dataType&&delete m.value.dataType});const le={props:e,emit:s,attributeTemplates:r,toast:l,userRole:f,form:c,errors:m,showCreateGroupDialog:I,creatingGroup:C,newGroupForm:B,showSynonymsDialog:A,openSynonyms:N,selectedGroup:V,dataTypeOptions:F,unitOptions:K,filteredDataTypeOptions:M,filteredUnitOptions:G,selectedDataType:k,selectedUnit:X,filterDataTypes:U,filterUnits:R,findSelectedGroup:O,isEditing:y,isValid:p,dataTypeClass:T,validateForm:S,save:q,onGroupSelect:W,onGroupClear:Y,createGroup:ge,VInputText:Te,VTextarea:Ke,VInputNumber:h4,InputChips:f0,VCheckbox:x4,VButton:Ce,VDialog:Be,VAutoComplete:Pe,AttributeSynonymManager:xu,GroupTreeAutoComplete:J0,get PlusIcon(){return du},get TagsIcon(){return fu}};return Object.defineProperty(le,"__isScriptSetup",{enumerable:!1,value:!0}),le}}),Z0={class:"template-form"},$0={class:"space-y-4"},et={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ut={key:0,class:"p-error"},tt={key:0,class:"p-error"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ot={key:0,class:"p-error"},nt={class:"flex gap-2"},at={key:0,class:"p-error"},rt={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},it={key:1},st={key:2},dt={class:"mt-3"},ct={class:"flex items-center gap-4"},ft={class:"flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700"},mt={class:"space-y-4"},pt={key:1,class:"p-4 text-surface-500"};function gt(t,u,o,e,s,r){return d(),h("div",Z0,[n("div",$0,[n("div",et,[n("div",null,[u[20]||(u[20]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Системное имя * ",-1)),a(e.VInputText,{modelValue:e.form.name,"onUpdate:modelValue":u[0]||(u[0]=l=>e.form.name=l),placeholder:"inner_diameter",class:L(["w-full",{"p-invalid":e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(d(),h("small",ut,E(e.errors.name),1)):w("",!0),u[21]||(u[21]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Только строчные буквы, цифры и подчеркивания ",-1))]),n("div",null,[u[22]||(u[22]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Отображаемое название * ",-1)),a(e.VInputText,{modelValue:e.form.title,"onUpdate:modelValue":u[1]||(u[1]=l=>e.form.title=l),placeholder:"Внутренний диаметр",class:L(["w-full",{"p-invalid":e.errors.title}])},null,8,["modelValue","class"]),e.errors.title?(d(),h("small",tt,E(e.errors.title),1)):w("",!0)])]),n("div",null,[u[23]||(u[23]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),a(e.VTextarea,{modelValue:e.form.description,"onUpdate:modelValue":u[2]||(u[2]=l=>e.form.description=l),placeholder:"Подробное описание атрибута...",rows:"3",class:"w-full"},null,8,["modelValue"])]),n("div",lt,[n("div",null,[u[24]||(u[24]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип данных * ",-1)),a(e.VAutoComplete,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[3]||(u[3]=l=>e.selectedDataType=l),suggestions:e.filteredDataTypeOptions,onComplete:e.filterDataTypes,onDropdownClick:u[4]||(u[4]=()=>e.filterDataTypes({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите тип",class:L(e.dataTypeClass),dropdown:""},null,8,["modelValue","suggestions","class"]),e.errors.dataType?(d(),h("small",ot,E(e.errors.dataType),1)):w("",!0)]),n("div",null,[u[25]||(u[25]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Единица измерения ",-1)),a(e.VAutoComplete,{modelValue:e.selectedUnit,"onUpdate:modelValue":u[5]||(u[5]=l=>e.selectedUnit=l),suggestions:e.filteredUnitOptions,onComplete:e.filterUnits,onDropdownClick:u[6]||(u[6]=()=>e.filterUnits({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите единицу",class:"w-full",dropdown:"","show-clear":""},null,8,["modelValue","suggestions"])])]),n("div",null,[u[27]||(u[27]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа ",-1)),n("div",nt,[a(e.GroupTreeAutoComplete,{modelValue:e.form.groupId,"onUpdate:modelValue":u[7]||(u[7]=l=>e.form.groupId=l),groups:o.hierarchyGroups,placeholder:"Поиск группы...",class:"flex-1",invalid:!!e.errors.groupId,onGroupSelect:e.onGroupSelect,onGroupClear:e.onGroupClear},null,8,["modelValue","groups","invalid"]),a(e.VButton,{onClick:u[8]||(u[8]=l=>e.showCreateGroupDialog=!0),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[u[26]||(u[26]=fe(" Создать ")),a(e.PlusIcon,{class:"w-5 h-5"})]),_:1,__:[26]})]),e.errors.groupId?(d(),h("small",at,E(e.errors.groupId),1)):w("",!0)]),e.form.dataType==="NUMBER"?(d(),h("div",rt,[n("div",null,[u[28]||(u[28]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Минимальное значение ",-1)),a(e.VInputNumber,{modelValue:e.form.minValue,"onUpdate:modelValue":u[9]||(u[9]=l=>e.form.minValue=l),placeholder:"0",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])]),n("div",null,[u[29]||(u[29]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Максимальное значение ",-1)),a(e.VInputNumber,{modelValue:e.form.maxValue,"onUpdate:modelValue":u[10]||(u[10]=l=>e.form.maxValue=l),placeholder:"100",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])])])):w("",!0),e.form.dataType==="NUMBER"?(d(),h("div",it,[u[30]||(u[30]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимое отклонение (tolerance) ",-1)),a(e.VInputNumber,{modelValue:e.form.tolerance,"onUpdate:modelValue":u[11]||(u[11]=l=>e.form.tolerance=l),placeholder:"0.1",class:"w-full","use-grouping":!1,"min-fraction-digits":1,"max-fraction-digits":4,min:0},null,8,["modelValue"]),u[31]||(u[31]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Допустимое отклонение при сопоставлении числовых значений. Например: если эталон = 30.0 и допуск = 0.1, то значения от 29.9 до 30.1 будут считаться эквивалентными. ",-1))])):w("",!0),e.form.dataType==="STRING"?(d(),h("div",st,[u[34]||(u[34]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимые значения ",-1)),a(e.InputChips,{modelValue:e.form.allowedValues,"onUpdate:modelValue":u[12]||(u[12]=l=>e.form.allowedValues=l),placeholder:"Введите значение и нажмите Enter",class:"w-full"},null,8,["modelValue"]),u[35]||(u[35]=n("small",{class:"text-surface-500 dark:text-surface-400"}," Оставьте пустым для любых значений. Например: steel, aluminum, plastic ",-1)),n("div",dt,[a(e.VButton,{severity:"secondary",outlined:"",onClick:e.openSynonyms},{default:g(()=>[u[32]||(u[32]=fe(" Синонимы ")),a(e.TagsIcon,{class:"w-5 h-5"})]),_:1,__:[32]}),u[33]||(u[33]=n("small",{class:"ml-2 text-surface-500"},"Доступно после сохранения шаблона",-1))])])):w("",!0),n("div",ct,[a(e.VCheckbox,{modelValue:e.form.isRequired,"onUpdate:modelValue":u[13]||(u[13]=l=>e.form.isRequired=l),"input-id":"required",binary:""},null,8,["modelValue"]),u[36]||(u[36]=n("label",{for:"required",class:"text-sm text-surface-700 dark:text-surface-300"}," Обязательный атрибут ",-1))])]),n("div",ft,[a(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[14]||(u[14]=l=>t.$emit("cancel"))}),a(e.VButton,{label:e.isEditing?"Обновить":"Создать",onClick:e.save,loading:o.loading,disabled:!e.isValid},null,8,["label","loading","disabled"])]),a(e.VDialog,{visible:e.showCreateGroupDialog,"onUpdate:visible":u[18]||(u[18]=l=>e.showCreateGroupDialog=l),modal:"",header:"Создать группу атрибутов",style:{width:"30rem"}},{footer:g(()=>[a(e.VButton,{label:"Отмена",severity:"secondary",onClick:u[17]||(u[17]=l=>e.showCreateGroupDialog=!1)}),a(e.VButton,{label:"Создать",onClick:e.createGroup,loading:e.creatingGroup,disabled:!e.newGroupForm.name},null,8,["loading","disabled"])]),default:g(()=>[n("div",mt,[n("div",null,[u[37]||(u[37]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название группы * ",-1)),a(e.VInputText,{modelValue:e.newGroupForm.name,"onUpdate:modelValue":u[15]||(u[15]=l=>e.newGroupForm.name=l),placeholder:"Размеры",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[38]||(u[38]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),a(e.VTextarea,{modelValue:e.newGroupForm.description,"onUpdate:modelValue":u[16]||(u[16]=l=>e.newGroupForm.description=l),placeholder:"Описание группы...",rows:"2",class:"w-full"},null,8,["modelValue"])])])]),_:1},8,["visible"]),a(e.VDialog,{visible:e.showSynonymsDialog,"onUpdate:visible":u[19]||(u[19]=l=>e.showSynonymsDialog=l),modal:"",header:"Синонимы значения",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:g(()=>[e.form.id&&e.form.dataType==="STRING"?(d(),D(e.AttributeSynonymManager,{key:0,template:{id:e.form.id,dataType:e.form.dataType,title:e.form.title,name:e.form.name}},null,8,["template"])):(d(),h("div",pt,"Сохраните шаблон, чтобы управлять синонимами."))]),_:1},8,["visible"])])}const vt=te(Q0,[["render",gt]]),yt=ue({__name:"AttributeTemplateManager",setup(t,{expose:u}){u();const{attributeTemplates:o,loading:e}=me(),s=v([]),r=v([]),l=v([]),f=v(0),c=v(25),m=v(0),I=v(!1);let C=0;const B=v(""),A=v(null),N=v(null),V=v("table"),F=Qu({search:"",groupId:void 0,dataType:void 0},{prefix:"attr_",numberParams:["groupId"],debounceMs:300});H(B,i=>{F.updateFilter("search",i||void 0)}),H(A,i=>{const b=i&&typeof i=="object"?i.id:i;F.updateFilter("groupId",b??void 0)}),H(N,i=>{const b=i&&typeof i=="object"?i.value:i;F.updateFilter("dataType",b??void 0)}),H(F.filters,i=>{const b=i.search||"",_=i.groupId??null,P=i.dataType??null;B.value!==b&&(B.value=b),A.value!==_&&(A.value=_),N.value!==P&&(N.value=P),m.value=0,$()});const K=v(!1),M=v(null),G=v({}),k=v(!1),X=v(!1),U=v(null),R=v(!1),O=v(null),y=v({name:"",description:"",parentId:null}),p=v({}),T=v(!1),S=v(null),q=v({}),W=v(!1),Y=v([]),ge=v(!1),le=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],x=[{label:"Таблица",value:"table"},{label:"Карточки",value:"cards"}],J=v([]),oe=v([]),De=v([]),Cu=i=>{const b=i.query?.toLowerCase()||"",_={id:null,name:"Без группы",isSpecial:!0};if(!b.trim())J.value=[_,...r.value];else{const P=r.value.filter(re=>re.name.toLowerCase().includes(b)),Q=b.includes("без")||b.includes("группы")||b.includes("группа")?[_,...P]:P;J.value=Q}},ku=i=>{const b=i.query?.toLowerCase()||"";b.trim()?oe.value=le.filter(_=>_.label.toLowerCase().includes(b)):oe.value=[...le]},Du=i=>{const b=i.query?.toLowerCase()||"";b.trim()?De.value=x.filter(_=>_.label.toLowerCase().includes(b)):De.value=[...x]},Eu=j(()=>s.value.filter(i=>Ee(i._count)>0).length),wu=j(()=>s.value.filter(i=>Ee(i._count)===0).length),Ie=j(()=>{const i=new Map;for(const b of r.value)i.set(b.id,b);return i}),Fu=j(()=>{const i=new Map,b=[],_={key:"no-group",label:"Без группы",data:{id:null,name:"Без группы",isSpecial:!0},children:[]};b.push(_);for(const P of r.value)i.set(P.id,{key:String(P.id),label:P.name,data:P,children:[]});for(const P of r.value){const Q=i.get(P.id);P.parentId&&i.has(P.parentId)?i.get(P.parentId).children.push(Q):b.push(Q)}return b}),Tu=j(()=>[{id:null,name:"Нет родителя"},...r.value.filter(i=>!O.value||i.id!==O.value.id)]),Bu=i=>le.find(_=>_.value===i)?.label||i,Vu=i=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[i]||i,Ee=i=>i?(i.partAttributes||0)+(i.catalogItemAttributes||0)+(i.equipmentAttributes||0):0,Ue=i=>{if(!i)return"";const b=i.partAttributes||0,_=i.catalogItemAttributes||0,P=i.equipmentAttributes||0,Q=[];return b>0&&Q.push(`${b} зап.`),_>0&&Q.push(`${_} кат.`),P>0&&Q.push(`${P} тех.`),Q.join(", ")},$=async()=>{const i=++C;I.value=!0;try{let b;A.value&&(typeof A.value=="object"?b=A.value.id:b=A.value);const _=N.value&&typeof N.value=="object"?N.value.value:void 0,P=await o.findMany({groupId:b,search:B.value||void 0,dataType:_,limit:c.value,offset:m.value*c.value});i===C&&P&&typeof P=="object"&&(s.value=P.templates||[],f.value=P.total||0)}catch(b){console.error("Ошибка загрузки шаблонов:",b),console.error("Не удалось загрузить шаблоны")}finally{i===C&&(I.value=!1)}},ne=async()=>{try{const[i,b]=await Promise.all([o.findAllGroups(),o.findGroupsHierarchy()]);if(i&&Array.isArray(i)){r.value=i;const _={id:null,name:"Без группы",isSpecial:!0};J.value=[_,...i]}b&&Array.isArray(b)&&(l.value=b)}catch(i){console.error("Ошибка загрузки групп:",i)}};let we;const Iu=()=>{clearTimeout(we),we=setTimeout(()=>{m.value=0,F.updateFilter("search",B.value||""),$()},500)},Su=async()=>{await ne(),await $()},_u=i=>{m.value=i.page,c.value=i.rows,$()},Au=i=>{M.value=i,G.value={...i},K.value=!0},Gu=i=>{U.value=i,X.value=!0},Mu=()=>{M.value=null,G.value={dataType:"STRING",isRequired:!1,allowedValues:[]},K.value=!0},Ku=async i=>{if(window.confirm(`Вы уверены, что хотите удалить шаблон "${i.title}"?`))try{await o.delete({id:i.id}),console.log("Шаблон успешно удален"),$()}catch(_){console.error("Ошибка удаления шаблона:",_),alert(_.message||"Не удалось удалить шаблон")}},Nu=async i=>{const b=Ee(i._count),_=Ue(i._count),P=`ВНИМАНИЕ! Вы собираетесь принудительно удалить шаблон атрибута "${i.title}".

Это действие удалит:
• Сам шаблон атрибута
• Все связанные атрибуты (${b} записей):
  ${_}

ДАННОЕ ДЕЙСТВИЕ НЕОБРАТИМО!

Вы действительно хотите продолжить?`;if(window.confirm(P))try{const re=await o.forceDelete({id:i.id});console.log("Шаблон успешно удален с отвязкой всех связей:",re),$();const ce=re.deletedCounts,Xu=ce.partAttributes+ce.catalogItemAttributes+ce.equipmentAttributes;alert(`Шаблон "${i.title}" успешно удален.

Было удалено атрибутов:
• Запчасти: ${ce.partAttributes}
• Каталожные позиции: ${ce.catalogItemAttributes}
• Техника: ${ce.equipmentAttributes}

Всего: ${Xu} записей`)}catch(re){console.error("Ошибка принудительного удаления шаблона:",re),alert(re.message||"Не удалось принудительно удалить шаблон")}},Pu=async i=>{try{k.value=!0,M.value?(await o.update({id:M.value.id,...i}),console.log("Шаблон успешно обновлен")):(await o.create(i),console.log("Шаблон успешно создан")),K.value=!1,M.value=null,G.value={},await ne(),await $()}catch(b){console.error("Ошибка сохранения шаблона:",b),alert(b.message||"Не удалось сохранить шаблон")}finally{k.value=!1}};ke(async()=>{oe.value=[...le],De.value=[...x];const i=F.filters.value;B.value=i.search||"",A.value=i.groupId??null,N.value=i.dataType??null,await ne(),await $();const b=r.value.find(_=>!_.parentId);b&&(S.value=b,Se())});const Ou=i=>{S.value=i,Se(),W.value=!1,Y.value=[]},Lu=i=>q.value[String(i.id)]===!0,ju=i=>{const b=String(i.id),_={...q.value};_[b]?delete _[b]:_[b]=!0,q.value=_},Se=()=>{const i={};let b=S.value?Ie.value.get(S.value.id):null;for(;b&&b.parentId;)i[String(b.parentId)]=!0,b=Ie.value.get(b.parentId);q.value=i},Uu=async()=>{await ne()},Ru=i=>{O.value=i,y.value={name:i.name,description:i.description||"",parentId:i.parentId??null},R.value=!0},zu=async i=>{if(confirm(`Удалить группу "${i.name}"?`))try{await o.deleteGroup({id:i.id}),await ne(),S.value?.id===i.id&&(S.value=null)}catch(b){console.error("Ошибка удаления группы:",b)}},qu=async()=>{if(!y.value.name.trim()){p.value.name="Название обязательно";return}T.value=!0,p.value={};try{O.value?await o.updateGroup({id:O.value.id,name:y.value.name.trim(),description:y.value.description||void 0,parentId:y.value.parentId}):await o.createGroup({name:y.value.name.trim(),description:y.value.description||void 0,parentId:y.value.parentId}),Re(),await ne()}catch(i){i.message?.includes("уже существует")?p.value.name="Группа с таким именем уже существует":console.error("Ошибка сохранения группы:",i)}finally{T.value=!1}},Hu=async()=>{await ne()},Re=()=>{R.value=!1,O.value=null,y.value={name:"",description:"",parentId:null},p.value={}},ze={attributeTemplates:o,loading:e,templates:s,groups:r,hierarchyGroups:l,totalCount:f,pageSize:c,currentPage:m,tableLoading:I,get lastRequestId(){return C},set lastRequestId(i){C=i},searchQuery:B,selectedGroup:A,selectedDataType:N,viewMode:V,urlSync:F,showCreateDialog:K,editingTemplate:M,templateForm:G,saving:k,showSynonymsDialog:X,selectedTemplateForSynonyms:U,showGroupDialog:R,editingGroup:O,groupForm:y,groupErrors:p,savingGroup:T,selectedTreeGroup:S,expandedKeys:q,showGroupTemplates:W,groupTemplates:Y,loadingGroupTemplates:ge,dataTypeOptions:le,viewModeOptions:x,groupSuggestions:J,dataTypeSuggestions:oe,viewModeSuggestions:De,filterGroups:Cu,filterDataTypes:ku,filterViewModes:Du,usedTemplatesCount:Eu,unusedTemplatesCount:wu,idMap:Ie,treeNodes:Fu,parentSelectOptions:Tu,getDataTypeLabel:Bu,getUnitLabel:Vu,getTotalUsage:Ee,getUsageDetails:Ue,loadTemplates:$,loadGroups:ne,get searchTimeout(){return we},set searchTimeout(i){we=i},debouncedSearch:Iu,refreshData:Su,onPageChange:_u,editTemplate:Au,openSynonyms:Gu,createNewTemplate:Mu,deleteTemplate:Ku,forceDeleteTemplate:Nu,saveTemplate:Pu,selectTreeGroup:Ou,isGroupExpanded:Lu,toggleGroupExpansion:ju,updateExpandedForSelection:Se,refreshGroupData:Uu,editGroup:Ru,deleteGroup:zu,saveGroup:qu,onGroupCreated:Hu,closeGroupDialog:Re,toggleGroupTemplates:async()=>{if(S.value&&(W.value=!W.value,W.value&&Y.value.length===0)){ge.value=!0;try{const i=await o.findMany({groupId:S.value.id,limit:100});Y.value=i?.templates||[]}catch(i){console.error("Ошибка загрузки шаблонов группы:",i)}finally{ge.value=!1}}},VCard:eu,VButton:Ce,VInputText:Te,VTextarea:Ke,VSelect:Ne,VDataTable:uu,VTag:lu,VDialog:Be,VTree:hu,get Column(){return tu},get Paginator(){return Zu},TemplateForm:vt,VAutoComplete:Pe,AttributeSynonymManager:xu,Icon:C4,get TagsIcon(){return fu},get PencilIcon(){return je},get TrashIcon(){return Le},get RefreshCwIcon(){return Wu},get TagIcon(){return F4},get PencilLineIcon(){return w4},get LoaderCircleIcon(){return E4},get FolderIcon(){return pu},get ChevronDownIcon(){return cu},get ChevronRightIcon(){return mu},DangerButton:Oe};return Object.defineProperty(ze,"__isScriptSetup",{enumerable:!1,value:!0}),ze}}),bt={class:"attribute-template-manager"},ht={key:0,class:"py-12 text-center"},xt={key:1},Ct={class:"mb-6 flex items-center justify-between"},kt={class:"flex gap-3"},Dt={class:"mb-6 grid grid-cols-1 gap-4 lg:grid-cols-3"},Et={class:"p-4 space-y-3"},wt={class:"flex items-center justify-between"},Ft=["onClick"],Tt={class:"font-medium"},Bt={class:"flex gap-1"},Vt={class:"p-4 space-y-3"},It={class:"flex items-center justify-between"},St={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},_t={key:0,class:"flex gap-2"},At={key:0,class:"text-surface-500 text-center py-8"},Gt={key:1,class:"space-y-4"},Mt={class:"flex items-center gap-3"},Kt={class:"flex-1"},Nt={class:"font-medium"},Pt={key:0,class:"text-sm text-surface-500"},Ot={key:0,class:"border-t border-surface-200 dark:border-surface-700 pt-4"},Lt={class:"flex items-center justify-between mb-3"},jt={key:0,class:"space-y-2"},Ut={key:0,class:"grid gap-2"},Rt={class:"flex items-center gap-3"},zt={class:"font-medium text-surface-900 dark:text-surface-0"},qt={class:"text-sm text-surface-600 dark:text-surface-400"},Ht={class:"flex gap-1"},Xt={key:1,class:"text-center py-4"},Wt={key:2,class:"text-center py-4 text-surface-500"},Yt={class:"p-4"},Jt={class:"grid grid-cols-1 gap-4 md:grid-cols-4"},Qt={class:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-4"},Zt={class:"p-4 text-center"},$t={class:"text-primary mb-2 text-2xl font-bold"},el={class:"p-4 text-center"},ul={class:"mb-2 text-2xl font-bold text-green-600"},tl={class:"p-4 text-center"},ll={class:"mb-2 text-2xl font-bold text-blue-600"},ol={class:"p-4 text-center"},nl={class:"mb-2 text-2xl font-bold text-orange-600"},al={class:"text-surface-700 dark:text-surface-300 font-mono text-sm"},rl={class:"text-surface-900 dark:text-surface-0 font-medium"},il={class:"text-surface-600 dark:text-surface-400 font-mono text-sm"},sl={key:1,class:"text-surface-400 dark:text-surface-600"},dl={key:1,class:"text-surface-400 dark:text-surface-600"},cl={class:"text-sm"},fl={key:0},ml={class:"text-surface-700 dark:text-surface-300"},pl={class:"text-surface-500 dark:text-surface-400 text-xs"},gl={key:1,class:"text-surface-400 dark:text-surface-600"},vl={class:"flex gap-2"},yl={key:1,class:"grid gap-4"},bl={class:"p-6"},hl={class:"mb-4 flex items-start justify-between"},xl={class:"flex-1"},Cl={class:"mb-2 flex items-center gap-3"},kl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Dl={class:"text-surface-600 dark:text-surface-400 mb-2 font-mono text-sm"},El={key:0,class:"text-surface-600 dark:text-surface-400 mb-3"},wl={class:"ml-4 flex gap-2"},Fl={class:"mb-4 flex items-center gap-4"},Tl={key:0,class:"border-surface-200 dark:border-surface-700 border-t pt-4"},Bl={class:"grid grid-cols-3 gap-4 text-center"},Vl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Il={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},Sl={class:"text-surface-900 dark:text-surface-0 text-lg font-semibold"},_l={class:"p-4"},Al={class:"space-y-4"},Gl={key:0,class:"p-error"},Ml={class:"flex justify-end gap-2"};function Kl(t,u,o,e,s,r){return d(),h("div",bt,[e.loading?(d(),h("div",ht,[a(e.Icon,{name:"pi pi-spinner pi-spin",class:"text-primary mb-4 inline-block text-4xl"}),u[15]||(u[15]=n("p",{class:"text-surface-600 dark:text-surface-400"},"Загрузка шаблонов атрибутов...",-1))])):(d(),h("div",xt,[n("div",Ct,[u[16]||(u[16]=n("div",null,[n("h2",{class:"text-surface-900 dark:text-surface-0 text-xl font-semibold"},"Управление атрибутами"),n("p",{class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},"Группы и шаблоны атрибутов для запчастей, каталожных позиций и техники")],-1)),n("div",kt,[a(e.VButton,{onClick:e.refreshData,disabled:e.loading,severity:"secondary",outlined:"",label:"Обновить"},null,8,["disabled"]),a(e.VButton,{onClick:u[0]||(u[0]=l=>e.showGroupDialog=!0),severity:"secondary",outlined:"",label:"Создать группу"}),a(e.VButton,{onClick:e.createNewTemplate,label:"Создать шаблон"})])]),n("div",Dt,[a(e.VCard,null,{content:g(()=>[n("div",Et,[n("div",wt,[u[17]||(u[17]=n("h3",{class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},"Дерево групп",-1)),a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:e.refreshGroupData},{default:g(()=>[a(e.RefreshCwIcon,{class:"w-4 h-4"})]),_:1})]),a(e.VTree,{value:e.treeNodes,expandedKeys:e.expandedKeys,"onUpdate:expandedKeys":u[2]||(u[2]=l=>e.expandedKeys=l),filter:"",filterPlaceholder:"Поиск групп..."},{default:g(({node:l})=>[n("div",{class:"flex items-center justify-between w-full",onClick:u[1]||(u[1]=Z(()=>{},["stop"]))},[n("div",{class:L(["flex items-center gap-2 flex-1 cursor-pointer rounded px-2 py-1 transition-colors",{"bg-primary text-primary-contrast":e.selectedTreeGroup?.id===l.data.id,"hover:bg-surface-100 dark:hover:bg-surface-800":e.selectedTreeGroup?.id!==l.data.id}]),onClick:f=>e.selectTreeGroup(l.data)},[n("span",Tt,E(l.data.name),1),a(e.VTag,{value:l.data._count?.templates||0,severity:"secondary",size:"small"},null,8,["value"])],10,Ft),n("div",Bt,[a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Z(f=>e.toggleGroupExpansion(l.data),["stop"]),title:e.isGroupExpanded(l.data)?"Свернуть группу":"Развернуть группу"},{default:g(()=>[e.isGroupExpanded(l.data)?(d(),D(e.ChevronDownIcon,{key:0,class:"w-3 h-3"})):(d(),D(e.ChevronRightIcon,{key:1,class:"w-3 h-3"}))]),_:2},1032,["onClick","title"]),a(e.VButton,{size:"small",severity:"secondary",outlined:"",onClick:Z(f=>e.editGroup(l.data),["stop"])},{default:g(()=>[a(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),a(e.VButton,{size:"small",severity:"danger",outlined:"",onClick:Z(f=>e.deleteGroup(l.data),["stop"]),disabled:(l.data._count?.templates||0)>0||(l.data._count?.children||0)>0},{default:g(()=>[a(e.TrashIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick","disabled"])])])]),_:1},8,["value","expandedKeys"])])]),_:1}),a(e.VCard,{class:"lg:col-span-2"},{content:g(()=>[n("div",Vt,[n("div",It,[n("h3",St,E(e.selectedTreeGroup?`Группа: ${e.selectedTreeGroup.name}`:"Выберите группу"),1),e.selectedTreeGroup?(d(),h("div",_t,[a(e.VButton,{outlined:"",onClick:u[3]||(u[3]=l=>e.editGroup(e.selectedTreeGroup))},{default:g(()=>[a(e.PencilLineIcon,{class:"w-4 h-4"}),u[18]||(u[18]=fe(" Редактировать "))]),_:1,__:[18]})])):w("",!0)]),e.selectedTreeGroup?(d(),h("div",Gt,[n("div",Mt,[a(e.FolderIcon,{class:"text-blue-600 w-5 h-5"}),n("div",Kt,[n("div",Nt,E(e.selectedTreeGroup.name),1),e.selectedTreeGroup.description?(d(),h("div",Pt,E(e.selectedTreeGroup.description),1)):w("",!0)]),a(e.VTag,{value:`${e.selectedTreeGroup._count?.templates||0} шаблонов`,severity:"secondary"},null,8,["value"]),e.selectedTreeGroup._count?.children?(d(),D(e.VTag,{key:0,value:`${e.selectedTreeGroup._count.children} дочерних`,severity:"info"},null,8,["value"])):w("",!0)]),e.selectedTreeGroup._count?.templates>0?(d(),h("div",Ot,[n("div",Lt,[u[19]||(u[19]=n("span",{class:"text-sm font-medium"},"Шаблоны в группе:",-1)),a(e.VButton,{label:e.showGroupTemplates?"Скрыть":"Показать",onClick:e.toggleGroupTemplates,severity:"secondary",text:"",size:"small"},null,8,["label"])]),e.showGroupTemplates?(d(),h("div",jt,[e.groupTemplates.length>0?(d(),h("div",Ut,[(d(!0),h(ae,null,pe(e.groupTemplates,l=>(d(),h("div",{key:l.id,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg"},[n("div",Rt,[a(e.TagIcon,{class:"text-green-600 w-4 h-4"}),n("div",null,[n("div",zt,E(l.title),1),n("div",qt,E(l.name)+" • "+E(e.getDataTypeLabel(l.dataType)),1)])]),n("div",Ht,[a(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info",size:"small"},null,8,["value"]),l.unit?(d(),D(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success",size:"small"},null,8,["value"])):w("",!0)])]))),128))])):e.loadingGroupTemplates?(d(),h("div",Xt,[a(e.LoaderCircleIcon,{class:"w-4 h-4 animate-spin"}),u[20]||(u[20]=fe(" Загрузка... "))])):(d(),h("div",Wt," Нет шаблонов в группе "))])):w("",!0)])):w("",!0)])):(d(),h("div",At," Выберите группу в дереве слева для просмотра деталей и шаблонов "))])]),_:1})]),a(e.VCard,{class:"mb-6"},{content:g(()=>[n("div",Yt,[n("div",Jt,[n("div",null,[u[21]||(u[21]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Поиск ",-1)),a(e.VInputText,{modelValue:e.searchQuery,"onUpdate:modelValue":u[4]||(u[4]=l=>e.searchQuery=l),placeholder:"Поиск по названию, имени или описанию...",class:"w-full",onInput:e.debouncedSearch},null,8,["modelValue"])]),n("div",null,[u[22]||(u[22]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Группа ",-1)),a(e.VAutoComplete,{modelValue:e.selectedGroup,"onUpdate:modelValue":u[5]||(u[5]=l=>e.selectedGroup=l),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name","option-value":"id",placeholder:"Все группы",class:"w-full",dropdown:"","show-clear":"",onChange:e.loadTemplates},null,8,["modelValue","suggestions"])]),n("div",null,[u[23]||(u[23]=n("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Тип данных ",-1)),a(e.VAutoComplete,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[6]||(u[6]=l=>e.selectedDataType=l),suggestions:e.dataTypeSuggestions,onComplete:e.filterDataTypes,"option-label":"label","option-value":"value",placeholder:"Все типы",class:"w-full",dropdown:"","show-clear":"",onChange:e.loadTemplates},null,8,["modelValue","suggestions"])])])])]),_:1}),n("div",Qt,[a(e.VCard,null,{content:g(()=>[n("div",Zt,[n("div",$t,E(e.totalCount),1),u[24]||(u[24]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Всего шаблонов",-1))])]),_:1}),a(e.VCard,null,{content:g(()=>[n("div",el,[n("div",ul,E(e.groups.length),1),u[25]||(u[25]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Групп",-1))])]),_:1}),a(e.VCard,null,{content:g(()=>[n("div",tl,[n("div",ll,E(e.usedTemplatesCount),1),u[26]||(u[26]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Используется",-1))])]),_:1}),a(e.VCard,null,{content:g(()=>[n("div",ol,[n("div",nl,E(e.unusedTemplatesCount),1),u[27]||(u[27]=n("div",{class:"text-surface-600 dark:text-surface-400 text-sm"},"Не используется",-1))])]),_:1})]),e.viewMode==="table"?(d(),D(e.VCard,{key:0},{content:g(()=>[a(e.VDataTable,{value:e.templates,loading:e.tableLoading&&e.templates.length===0,paginator:"",rows:e.pageSize,"total-records":e.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:e.onPageChange,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":""},{default:g(()=>[a(e.Column,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:g(({data:l})=>[n("span",al,"#"+E(l.id),1)]),_:1}),a(e.Column,{field:"title",header:"Название",sortable:""},{body:g(({data:l})=>[n("div",null,[n("div",rl,E(l.title),1),n("div",il,E(l.name),1)])]),_:1}),a(e.Column,{field:"group.name",header:"Группа",sortable:""},{body:g(({data:l})=>[l.group?(d(),D(e.VTag,{key:0,value:l.group.name,severity:"secondary"},null,8,["value"])):(d(),h("span",sl,"—"))]),_:1}),a(e.Column,{field:"dataType",header:"Тип",sortable:""},{body:g(({data:l})=>[a(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"])]),_:1}),a(e.Column,{field:"unit",header:"Единица"},{body:g(({data:l})=>[l.unit?(d(),D(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):(d(),h("span",dl,"—"))]),_:1}),a(e.Column,{header:"Использование",style:{width:"120px"}},{body:g(({data:l})=>[n("div",cl,[l._count?(d(),h("div",fl,[n("div",ml,E(e.getTotalUsage(l._count))+" исп.",1),n("div",pl,E(e.getUsageDetails(l._count)),1)])):(d(),h("span",gl,"—"))])]),_:1}),a(e.Column,{header:"Действия",style:{width:"120px"}},{body:g(({data:l})=>[n("div",vl,[l.dataType==="STRING"?(d(),D(e.VButton,{key:0,onClick:f=>e.openSynonyms(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[u[28]||(u[28]=fe(" Синонимы ")),a(e.TagsIcon,{class:"h-5 w-5"})]),_:2,__:[28]},1032,["onClick"])):w("",!0),a(e.VButton,{onClick:f=>e.editTemplate(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[a(e.PencilIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick"]),a(e.DangerButton,{onClick:f=>e.deleteTemplate(l),severity:"danger",outlined:"",size:"small",disabled:e.getTotalUsage(l._count)>0},{default:g(()=>[a(e.TrashIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick","disabled"]),e.getTotalUsage(l._count)>0?(d(),D(e.DangerButton,{key:1,onClick:f=>e.forceDeleteTemplate(l),severity:"danger",outlined:"",size:"small",title:`Принудительно удалить атрибут и все связанные записи (${e.getTotalUsage(l._count)} использований)`},{default:g(()=>[a(e.TrashIcon,{class:"h-4 w-4"}),u[29]||(u[29]=n("span",{class:"ml-1 text-xs"},"!",-1))]),_:2,__:[29]},1032,["onClick","title"])):w("",!0)])]),_:1})]),_:1},8,["value","loading","rows","total-records"])]),_:1})):e.viewMode==="cards"?(d(),h("div",yl,[(d(!0),h(ae,null,pe(e.templates,l=>(d(),D(e.VCard,{key:l.id,class:"border-surface-200 dark:border-surface-700 hover:border-primary border transition-colors"},{content:g(()=>[n("div",bl,[n("div",hl,[n("div",xl,[n("div",Cl,[a(e.TagsIcon,{class:"h-5 w-5"}),n("h3",kl,E(l.title),1),l.isRequired?(d(),D(e.VTag,{key:0,value:"Обязательный",severity:"danger",size:"small"})):w("",!0)]),n("div",Dl,E(l.name),1),l.description?(d(),h("p",El,E(l.description),1)):w("",!0)]),n("div",wl,[a(e.VButton,{onClick:f=>e.editTemplate(l),severity:"secondary",outlined:"",size:"small"},{default:g(()=>[a(e.PencilIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick"]),a(e.VButton,{onClick:f=>e.deleteTemplate(l),severity:"danger",outlined:"",size:"small",disabled:e.getTotalUsage(l._count)>0},{default:g(()=>[a(e.TrashIcon,{class:"h-5 w-5"})]),_:2},1032,["onClick","disabled"]),e.getTotalUsage(l._count)>0?(d(),D(e.VButton,{key:0,onClick:f=>e.forceDeleteTemplate(l),severity:"danger",outlined:"",size:"small",title:`Принудительно удалить атрибут и все связанные записи (${e.getTotalUsage(l._count)} использований)`},{default:g(()=>[a(e.TrashIcon,{class:"h-4 w-4"}),u[30]||(u[30]=n("span",{class:"ml-1 text-xs"},"!",-1))]),_:2,__:[30]},1032,["onClick","title"])):w("",!0)])]),n("div",Fl,[a(e.VTag,{value:e.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"]),l.unit?(d(),D(e.VTag,{key:0,value:e.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):w("",!0),l.group?(d(),D(e.VTag,{key:1,value:l.group.name,severity:"secondary"},null,8,["value"])):w("",!0)]),l._count?(d(),h("div",Tl,[u[34]||(u[34]=n("div",{class:"text-surface-600 dark:text-surface-400 mb-2 text-sm"},"Использование:",-1)),n("div",Bl,[n("div",null,[n("div",Vl,E(l._count.partAttributes||0),1),u[31]||(u[31]=n("div",{class:"text-surface-500 text-xs"},"Запчасти",-1))]),n("div",null,[n("div",Il,E(l._count.catalogItemAttributes||0),1),u[32]||(u[32]=n("div",{class:"text-surface-500 text-xs"},"Каталог",-1))]),n("div",null,[n("div",Sl,E(l._count.equipmentAttributes||0),1),u[33]||(u[33]=n("div",{class:"text-surface-500 text-xs"},"Техника",-1))])])])):w("",!0)])]),_:2},1024))),128)),e.totalCount>e.pageSize?(d(),D(e.VCard,{key:0},{content:g(()=>[n("div",_l,[a(e.Paginator,{rows:e.pageSize,"total-records":e.totalCount,"rows-per-page-options":[10,25,50],onPage:e.onPageChange},null,8,["rows","total-records"])])]),_:1})):w("",!0)])):w("",!0),a(e.VDialog,{visible:e.showCreateDialog,"onUpdate:visible":u[9]||(u[9]=l=>e.showCreateDialog=l),modal:"",header:e.editingTemplate?"Редактировать шаблон":"Создать шаблон",style:{width:"50rem"},breakpoints:{"1199px":"75vw","575px":"90vw"}},{default:g(()=>[a(e.TemplateForm,{modelValue:e.templateForm,"onUpdate:modelValue":u[7]||(u[7]=l=>e.templateForm=l),groups:e.groups,"hierarchy-groups":e.hierarchyGroups,loading:e.saving,onSave:e.saveTemplate,onCancel:u[8]||(u[8]=l=>e.showCreateDialog=!1),onGroupCreated:e.onGroupCreated},null,8,["modelValue","groups","hierarchy-groups","loading"])]),_:1},8,["visible","header"]),a(e.VDialog,{visible:e.showSynonymsDialog,"onUpdate:visible":u[10]||(u[10]=l=>e.showSynonymsDialog=l),modal:"",header:"Управление синонимами",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:g(()=>[e.selectedTemplateForSynonyms?(d(),D(e.AttributeSynonymManager,{key:0,template:e.selectedTemplateForSynonyms},null,8,["template"])):w("",!0)]),_:1},8,["visible"]),a(e.VDialog,{visible:e.showGroupDialog,"onUpdate:visible":u[14]||(u[14]=l=>e.showGroupDialog=l),modal:"",header:e.editingGroup?"Редактировать группу":"Создать группу",style:{width:"500px"}},{footer:g(()=>[n("div",Ml,[a(e.VButton,{onClick:e.closeGroupDialog,severity:"secondary",outlined:"",label:"Отмена"}),a(e.VButton,{onClick:e.saveGroup,loading:e.savingGroup,label:e.editingGroup?"Сохранить":"Создать"},null,8,["loading","label"])])]),default:g(()=>[n("div",Al,[n("div",null,[u[35]||(u[35]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название группы * ",-1)),a(e.VInputText,{modelValue:e.groupForm.name,"onUpdate:modelValue":u[11]||(u[11]=l=>e.groupForm.name=l),placeholder:"Введите название группы...",class:L(["w-full",{"p-invalid":e.groupErrors.name}])},null,8,["modelValue","class"]),e.groupErrors.name?(d(),h("small",Gl,E(e.groupErrors.name),1)):w("",!0)]),n("div",null,[u[36]||(u[36]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),a(e.VTextarea,{modelValue:e.groupForm.description,"onUpdate:modelValue":u[12]||(u[12]=l=>e.groupForm.description=l),placeholder:"Подробное описание группы атрибутов...",rows:"3",class:"w-full"},null,8,["modelValue"])]),n("div",null,[u[37]||(u[37]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Родительская группа ",-1)),a(e.VSelect,{modelValue:e.groupForm.parentId,"onUpdate:modelValue":u[13]||(u[13]=l=>e.groupForm.parentId=l),options:e.parentSelectOptions,"option-label":"name","option-value":"id",class:"w-full",placeholder:"Не выбрано"},null,8,["modelValue","options"])])])]),_:1},8,["visible","header"])]))])}const Nl=te(yt,[["render",Kl]]),Pl=ue({__name:"AttributeTemplateManagerBoundary",setup(t,{expose:u}){u();const o=v(0),s={key:o,onRetry:()=>{o.value++},ErrorBoundary:Yu,AttributeTemplateManager:Nl};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}});function Ol(t,u,o,e,s,r){return d(),D(e.ErrorBoundary,{variant:"detailed",onRetry:e.onRetry,title:"Проблема при загрузке шаблонов",message:"Попробуйте обновить список или повторить попытку."},{default:g(()=>[(d(),D(e.AttributeTemplateManager,{key:e.key}))]),_:1})}const Lo=te(Pl,[["render",Ol]]);export{Lo as default};
