import q from"./Card.DllwvXut.js";import S from"./Button.CplYapw1.js";import{S as G}from"./Select.B5f7pqRM.js";import{I as F}from"./InputText.CPqCR4in.js";import{V as L}from"./Textarea.C8hcWg9_.js";import{V as B}from"./AutoComplete.WqgqstcD.js";import{V as j}from"./Message.DXe4eGzY.js";import{V as Y}from"./Checkbox.Czip7_Ii.js";import{u as R}from"./useTrpc.CcBnDuWb.js";import{_ as U}from"./utils.BWEB-mtU.js";import{d as O,c as i,o as r,a as l,e as n,F as A,r as E,Z as P,_ as H,b as k,h as I,w as T,l as Z,n as z,g as N,f as J}from"./index.CpC-7sc3.js";import{t as d,r as b}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CwY1vywt.js";import"./index.DqILEIKx.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./index.CyH7ziOX.js";import"./trpc.CMxyjkwB.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";const K=O({__name:"AttributePicker",props:P({modelValue:{},label:{}},{modelValue:{local:!0,default:{attributeNames:[],sortOrder:[],withUnits:!1}},modelModifiers:{}}),emits:P(["update:modelValue"],["update:modelValue"]),setup(y,{expose:o,emit:a}){o();const e=y,m=a,s=H(y,"modelValue"),t=R(),p=b(""),g=b([]);function V(){s.value.attributeNames=[],s.value.sortOrder=[]}function f(c){s.value.attributeNames=s.value.attributeNames.filter(C=>C!==c)}function v(c){t.crud.attributeTemplate.findMany.query({where:{OR:[{name:{contains:c.query}},{title:{contains:c.query}}]},take:10}).then(C=>{g.value=C.map(x=>({...x,label:`${x.title} (${x.name})`,value:x.name}))})}function h(c){const C=c.value?.name||c.value;C&&!s.value.attributeNames.includes(C)&&s.value.attributeNames.push(C),p.value=""}watch(s,c=>m("update:modelValue",c),{deep:!0});const _={props:e,emit:m,model:s,trpc:t,query:p,suggestions:g,clear:V,remove:f,onComplete:v,onSelect:h,VButton:S,VAutoComplete:B,VCheckbox:Y};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}}),Q={class:"space-y-2"},W={class:"flex items-center justify-between"},X={class:"font-medium"},$={class:"flex items-center gap-2"},ee={class:"flex gap-2"},te={class:"flex flex-wrap gap-2"},oe=["onClick"];function ae(y,o,a,e,m,s){return r(),i("div",Q,[l("div",W,[l("div",X,d(a.label),1),l("div",$,[n(e.VCheckbox,{modelValue:e.model.withUnits,"onUpdate:modelValue":o[0]||(o[0]=t=>e.model.withUnits=t),inputId:"withUnits",binary:"",trueValue:!0,falseValue:!1},null,8,["modelValue"]),o[2]||(o[2]=l("label",{for:"withUnits",class:"text-sm"},"Единицы",-1))])]),l("div",ee,[n(e.VAutoComplete,{modelValue:e.query,"onUpdate:modelValue":o[1]||(o[1]=t=>e.query=t),suggestions:e.suggestions,optionLabel:"title",placeholder:"Добавить атрибут",class:"flex-1",onComplete:e.onComplete,onItemSelect:e.onSelect},null,8,["modelValue","suggestions"]),n(e.VButton,{label:"Очистить",text:"",onClick:e.clear})]),l("div",te,[(r(!0),i(A,null,E(e.model.attributeNames,t=>(r(),i("span",{key:t,class:"inline-flex items-center gap-2 rounded bg-surface-200 dark:bg-surface-800 px-2 py-1 text-sm"},[l("span",null,d(t),1),l("button",{class:"text-surface-500 hover:text-surface-900",onClick:p=>e.remove(t)},"×",8,oe)]))),128))])])}const le=U(K,[["render",ae]]),re=O({__name:"PreviewRenderer",props:{kind:{},config:{},previewData:{}},setup(y,{expose:o}){o();const a=y,e=I(()=>a.previewData?.data||{}),m=I(()=>a.previewData?.attr?.byName||{});function s(g,V){const f=m.value?.[g];return f?V&&f.unit?`${f.value} ${f.unit}`:f.value:"-"}function t(g,V){return g?g.replace(/\{\{\s*([^\}]+)\s*\}\}/g,(f,v)=>{try{const h=v.split(".");let _=V;for(const c of h)_=_?.[c];return _??""}catch{return""}}):""}const p={props:a,scope:e,attr:m,attrValue:s,interpolate:t};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}}),ne={key:0},ie={class:"text-2xl font-bold"},ue={class:"text-lg text-surface-600"},de={class:"mt-2"},se={class:"mt-4 text-surface-500"},me={key:1},ce={class:"text-2xl font-bold"},fe={class:"text-lg text-surface-600"},ge={class:"mt-4 space-y-1"},ye={key:2},Ve={class:"text-2xl font-bold"},Ce={class:"text-lg text-surface-600"},pe={class:"mt-4 space-y-1"};function ve(y,o,a,e,m,s){return r(),i("div",null,[a.kind==="CATEGORY"?(r(),i("div",ne,[l("h1",ie,d(e.interpolate(a.config.categoryConfig?.h1,e.scope)),1),l("h2",ue,d(e.interpolate(a.config.categoryConfig?.h2,e.scope)),1),l("p",de,d(e.interpolate(a.config.categoryConfig?.description,e.scope)),1),l("div",se,d(e.interpolate(a.config.categoryConfig?.footer,e.scope)),1)])):a.kind==="PART"?(r(),i("div",me,[l("h1",ce,d(e.interpolate(a.config.partConfig?.h1,e.scope)),1),l("h2",fe,d(e.interpolate(a.config.partConfig?.h2,e.scope)),1),l("ul",ge,[(r(!0),i(A,null,E(a.config.partConfig?.attributes.attributeNames||[],t=>(r(),i("li",{key:t,class:"flex justify-between"},[l("span",null,d(t),1),l("span",null,d(e.attrValue(t,!!a.config.partConfig?.attributes.withUnits)),1)]))),128))])])):a.kind==="CATALOG_ITEM"?(r(),i("div",ye,[l("h1",Ve,d(e.interpolate(a.config.catalogItemConfig?.h1,e.scope)),1),l("h2",Ce,d(e.interpolate(a.config.catalogItemConfig?.h2,e.scope)),1),l("ul",pe,[(r(!0),i(A,null,E(a.config.catalogItemConfig?.attributes.attributeNames||[],t=>(r(),i("li",{key:t,class:"flex justify-between"},[l("span",null,d(t),1),l("span",null,d(e.attrValue(t,!!a.config.catalogItemConfig?.attributes.withUnits)),1)]))),128))])])):k("",!0)])}const _e=U(re,[["render",ve]]),be=O({__name:"TemplateEditor",props:{id:{}},setup(y,{expose:o}){o();const a=y,e=R(),m=I(()=>!a.id||a.id==="new"),s=[{label:"CATEGORY",value:"CATEGORY"},{label:"PART",value:"PART"},{label:"CATALOG_ITEM",value:"CATALOG_ITEM"}],t=b({name:"",kind:"CATEGORY",isActive:!0,isDefault:!1,categoryConfig:{h1:"",h2:"",description:"",footer:"",filters:{attributeNames:[],sortOrder:[],withUnits:!1},productAttrs:{attributeNames:[],sortOrder:[],withUnits:!0}},partConfig:{h1:"",h2:"",attributes:{attributeNames:[],sortOrder:[],withUnits:!0}},catalogItemConfig:{h1:"",h2:"",attributes:{attributeNames:[],sortOrder:[],withUnits:!0}}}),p=b(""),g=b([]),V=b(""),f=b(!1),v=b(null);async function h(){if(m.value)return;const u=await e.pageTemplates.byId.query({id:a.id});u&&(t.value={...t.value,...u})}async function _(){try{if(V.value="",m.value){const u=await e.pageTemplates.create.mutate(t.value);navigate(`/admin/templates/${u.id}`)}else await e.pageTemplates.update.mutate({id:a.id,...t.value});await w()}catch(u){V.value=u?.message||"Ошибка сохранения"}}async function c(){!a.id||m.value||(await e.pageTemplates.delete.mutate({id:a.id}),navigate("/admin/templates"))}function C(u){e.crud.partCategory.findMany.query({where:{name:{contains:u.query}},take:10}).then(M=>g.value=M)}function x(u){t.value.partCategoryId=u.value?.id,w()}async function w(){f.value=!0;try{if(t.value.kind==="CATEGORY"){const u=t.value.partCategoryId?await e.crud.partCategory.findFirst.query({where:{id:t.value.partCategoryId}}):(await e.crud.partCategory.findMany.query({take:1}))[0];v.value=await e.pageTemplates.renderCategory.query({slug:u.slug})}else if(t.value.kind==="PART"){const u=(await e.crud.part.findMany.query({take:1,include:{attributes:!0}}))[0];v.value=await e.pageTemplates.renderPart.query({id:u.id})}else if(t.value.kind==="CATALOG_ITEM"){const u=(await e.crud.catalogItem.findMany.query({take:1,include:{attributes:!0}}))[0];v.value=await e.pageTemplates.renderCatalogItem.query({id:u.id})}}finally{f.value=!1}}Z(t,()=>w(),{deep:!0}),z(async()=>{await h(),await w()});const D={props:a,trpc:e,isNew:m,kindOptions:s,form:t,categoryInput:p,categorySuggestions:g,errorText:V,loadingPreview:f,previewData:v,loadTemplate:h,onSave:_,onDelete:c,onCompleteCategory:C,onSelectCategory:x,refreshPreview:w,VCard:q,VButton:S,VSelect:G,VInputText:F,VTextarea:L,VAutoComplete:B,VMessage:j,AttributePicker:le,PreviewRenderer:_e};return Object.defineProperty(D,"__isScriptSetup",{enumerable:!1,value:!0}),D}}),he={class:"w-full max-w-7xl grid grid-cols-1 lg:grid-cols-2 gap-6"},xe={class:"flex items-center justify-between"},ke={class:"text-lg font-semibold"},we={class:"flex items-center gap-2"},Te={class:"space-y-4"},Ae={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Ee={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Ie={key:1},Ue={key:2},Oe={key:3},De={key:4},Pe={key:5},Ne={key:6},Se={key:0,class:"text-surface-500"},Be={key:1};function Re(y,o,a,e,m,s){return r(),i("div",he,[n(e.VCard,null,{header:T(()=>[l("div",xe,[l("h2",ke,d(e.isNew?"Новый шаблон":"Шаблон"),1),l("div",we,[n(e.VButton,{label:"Сохранить",onClick:e.onSave}),e.isNew?k("",!0):(r(),N(e.VButton,{key:0,label:"Удалить",severity:"danger",onClick:e.onDelete}))])])]),content:T(()=>[l("div",Te,[l("div",Ae,[n(e.VInputText,{modelValue:e.form.name,"onUpdate:modelValue":o[0]||(o[0]=t=>e.form.name=t),placeholder:"Название"},null,8,["modelValue"]),n(e.VSelect,{modelValue:e.form.kind,"onUpdate:modelValue":o[1]||(o[1]=t=>e.form.kind=t),options:e.kindOptions,placeholder:"Тип",optionLabel:"label",optionValue:"value"},null,8,["modelValue"])]),e.form.kind==="CATEGORY"?(r(),i("div",Ee,[n(e.VAutoComplete,{modelValue:e.categoryInput,"onUpdate:modelValue":o[2]||(o[2]=t=>e.categoryInput=t),suggestions:e.categorySuggestions,optionLabel:"name",placeholder:"Категория",onComplete:e.onCompleteCategory,onItemSelect:e.onSelectCategory},null,8,["modelValue","suggestions"]),o[15]||(o[15]=l("div",null,null,-1))])):k("",!0),e.form.kind==="CATEGORY"?(r(),i("div",Ie,[n(e.VTextarea,{modelValue:e.form.categoryConfig.h1,"onUpdate:modelValue":o[3]||(o[3]=t=>e.form.categoryConfig.h1=t),placeholder:"H1"},null,8,["modelValue"]),n(e.VTextarea,{modelValue:e.form.categoryConfig.h2,"onUpdate:modelValue":o[4]||(o[4]=t=>e.form.categoryConfig.h2=t),placeholder:"H2"},null,8,["modelValue"]),n(e.VTextarea,{modelValue:e.form.categoryConfig.description,"onUpdate:modelValue":o[5]||(o[5]=t=>e.form.categoryConfig.description=t),placeholder:"Описание"},null,8,["modelValue"]),n(e.VTextarea,{modelValue:e.form.categoryConfig.footer,"onUpdate:modelValue":o[6]||(o[6]=t=>e.form.categoryConfig.footer=t),placeholder:"Футер"},null,8,["modelValue"])])):e.form.kind==="PART"?(r(),i("div",Ue,[n(e.VTextarea,{modelValue:e.form.partConfig.h1,"onUpdate:modelValue":o[7]||(o[7]=t=>e.form.partConfig.h1=t),placeholder:"H1"},null,8,["modelValue"]),n(e.VTextarea,{modelValue:e.form.partConfig.h2,"onUpdate:modelValue":o[8]||(o[8]=t=>e.form.partConfig.h2=t),placeholder:"H2"},null,8,["modelValue"])])):e.form.kind==="CATALOG_ITEM"?(r(),i("div",Oe,[n(e.VTextarea,{modelValue:e.form.catalogItemConfig.h1,"onUpdate:modelValue":o[9]||(o[9]=t=>e.form.catalogItemConfig.h1=t),placeholder:"H1"},null,8,["modelValue"]),n(e.VTextarea,{modelValue:e.form.catalogItemConfig.h2,"onUpdate:modelValue":o[10]||(o[10]=t=>e.form.catalogItemConfig.h2=t),placeholder:"H2"},null,8,["modelValue"])])):k("",!0),e.form.kind==="PART"?(r(),i("div",De,[n(e.AttributePicker,{modelValue:e.form.partConfig.attributes,"onUpdate:modelValue":o[11]||(o[11]=t=>e.form.partConfig.attributes=t)},null,8,["modelValue"])])):e.form.kind==="CATALOG_ITEM"?(r(),i("div",Pe,[n(e.AttributePicker,{modelValue:e.form.catalogItemConfig.attributes,"onUpdate:modelValue":o[12]||(o[12]=t=>e.form.catalogItemConfig.attributes=t)},null,8,["modelValue"])])):e.form.kind==="CATEGORY"?(r(),i("div",Ne,[n(e.AttributePicker,{label:"Фильтры",modelValue:e.form.categoryConfig.filters,"onUpdate:modelValue":o[13]||(o[13]=t=>e.form.categoryConfig.filters=t)},null,8,["modelValue"]),n(e.AttributePicker,{label:"Атрибуты товаров",modelValue:e.form.categoryConfig.productAttrs,"onUpdate:modelValue":o[14]||(o[14]=t=>e.form.categoryConfig.productAttrs=t)},null,8,["modelValue"])])):k("",!0),e.errorText?(r(),N(e.VMessage,{key:7,severity:"error"},{default:T(()=>[J(d(e.errorText),1)]),_:1})):k("",!0)])]),_:1}),n(e.VCard,null,{header:T(()=>o[16]||(o[16]=[l("div",{class:"flex items-center justify-between"},[l("h2",{class:"text-lg font-semibold"},"Предпросмотр"),l("div",{class:"text-sm text-surface-500"},"Обновляется в реальном времени")],-1)])),content:T(()=>[e.loadingPreview?(r(),i("div",Se,"Загрузка предпросмотра...")):(r(),i("div",Be,[n(e.PreviewRenderer,{kind:e.form.kind,config:e.form,previewData:e.previewData},null,8,["kind","config","previewData"])]))]),_:1})])}const ft=U(be,[["render",Re]]);export{ft as default};
