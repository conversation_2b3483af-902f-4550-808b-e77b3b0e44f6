import { defineComponent, useSSRContext, ref, mergeProps, createSlots, withCtx, createVNode, renderList, renderSlot } from 'vue';
import ChevronDownIcon from '@primevue/icons/chevrondown';
import SearchIcon from '@primevue/icons/search';
import SpinnerIcon from '@primevue/icons/spinner';
import TimesIcon from '@primevue/icons/times';
import { c as script } from './index_Ci1qAxnT.mjs';
import { p as ptViewMerge, _ as _export_sfc } from './ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Select",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex cursor-pointer relative select-none rounded-md p-fluid:flex
        bg-surface-0 dark:bg-surface-950
        border border-surface-300 hover:border-surface-400 dark:border-surface-700 dark:hover:border-surface-600
        p-focus:border-primary
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-disabled:bg-surface-200 p-disabled:text-surface-500 dark:p-disabled:bg-surface-700 dark:p-disabled:text-surface-400 p-disabled:pointer-events-none
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        transition-colors duration-200`,
      label: `block whitespace-nowrap overflow-hidden flex-auto w-[1%]
        py-2 px-3 overflow-ellipsis
        p-clearable:pe-7 p-empty:overflow-hidden p-empty:opacity-0 p-editable:cursor-default
        text-surface-700 dark:text-surface-0 bg-transparent border-none outline-none
        p-placeholder:text-surface-500 dark:p-placeholder:text-surface-400
        p-disabled:text-surface-500 dark:p-disabled:text-surface-400
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-lg p-large:px-[0.875rem] p-large:py-[0.625rem]`,
      dropdown: `flex items-center justify-center shrink-0 bg-transparent
        text-surface-400 w-10 rounded-e-md`,
      overlay: `absolute top-0 left-0 rounded-md p-portal-self:min-w-full
        bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]`,
      header: `pt-2 pb-1 px-4`,
      pcFilterContainer: {
        root: `relative`
      },
      pcFilter: {
        root: `w-full appearance-none rounded-md outline-hidden
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            placeholder:text-surface-500 dark:placeholder:text-surface-400
            border border-surface-300 dark:border-surface-700
            enabled:hover:border-surface-400 dark:enabled:hover:border-surface-600
            enabled:focus:border-primary
            disabled:bg-surface-200 disabled:text-surface-500
            dark:disabled:bg-surface-700 dark:disabled:text-surface-400
            ps-3 pe-10 py-2 p-fluid:w-full
            transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`
      },
      pcFilterIconContainer: {
        root: `absolute top-1/2 -mt-2 leading-none end-3 z-1`
      },
      listContainer: `overflow-auto`,
      list: `m-0 p-1 list-none gap-[2px] flex flex-col`,
      optionGroup: `m-0 px-3 py-2 bg-transparent text-surface-500 dark:text-surface-400 font-semibold`,
      optionGroupLabel: ``,
      option: `cursor-pointer font-normal whitespace-nowrap relative overflow-hidden flex items-center
        px-3 py-2 border-none text-surface-700 dark:text-surface-0 bg-transparent rounded-sm
        p-focus:bg-surface-100 dark:p-focus:bg-surface-800 p-focus:text-surface-800 dark:p-focus:text-surface-0
        p-selected:bg-highlight p-focus:p-selected:bg-highlight-emphasis
        transition-colors duration-200`,
      optionLabel: ``,
      optionCheckIcon: `relative -ms-[0.375rem] me-[0.375rem] text-surface-700 dark:text-surface-0`,
      optionBlankIcon: ``,
      emptyMessage: `px-3 py-2`,
      virtualScroller: ``,
      transition: {
        enterFromClass: "opacity-0 scale-y-75",
        enterActiveClass: "transition duration-120 ease-[cubic-bezier(0,0,0.2,1)]",
        leaveActiveClass: "transition-opacity duration-100 ease-linear",
        leaveToClass: "opacity-0"
      }
    });
    const __returned__ = { theme, get ChevronDownIcon() {
      return ChevronDownIcon;
    }, get SearchIcon() {
      return SearchIcon;
    }, get SpinnerIcon() {
      return SpinnerIcon;
    }, get TimesIcon() {
      return TimesIcon;
    }, get Select() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Select"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({
    dropdownicon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["ChevronDownIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["ChevronDownIcon"])
        ];
      }
    }),
    loadingicon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["SpinnerIcon"], { class: "animate-spin" }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["SpinnerIcon"], { class: "animate-spin" })
        ];
      }
    }),
    filtericon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["SearchIcon"], { class: "text-surface-400" }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["SearchIcon"], { class: "text-surface-400" })
        ];
      }
    }),
    clearicon: withCtx(({ clearCallback }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["TimesIcon"], {
          onClick: clearCallback,
          class: "text-surface-400 absolute top-1/2 -mt-2 end-10"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["TimesIcon"], {
            onClick: clearCallback,
            class: "text-surface-400 absolute top-1/2 -mt-2 end-10"
          }, null, 8, ["onClick"])
        ];
      }
    }),
    _: 2
  }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Select.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Select = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { Select as S };
