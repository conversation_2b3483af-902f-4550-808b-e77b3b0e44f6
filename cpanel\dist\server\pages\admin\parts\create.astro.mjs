import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../../chunks/AdminLayout_DWqIyijm.mjs';
import { P as PartWizard } from '../../../chunks/PartWizard_HGoo9yTi.mjs';
import { V as VButton } from '../../../chunks/Button_CVFNKcZL.mjs';
export { r as renderers } from '../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Create = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 - PartTec" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="space-y-6"> <!-- Заголовок страницы --> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
Создание новой запчасти
</h1> <p class="text-surface-600 dark:text-surface-400 mt-1">
Используйте мастер для создания новой запчасти с каталожными позициями
</p> </div> <a href="/admin/parts"> ${renderComponent($$result2, "VButton", VButton, { "client:load": true, "severity": "secondary", "outlined": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/parttec/cpanel/src/volt/Button.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate`
← Назад к списку
` })} </a> </div> <!-- Мастер создания запчасти --> <div> ${renderComponent($$result2, "PartWizard", PartWizard, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/parttec/cpanel/src/components/admin/parts/PartWizard.vue", "client:component-export": "default" })} </div> </div> ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/parts/create.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/parts/create.astro";
const $$url = "/admin/parts/create";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Create,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
