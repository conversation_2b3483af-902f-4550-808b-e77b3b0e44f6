import { createElement<PERSON><PERSON>, openBlock, mergeProps, createElementVNode, renderSlot, defineComponent, useSSRContext, ref, createSlots, renderList, withCtx } from 'vue';
import { cn } from '@primeuix/utils';
import BaseEditableHolder from '@primevue/core/baseeditableholder';
import { style } from '@primeuix/styles/toggleswitch';
import BaseStyle from '@primevue/core/base/style';
import { p as ptViewMerge, _ as _export_sfc } from './ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';

var inlineStyles = {
  root: {
    position: 'relative'
  }
};
var classes = {
  root: function root(_ref) {
    var instance = _ref.instance,
      props = _ref.props;
    return ['p-toggleswitch p-component', {
      'p-toggleswitch-checked': instance.checked,
      'p-disabled': props.disabled,
      'p-invalid': instance.$invalid
    }];
  },
  input: 'p-toggleswitch-input',
  slider: 'p-toggleswitch-slider',
  handle: 'p-toggleswitch-handle'
};
var ToggleSwitchStyle = BaseStyle.extend({
  name: 'toggleswitch',
  style: style,
  classes: classes,
  inlineStyles: inlineStyles
});

var script$1 = {
  name: 'BaseToggleSwitch',
  "extends": BaseEditableHolder,
  props: {
    trueValue: {
      type: null,
      "default": true
    },
    falseValue: {
      type: null,
      "default": false
    },
    readonly: {
      type: Boolean,
      "default": false
    },
    tabindex: {
      type: Number,
      "default": null
    },
    inputId: {
      type: String,
      "default": null
    },
    inputClass: {
      type: [String, Object],
      "default": null
    },
    inputStyle: {
      type: Object,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    }
  },
  style: ToggleSwitchStyle,
  provide: function provide() {
    return {
      $pcToggleSwitch: this,
      $parentInstance: this
    };
  }
};

var script = {
  name: 'ToggleSwitch',
  "extends": script$1,
  inheritAttrs: false,
  emits: ['change', 'focus', 'blur'],
  methods: {
    getPTOptions: function getPTOptions(key) {
      var _ptm = key === 'root' ? this.ptmi : this.ptm;
      return _ptm(key, {
        context: {
          checked: this.checked,
          disabled: this.disabled
        }
      });
    },
    onChange: function onChange(event) {
      if (!this.disabled && !this.readonly) {
        var newValue = this.checked ? this.falseValue : this.trueValue;
        this.writeValue(newValue, event);
        this.$emit('change', event);
      }
    },
    onFocus: function onFocus(event) {
      this.$emit('focus', event);
    },
    onBlur: function onBlur(event) {
      var _this$formField$onBlu, _this$formField;
      this.$emit('blur', event);
      (_this$formField$onBlu = (_this$formField = this.formField).onBlur) === null || _this$formField$onBlu === void 0 || _this$formField$onBlu.call(_this$formField, event);
    }
  },
  computed: {
    checked: function checked() {
      return this.d_value === this.trueValue;
    },
    dataP: function dataP() {
      return cn({
        checked: this.checked,
        disabled: this.disabled,
        invalid: this.$invalid
      });
    }
  }
};

var _hoisted_1 = ["data-p-checked", "data-p-disabled", "data-p"];
var _hoisted_2 = ["id", "checked", "tabindex", "disabled", "readonly", "aria-checked", "aria-labelledby", "aria-label", "aria-invalid"];
var _hoisted_3 = ["data-p"];
var _hoisted_4 = ["data-p"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    style: _ctx.sx('root')
  }, $options.getPTOptions('root'), {
    "data-p-checked": $options.checked,
    "data-p-disabled": _ctx.disabled,
    "data-p": $options.dataP
  }), [createElementVNode("input", mergeProps({
    id: _ctx.inputId,
    type: "checkbox",
    role: "switch",
    "class": [_ctx.cx('input'), _ctx.inputClass],
    style: _ctx.inputStyle,
    checked: $options.checked,
    tabindex: _ctx.tabindex,
    disabled: _ctx.disabled,
    readonly: _ctx.readonly,
    "aria-checked": $options.checked,
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel,
    "aria-invalid": _ctx.invalid || undefined,
    onFocus: _cache[0] || (_cache[0] = function () {
      return $options.onFocus && $options.onFocus.apply($options, arguments);
    }),
    onBlur: _cache[1] || (_cache[1] = function () {
      return $options.onBlur && $options.onBlur.apply($options, arguments);
    }),
    onChange: _cache[2] || (_cache[2] = function () {
      return $options.onChange && $options.onChange.apply($options, arguments);
    })
  }, $options.getPTOptions('input')), null, 16, _hoisted_2), createElementVNode("div", mergeProps({
    "class": _ctx.cx('slider')
  }, $options.getPTOptions('slider'), {
    "data-p": $options.dataP
  }), [createElementVNode("div", mergeProps({
    "class": _ctx.cx('handle')
  }, $options.getPTOptions('handle'), {
    "data-p": $options.dataP
  }), [renderSlot(_ctx.$slots, "handle", {
    checked: $options.checked
  })], 16, _hoisted_4)], 16, _hoisted_3)], 16, _hoisted_1);
}

script.render = render;

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ToggleSwitch",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-block w-10 h-6`,
      input: `peer cursor-pointer disabled:cursor-default appearance-none absolute top-0 start-0 w-full h-full m-0 p-0 opacity-0 z-10 rounded-[30px]`,
      slider: `inline-block w-full h-full rounded-[30px] shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        bg-surface-300 dark:bg-surface-700
        border border-transparent
        transition-colors duration-200
        peer-enabled:peer-hover:bg-surface-400 dark:peer-enabled:peer-hover:bg-surface-600
        p-checked:bg-primary peer-enabled:peer-hover:p-checked:bg-primary-emphasis
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-disabled:bg-surface-200 dark:p-disabled:bg-surface-600
        peer-focus-visible:outline peer-focus-visible:outline-1 peer-focus-visible:outline-offset-2 peer-focus-visible:outline-primary`,
      handle: `absolute top-1/2 flex justify-center items-center
        bg-surface-0 dark:bg-surface-400
        text-surface-500 dark:text-surface-900
        w-4 h-4 start-1 -mt-2 rounded-full
        transition-[background,color,left] duration-200
        p-checked:bg-surface-0 dark:p-checked:bg-surface-900 p-checked:text-primary p-checked:start-5
        p-disabled:bg-surface-700 dark:p-disabled:bg-surface-900
}`
    });
    const __returned__ = { theme, get ToggleSwitch() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ToggleSwitch"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/ToggleSwitch.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const VToggleSwitch = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { VToggleSwitch as V };
