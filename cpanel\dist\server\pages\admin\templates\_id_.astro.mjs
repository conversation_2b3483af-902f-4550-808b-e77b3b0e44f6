import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate } from '../../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../../chunks/AdminLayout_DWqIyijm.mjs';
import { defineComponent, useSSRContext, mergeModels, useModel, ref, mergeProps, computed, watch as watch$1, onMounted, withCtx, createTextVNode, toDisplayString, createVNode, createBlock, createCommentVNode, openBlock } from 'vue';
import { V as VCard } from '../../../chunks/Card_gUaTQQTl.mjs';
import { V as VButton } from '../../../chunks/Button_CVFNKcZL.mjs';
import { S as Select } from '../../../chunks/Select_CSb2vMfa.mjs';
import { I as InputText } from '../../../chunks/InputText_DBferNa6.mjs';
import { V as VTextarea } from '../../../chunks/Textarea_DvjjWqGc.mjs';
import { V as VAutoComplete } from '../../../chunks/AutoComplete_Ude6CFTv.mjs';
import { V as VMessage } from '../../../chunks/Message_tF9wpTDJ.mjs';
import { V as VCheckbox } from '../../../chunks/Checkbox_sKnCOH0n.mjs';
import { u as useTrpc } from '../../../chunks/useTrpc_CFYBp3yo.mjs';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderList } from 'vue/server-renderer';
import { _ as _export_sfc } from '../../../chunks/ClientRouter_Cit0rBg5.mjs';
export { r as renderers } from '../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "AttributePicker",
  props: /* @__PURE__ */ mergeModels({
    modelValue: {},
    label: {}
  }, {
    "modelValue": { local: true, default: { attributeNames: [], sortOrder: [], withUnits: false } },
    "modelModifiers": {}
  }),
  emits: /* @__PURE__ */ mergeModels(["update:modelValue"], ["update:modelValue"]),
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const model = useModel(__props, "modelValue");
    const trpc = useTrpc();
    const query = ref("");
    const suggestions = ref([]);
    function clear() {
      model.value.attributeNames = [];
      model.value.sortOrder = [];
    }
    function remove(name) {
      model.value.attributeNames = model.value.attributeNames.filter((n) => n !== name);
    }
    function onComplete(e) {
      trpc.crud.attributeTemplate.findMany.query({ where: { OR: [{ name: { contains: e.query } }, { title: { contains: e.query } }] }, take: 10 }).then((res) => {
        suggestions.value = res.map((t) => ({ ...t, label: `${t.title} (${t.name})`, value: t.name }));
      });
    }
    function onSelect(e) {
      const name = e.value?.name || e.value;
      if (name && !model.value.attributeNames.includes(name)) model.value.attributeNames.push(name);
      query.value = "";
    }
    watch(model, (v) => emit("update:modelValue", v), { deep: true });
    const __returned__ = { props, emit, model, trpc, query, suggestions, clear, remove, onComplete, onSelect, VButton, VAutoComplete, VCheckbox };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-2" }, _attrs))}><div class="flex items-center justify-between"><div class="font-medium">${ssrInterpolate($props.label)}</div><div class="flex items-center gap-2">`);
  _push(ssrRenderComponent($setup["VCheckbox"], {
    modelValue: $setup.model.withUnits,
    "onUpdate:modelValue": ($event) => $setup.model.withUnits = $event,
    inputId: "withUnits",
    binary: "",
    trueValue: true,
    falseValue: false
  }, null, _parent));
  _push(`<label for="withUnits" class="text-sm">\u0415\u0434\u0438\u043D\u0438\u0446\u044B</label></div></div><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.query,
    "onUpdate:modelValue": ($event) => $setup.query = $event,
    suggestions: $setup.suggestions,
    optionLabel: "title",
    placeholder: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442",
    class: "flex-1",
    onComplete: $setup.onComplete,
    onItemSelect: $setup.onSelect
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    label: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
    text: "",
    onClick: $setup.clear
  }, null, _parent));
  _push(`</div><div class="flex flex-wrap gap-2"><!--[-->`);
  ssrRenderList($setup.model.attributeNames, (name) => {
    _push(`<span class="inline-flex items-center gap-2 rounded bg-surface-200 dark:bg-surface-800 px-2 py-1 text-sm"><span>${ssrInterpolate(name)}</span><button class="text-surface-500 hover:text-surface-900">\xD7</button></span>`);
  });
  _push(`<!--]--></div></div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/templates/components/AttributePicker.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const AttributePicker = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "PreviewRenderer",
  props: {
    kind: {},
    config: {},
    previewData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const scope = computed(() => props.previewData?.data || {});
    const attr = computed(() => props.previewData?.attr?.byName || {});
    function attrValue(name, withUnits) {
      const i = attr.value?.[name];
      if (!i) return "-";
      return withUnits && i.unit ? `${i.value} ${i.unit}` : i.value;
    }
    function interpolate(tpl, scope2) {
      if (!tpl) return "";
      return tpl.replace(/\{\{\s*([^\}]+)\s*\}\}/g, (_, expr) => {
        try {
          const path = expr.split(".");
          let cur = scope2;
          for (const key of path) cur = cur?.[key];
          return cur ?? "";
        } catch {
          return "";
        }
      });
    }
    const __returned__ = { props, scope, attr, attrValue, interpolate };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(_attrs)}>`);
  if ($props.kind === "CATEGORY") {
    _push(`<div><h1 class="text-2xl font-bold">${ssrInterpolate($setup.interpolate($props.config.categoryConfig?.h1, $setup.scope))}</h1><h2 class="text-lg text-surface-600">${ssrInterpolate($setup.interpolate($props.config.categoryConfig?.h2, $setup.scope))}</h2><p class="mt-2">${ssrInterpolate($setup.interpolate($props.config.categoryConfig?.description, $setup.scope))}</p><div class="mt-4 text-surface-500">${ssrInterpolate($setup.interpolate($props.config.categoryConfig?.footer, $setup.scope))}</div></div>`);
  } else if ($props.kind === "PART") {
    _push(`<div><h1 class="text-2xl font-bold">${ssrInterpolate($setup.interpolate($props.config.partConfig?.h1, $setup.scope))}</h1><h2 class="text-lg text-surface-600">${ssrInterpolate($setup.interpolate($props.config.partConfig?.h2, $setup.scope))}</h2><ul class="mt-4 space-y-1"><!--[-->`);
    ssrRenderList($props.config.partConfig?.attributes.attributeNames || [], (name) => {
      _push(`<li class="flex justify-between"><span>${ssrInterpolate(name)}</span><span>${ssrInterpolate($setup.attrValue(name, !!$props.config.partConfig?.attributes.withUnits))}</span></li>`);
    });
    _push(`<!--]--></ul></div>`);
  } else if ($props.kind === "CATALOG_ITEM") {
    _push(`<div><h1 class="text-2xl font-bold">${ssrInterpolate($setup.interpolate($props.config.catalogItemConfig?.h1, $setup.scope))}</h1><h2 class="text-lg text-surface-600">${ssrInterpolate($setup.interpolate($props.config.catalogItemConfig?.h2, $setup.scope))}</h2><ul class="mt-4 space-y-1"><!--[-->`);
    ssrRenderList($props.config.catalogItemConfig?.attributes.attributeNames || [], (name) => {
      _push(`<li class="flex justify-between"><span>${ssrInterpolate(name)}</span><span>${ssrInterpolate($setup.attrValue(name, !!$props.config.catalogItemConfig?.attributes.withUnits))}</span></li>`);
    });
    _push(`<!--]--></ul></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/templates/components/PreviewRenderer.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const PreviewRenderer = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "TemplateEditor",
  props: {
    id: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const trpc = useTrpc();
    const isNew = computed(() => !props.id || props.id === "new");
    const kindOptions = [
      { label: "CATEGORY", value: "CATEGORY" },
      { label: "PART", value: "PART" },
      { label: "CATALOG_ITEM", value: "CATALOG_ITEM" }
    ];
    const form = ref({
      name: "",
      kind: "CATEGORY",
      isActive: true,
      isDefault: false,
      categoryConfig: { h1: "", h2: "", description: "", footer: "", filters: { attributeNames: [], sortOrder: [], withUnits: false }, productAttrs: { attributeNames: [], sortOrder: [], withUnits: true } },
      partConfig: { h1: "", h2: "", attributes: { attributeNames: [], sortOrder: [], withUnits: true } },
      catalogItemConfig: { h1: "", h2: "", attributes: { attributeNames: [], sortOrder: [], withUnits: true } }
    });
    const categoryInput = ref("");
    const categorySuggestions = ref([]);
    const errorText = ref("");
    const loadingPreview = ref(false);
    const previewData = ref(null);
    async function loadTemplate() {
      if (isNew.value) return;
      const data = await trpc.pageTemplates.byId.query({ id: props.id });
      if (data) form.value = { ...form.value, ...data };
    }
    async function onSave() {
      try {
        errorText.value = "";
        if (isNew.value) {
          const created = await trpc.pageTemplates.create.mutate(form.value);
          navigate(`/admin/templates/${created.id}`);
        } else {
          await trpc.pageTemplates.update.mutate({ id: props.id, ...form.value });
        }
        await refreshPreview();
      } catch (e) {
        errorText.value = e?.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F";
      }
    }
    async function onDelete() {
      if (!props.id || isNew.value) return;
      await trpc.pageTemplates.delete.mutate({ id: props.id });
      navigate("/admin/templates");
    }
    function onCompleteCategory(e) {
      trpc.crud.partCategory.findMany.query({ where: { name: { contains: e.query } }, take: 10 }).then((res) => categorySuggestions.value = res);
    }
    function onSelectCategory(e) {
      form.value.partCategoryId = e.value?.id;
      refreshPreview();
    }
    async function refreshPreview() {
      loadingPreview.value = true;
      try {
        if (form.value.kind === "CATEGORY") {
          const category = form.value.partCategoryId ? await trpc.crud.partCategory.findFirst.query({ where: { id: form.value.partCategoryId } }) : (await trpc.crud.partCategory.findMany.query({ take: 1 }))[0];
          previewData.value = await trpc.pageTemplates.renderCategory.query({ slug: category.slug });
        } else if (form.value.kind === "PART") {
          const part = (await trpc.crud.part.findMany.query({ take: 1, include: { attributes: true } }))[0];
          previewData.value = await trpc.pageTemplates.renderPart.query({ id: part.id });
        } else if (form.value.kind === "CATALOG_ITEM") {
          const item = (await trpc.crud.catalogItem.findMany.query({ take: 1, include: { attributes: true } }))[0];
          previewData.value = await trpc.pageTemplates.renderCatalogItem.query({ id: item.id });
        }
      } finally {
        loadingPreview.value = false;
      }
    }
    watch$1(form, () => refreshPreview(), { deep: true });
    onMounted(async () => {
      await loadTemplate();
      await refreshPreview();
    });
    const __returned__ = { props, trpc, isNew, kindOptions, form, categoryInput, categorySuggestions, errorText, loadingPreview, previewData, loadTemplate, onSave, onDelete, onCompleteCategory, onSelectCategory, refreshPreview, VCard, VButton, VSelect: Select, VInputText: InputText, VTextarea, VAutoComplete, VMessage, AttributePicker, PreviewRenderer };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-7xl grid grid-cols-1 lg:grid-cols-2 gap-6" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center justify-between"${_scopeId}><h2 class="text-lg font-semibold"${_scopeId}>${ssrInterpolate($setup.isNew ? "\u041D\u043E\u0432\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D" : "\u0428\u0430\u0431\u043B\u043E\u043D")}</h2><div class="flex items-center gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
          onClick: $setup.onSave
        }, null, _parent2, _scopeId));
        if (!$setup.isNew) {
          _push2(ssrRenderComponent($setup["VButton"], {
            label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
            severity: "danger",
            onClick: $setup.onDelete
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center justify-between" }, [
            createVNode("h2", { class: "text-lg font-semibold" }, toDisplayString($setup.isNew ? "\u041D\u043E\u0432\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D" : "\u0428\u0430\u0431\u043B\u043E\u043D"), 1),
            createVNode("div", { class: "flex items-center gap-2" }, [
              createVNode($setup["VButton"], {
                label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
                onClick: $setup.onSave
              }),
              !$setup.isNew ? (openBlock(), createBlock($setup["VButton"], {
                key: 0,
                label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                severity: "danger",
                onClick: $setup.onDelete
              })) : createCommentVNode("", true)
            ])
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div class="grid grid-cols-1 sm:grid-cols-2 gap-4"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.name,
          "onUpdate:modelValue": ($event) => $setup.form.name = $event,
          placeholder: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.form.kind,
          "onUpdate:modelValue": ($event) => $setup.form.kind = $event,
          options: $setup.kindOptions,
          placeholder: "\u0422\u0438\u043F",
          optionLabel: "label",
          optionValue: "value"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.form.kind === "CATEGORY") {
          _push2(`<div class="grid grid-cols-1 sm:grid-cols-2 gap-4"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.categoryInput,
            "onUpdate:modelValue": ($event) => $setup.categoryInput = $event,
            suggestions: $setup.categorySuggestions,
            optionLabel: "name",
            placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
            onComplete: $setup.onCompleteCategory,
            onItemSelect: $setup.onSelectCategory
          }, null, _parent2, _scopeId));
          _push2(`<div${_scopeId}></div></div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.form.kind === "CATEGORY") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.categoryConfig.h1,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.h1 = $event,
            placeholder: "H1"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.categoryConfig.h2,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.h2 = $event,
            placeholder: "H2"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.categoryConfig.description,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.description = $event,
            placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.categoryConfig.footer,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.footer = $event,
            placeholder: "\u0424\u0443\u0442\u0435\u0440"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else if ($setup.form.kind === "PART") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.partConfig.h1,
            "onUpdate:modelValue": ($event) => $setup.form.partConfig.h1 = $event,
            placeholder: "H1"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.partConfig.h2,
            "onUpdate:modelValue": ($event) => $setup.form.partConfig.h2 = $event,
            placeholder: "H2"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else if ($setup.form.kind === "CATALOG_ITEM") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.catalogItemConfig.h1,
            "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.h1 = $event,
            placeholder: "H1"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.form.catalogItemConfig.h2,
            "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.h2 = $event,
            placeholder: "H2"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.form.kind === "PART") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["AttributePicker"], {
            modelValue: $setup.form.partConfig.attributes,
            "onUpdate:modelValue": ($event) => $setup.form.partConfig.attributes = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else if ($setup.form.kind === "CATALOG_ITEM") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["AttributePicker"], {
            modelValue: $setup.form.catalogItemConfig.attributes,
            "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.attributes = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else if ($setup.form.kind === "CATEGORY") {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["AttributePicker"], {
            label: "\u0424\u0438\u043B\u044C\u0442\u0440\u044B",
            modelValue: $setup.form.categoryConfig.filters,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.filters = $event
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["AttributePicker"], {
            label: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u043E\u0432\u0430\u0440\u043E\u0432",
            modelValue: $setup.form.categoryConfig.productAttrs,
            "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.productAttrs = $event
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if ($setup.errorText) {
          _push2(ssrRenderComponent($setup["VMessage"], { severity: "error" }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate($setup.errorText)}`);
              } else {
                return [
                  createTextVNode(toDisplayString($setup.errorText), 1)
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", { class: "grid grid-cols-1 sm:grid-cols-2 gap-4" }, [
              createVNode($setup["VInputText"], {
                modelValue: $setup.form.name,
                "onUpdate:modelValue": ($event) => $setup.form.name = $event,
                placeholder: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VSelect"], {
                modelValue: $setup.form.kind,
                "onUpdate:modelValue": ($event) => $setup.form.kind = $event,
                options: $setup.kindOptions,
                placeholder: "\u0422\u0438\u043F",
                optionLabel: "label",
                optionValue: "value"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            $setup.form.kind === "CATEGORY" ? (openBlock(), createBlock("div", {
              key: 0,
              class: "grid grid-cols-1 sm:grid-cols-2 gap-4"
            }, [
              createVNode($setup["VAutoComplete"], {
                modelValue: $setup.categoryInput,
                "onUpdate:modelValue": ($event) => $setup.categoryInput = $event,
                suggestions: $setup.categorySuggestions,
                optionLabel: "name",
                placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
                onComplete: $setup.onCompleteCategory,
                onItemSelect: $setup.onSelectCategory
              }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"]),
              createVNode("div")
            ])) : createCommentVNode("", true),
            $setup.form.kind === "CATEGORY" ? (openBlock(), createBlock("div", { key: 1 }, [
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.categoryConfig.h1,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.h1 = $event,
                placeholder: "H1"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.categoryConfig.h2,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.h2 = $event,
                placeholder: "H2"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.categoryConfig.description,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.description = $event,
                placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.categoryConfig.footer,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.footer = $event,
                placeholder: "\u0424\u0443\u0442\u0435\u0440"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : $setup.form.kind === "PART" ? (openBlock(), createBlock("div", { key: 2 }, [
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.partConfig.h1,
                "onUpdate:modelValue": ($event) => $setup.form.partConfig.h1 = $event,
                placeholder: "H1"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.partConfig.h2,
                "onUpdate:modelValue": ($event) => $setup.form.partConfig.h2 = $event,
                placeholder: "H2"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : $setup.form.kind === "CATALOG_ITEM" ? (openBlock(), createBlock("div", { key: 3 }, [
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.catalogItemConfig.h1,
                "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.h1 = $event,
                placeholder: "H1"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.catalogItemConfig.h2,
                "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.h2 = $event,
                placeholder: "H2"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : createCommentVNode("", true),
            $setup.form.kind === "PART" ? (openBlock(), createBlock("div", { key: 4 }, [
              createVNode($setup["AttributePicker"], {
                modelValue: $setup.form.partConfig.attributes,
                "onUpdate:modelValue": ($event) => $setup.form.partConfig.attributes = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : $setup.form.kind === "CATALOG_ITEM" ? (openBlock(), createBlock("div", { key: 5 }, [
              createVNode($setup["AttributePicker"], {
                modelValue: $setup.form.catalogItemConfig.attributes,
                "onUpdate:modelValue": ($event) => $setup.form.catalogItemConfig.attributes = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : $setup.form.kind === "CATEGORY" ? (openBlock(), createBlock("div", { key: 6 }, [
              createVNode($setup["AttributePicker"], {
                label: "\u0424\u0438\u043B\u044C\u0442\u0440\u044B",
                modelValue: $setup.form.categoryConfig.filters,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.filters = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["AttributePicker"], {
                label: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u043E\u0432\u0430\u0440\u043E\u0432",
                modelValue: $setup.form.categoryConfig.productAttrs,
                "onUpdate:modelValue": ($event) => $setup.form.categoryConfig.productAttrs = $event
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])) : createCommentVNode("", true),
            $setup.errorText ? (openBlock(), createBlock($setup["VMessage"], {
              key: 7,
              severity: "error"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString($setup.errorText), 1)
              ]),
              _: 1
            })) : createCommentVNode("", true)
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center justify-between"${_scopeId}><h2 class="text-lg font-semibold"${_scopeId}>\u041F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440</h2><div class="text-sm text-surface-500"${_scopeId}>\u041E\u0431\u043D\u043E\u0432\u043B\u044F\u0435\u0442\u0441\u044F \u0432 \u0440\u0435\u0430\u043B\u044C\u043D\u043E\u043C \u0432\u0440\u0435\u043C\u0435\u043D\u0438</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center justify-between" }, [
            createVNode("h2", { class: "text-lg font-semibold" }, "\u041F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440"),
            createVNode("div", { class: "text-sm text-surface-500" }, "\u041E\u0431\u043D\u043E\u0432\u043B\u044F\u0435\u0442\u0441\u044F \u0432 \u0440\u0435\u0430\u043B\u044C\u043D\u043E\u043C \u0432\u0440\u0435\u043C\u0435\u043D\u0438")
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.loadingPreview) {
          _push2(`<div class="text-surface-500"${_scopeId}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430...</div>`);
        } else {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["PreviewRenderer"], {
            kind: $setup.form.kind,
            config: $setup.form,
            previewData: $setup.previewData
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        }
      } else {
        return [
          $setup.loadingPreview ? (openBlock(), createBlock("div", {
            key: 0,
            class: "text-surface-500"
          }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430...")) : (openBlock(), createBlock("div", { key: 1 }, [
            createVNode($setup["PreviewRenderer"], {
              kind: $setup.form.kind,
              config: $setup.form,
              previewData: $setup.previewData
            }, null, 8, ["kind", "config", "previewData"])
          ]))
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/templates/TemplateEditor.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const TemplateEditor = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$id = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const id = Astro2.params.id;
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 - PartTec Admin", "showSidebar": true }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "TemplateEditor", TemplateEditor, { "client:load": true, "id": id, "client:component-hydration": "load", "client:component-path": "@/widgets/templates/TemplateEditor.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/templates/[id].astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/templates/[id].astro";
const $$url = "/admin/templates/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
