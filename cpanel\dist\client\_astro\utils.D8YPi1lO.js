var i={};function a(e){return new Promise((n,s)=>{const t=new FileReader;t.onload=()=>n(String(t.result)),t.onerror=o=>s(o),t.readAsDataURL(e)})}function c(e){if(!e)return"";const n={а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"e",ж:"zh",з:"z",и:"i",й:"y",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"h",ц:"ts",ч:"ch",ш:"sh",щ:"sch",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya"},s=e.trim().toLowerCase();let t="";for(const o of s){const r=o.charCodeAt(0);if(r>=97&&r<=122||r>=48&&r<=57){t+=o;continue}if(n[o]!==void 0){t+=n[o];continue}if(/\s|[_]+/.test(o)){t+="-";continue}}return t.replace(/[^a-z0-9-]/g,"-").replace(/-{2,}/g,"-").replace(/^-+|-+$/g,"")}function f(e){if(!e)return;if(/^https?:\/\//i.test(e))return e;const n=typeof window<"u"?window.__PUBLIC_API_URL__||"http://localhost:3000":i.PUBLIC_API_URL||i.API_URL||"http://localhost:3000";return e.startsWith("/")?`${n}${e}`:`${n}/${e}`}export{a as f,f as r,c as s};
