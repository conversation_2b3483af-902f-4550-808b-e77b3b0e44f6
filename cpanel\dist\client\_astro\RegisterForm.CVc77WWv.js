import{w as C}from"./runtime-dom.esm-bundler.0NVQG2L5.js";import{u as B}from"./useAuth.yxvNvvlH.js";import P from"./Button.CplYapw1.js";import k from"./Card.DllwvXut.js";import{I as S}from"./InputText.CPqCR4in.js";import{P as T}from"./Password.qRKXeLK4.js";import{V as A}from"./Message.DXe4eGzY.js";import{V as I}from"./Checkbox.Czip7_Ii.js";import{S as U}from"./Select.B5f7pqRM.js";import{n as R}from"./router.DKcY2uv6.js";import{_ as O}from"./utils.BWEB-mtU.js";import{d as j,c as d,a as r,e as m,w,h as M,o as n,g as N,b as f,f as p}from"./index.CpC-7sc3.js";import{r as g,a as b,t as c}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./auth-client.1y76axwe.js";import"./schemas.BR5-L2eu.js";import"./coerce.CW6lkyCY.js";import"./types.FgRm47Sn.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DqILEIKx.js";import"./index.BRRJVlxZ.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.CwY1vywt.js";import"./index.CyH7ziOX.js";import"./index.DBjPSdxz.js";import"./index.DCNsBfCe.js";const z=j({__name:"RegisterForm",setup(y,{expose:e}){e();const{signUp:x,RegisterFormSchema:u}=B(),v=g(!1),s=g(""),l=[{label:"Пользователь",value:"USER"},{label:"Магазин запчастей",value:"SHOP"}],a=b({name:"",email:"",password:"",confirmPassword:"",role:"USER",acceptTerms:!1}),t=b({}),V=M(()=>a.name&&a.email&&a.password&&a.confirmPassword&&a.acceptTerms&&Object.keys(t).length===0),F={signUp:x,RegisterFormSchema:u,isSubmitting:v,generalError:s,roleOptions:l,formData:a,fieldErrors:t,isFormValid:V,validateField:i=>{const o=u.safeParse(a);if(o.success)delete t[i];else{const E=o.error.issues.find(D=>D.path.includes(i));E&&(t[i]=E.message)}},clearFieldError:i=>{delete t[i],s.value=""},handleSubmit:async()=>{Object.keys(t).forEach(o=>delete t[o]),s.value="";const i=u.safeParse(a);if(!i.success){i.error.issues.forEach(o=>{const E=o.path[0];t[E]=o.message});return}v.value=!0;try{const o=await x(a);o.error?o.error.message.includes("already exists")?s.value="Пользователь с таким email уже существует":o.error.message.includes("Invalid email")?s.value="Некорректный email адрес":s.value=o.error.message||"Ошибка регистрации":(console.log("✅ Успешная регистрация"),setTimeout(()=>{R("/admin")},100))}catch(o){console.error("Registration error:",o),s.value="Произошла ошибка при регистрации. Попробуйте позже."}finally{v.value=!1}},Button:P,Card:k,InputText:S,Password:T,Message:A,Checkbox:I,Select:U};return Object.defineProperty(F,"__isScriptSetup",{enumerable:!1,value:!0}),F}}),H={class:"min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8"},q={class:"max-w-md w-full space-y-8"},G={key:1,class:"text-red-500"},J={key:2,class:"text-red-500"},K={key:3,class:"text-red-500"},L={key:4,class:"text-red-500"},Q={key:5,class:"text-red-500"},W={class:"flex items-start"},X={key:6,class:"text-red-500"};function Y(y,e,x,u,v,s){return n(),d("div",H,[r("div",q,[e[23]||(e[23]=r("div",null,[r("div",{class:"mx-auto h-12 w-12 flex items-center justify-center"},[r("img",{class:"h-12 w-12",src:"/favicon.svg",alt:"PartTec"})]),r("h2",{class:"mt-6 text-center text-3xl font-extrabold text-surface-900"}," Регистрация в PartTec "),r("p",{class:"mt-2 text-center text-sm text-surface-600"}," Создайте аккаунт для доступа к системе ")],-1)),m(u.Card,{class:"mt-8"},{content:w(()=>[r("form",{class:"space-y-6",onSubmit:C(u.handleSubmit,["prevent"])},[u.generalError?(n(),N(u.Message,{key:0,severity:"error",closable:!1},{default:w(()=>[p(c(u.generalError),1)]),_:1})):f("",!0),r("div",null,[m(u.InputText,{id:"name",modelValue:u.formData.name,"onUpdate:modelValue":e[0]||(e[0]=l=>u.formData.name=l),type:"text",autocomplete:"name",class:"w-full",invalid:!!u.fieldErrors.name,onBlur:e[1]||(e[1]=l=>u.validateField("name")),onInput:e[2]||(e[2]=l=>u.clearFieldError("name"))},null,8,["modelValue","invalid"]),e[16]||(e[16]=r("label",{for:"name"},"Полное имя",-1))]),u.fieldErrors.name?(n(),d("small",G,c(u.fieldErrors.name),1)):f("",!0),r("div",null,[m(u.InputText,{id:"email",modelValue:u.formData.email,"onUpdate:modelValue":e[3]||(e[3]=l=>u.formData.email=l),type:"email",autocomplete:"email",class:"w-full",invalid:!!u.fieldErrors.email,onBlur:e[4]||(e[4]=l=>u.validateField("email")),onInput:e[5]||(e[5]=l=>u.clearFieldError("email"))},null,8,["modelValue","invalid"]),e[17]||(e[17]=r("label",{for:"email"},"Email адрес",-1))]),u.fieldErrors.email?(n(),d("small",J,c(u.fieldErrors.email),1)):f("",!0),r("div",null,[m(u.Password,{id:"password",modelValue:u.formData.password,"onUpdate:modelValue":e[6]||(e[6]=l=>u.formData.password=l),autocomplete:"new-password",class:"w-full",invalid:!!u.fieldErrors.password,feedback:!1,"toggle-mask":"",onBlur:e[7]||(e[7]=l=>u.validateField("password")),onInput:e[8]||(e[8]=l=>u.clearFieldError("password"))},null,8,["modelValue","invalid"]),e[18]||(e[18]=r("label",{for:"password"},"Пароль",-1))]),u.fieldErrors.password?(n(),d("small",K,c(u.fieldErrors.password),1)):f("",!0),r("div",null,[m(u.Password,{id:"confirmPassword",modelValue:u.formData.confirmPassword,"onUpdate:modelValue":e[9]||(e[9]=l=>u.formData.confirmPassword=l),autocomplete:"new-password",class:"w-full",invalid:!!u.fieldErrors.confirmPassword,feedback:!1,"toggle-mask":"",onBlur:e[10]||(e[10]=l=>u.validateField("confirmPassword")),onInput:e[11]||(e[11]=l=>u.clearFieldError("confirmPassword"))},null,8,["modelValue","invalid"]),e[19]||(e[19]=r("label",{for:"confirmPassword"},"Подтвердите пароль",-1))]),u.fieldErrors.confirmPassword?(n(),d("small",L,c(u.fieldErrors.confirmPassword),1)):f("",!0),r("div",null,[m(u.Select,{id:"role",modelValue:u.formData.role,"onUpdate:modelValue":e[12]||(e[12]=l=>u.formData.role=l),options:u.roleOptions,"option-label":"label","option-value":"value",class:"w-full",invalid:!!u.fieldErrors.role,onChange:e[13]||(e[13]=l=>u.clearFieldError("role"))},null,8,["modelValue","invalid"]),e[20]||(e[20]=r("label",{for:"role"},"Тип аккаунта",-1))]),u.fieldErrors.role?(n(),d("small",Q,c(u.fieldErrors.role),1)):f("",!0),r("div",W,[m(u.Checkbox,{id:"acceptTerms",modelValue:u.formData.acceptTerms,"onUpdate:modelValue":e[14]||(e[14]=l=>u.formData.acceptTerms=l),binary:!0,invalid:!!u.fieldErrors.acceptTerms,onChange:e[15]||(e[15]=l=>u.clearFieldError("acceptTerms"))},null,8,["modelValue","invalid"]),e[21]||(e[21]=r("label",{for:"acceptTerms",class:"ml-2 block text-sm text-surface-700"},[p(" Я принимаю "),r("a",{href:"/terms",class:"font-medium text-primary-600 hover:text-primary-500"}," условия использования "),p(" и "),r("a",{href:"/privacy",class:"font-medium text-primary-600 hover:text-primary-500"}," политику конфиденциальности ")],-1))]),u.fieldErrors.acceptTerms?(n(),d("small",X,c(u.fieldErrors.acceptTerms),1)):f("",!0),r("div",null,[m(u.Button,{type:"submit",disabled:u.isSubmitting||!u.isFormValid,loading:u.isSubmitting,label:"Зарегистрироваться",class:"w-full",size:"large"},null,8,["disabled","loading"])]),e[22]||(e[22]=r("div",{class:"text-center"},[r("p",{class:"text-sm text-surface-600"},[p(" Уже есть аккаунт? "),r("a",{href:"/admin/login",class:"font-medium text-primary-600 hover:text-primary-500"}," Войти в систему ")])],-1))],32)]),_:1})])])}const P4=O(z,[["render",Y]]);export{P4 as default};
