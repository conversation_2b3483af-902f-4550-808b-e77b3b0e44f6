import{u as X}from"./useTrpc.CcBnDuWb.js";import eu from"./Card.DllwvXut.js";import{V as tu}from"./Checkbox.Czip7_Ii.js";import{I as lu}from"./InputText.CPqCR4in.js";import au from"./Button.CplYapw1.js";import{S as ou}from"./Select.B5f7pqRM.js";import{V as ru,a as nu,b as su}from"./Tab.DSDMNzbv.js";import{s as iu,_ as J,p as du}from"./utils.BWEB-mtU.js";import{B as mu,c as b,o as s,j as U,m as uu,d as Q,g as M,w as n,u as yu,F as h,b as y,q as Vu,a0 as Eu,a as t,e as a,f as d,r as H,n as Cu}from"./index.CpC-7sc3.js";import{r as p,n as Bu,t as m}from"./reactivity.esm-bundler.Bx7uHohy.js";import{a as ku}from"./runtime-dom.esm-bundler.0NVQG2L5.js";import{D as cu}from"./DangerButton.DyBZF5lv.js";import{I as bu}from"./Icon.DGPcirKX.js";import{M as Au}from"./MultiSelect.fjeRlUGk.js";import"./trpc.CMxyjkwB.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";import"./index.CwY1vywt.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.CyH7ziOX.js";import"./index.DqILEIKx.js";import"./index.DV5zenSI.js";import"./index.DBjPSdxz.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.B3SmZZpj.js";import"./index.DUcQAuYR.js";/* empty css                            */import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";var Du={root:"p-tabpanels"},Tu=mu.extend({name:"tabpanels",classes:Du}),Iu={name:"BaseTabPanels",extends:iu,props:{},style:Tu,provide:function(){return{$pcTabPanels:this,$parentInstance:this}}},fu={name:"TabPanels",extends:Iu,inheritAttrs:!1};function Fu(c,e,V,u,C,x){return s(),b("div",uu({class:c.cx("root"),role:"presentation"},c.ptmi("root")),[U(c.$slots,"default")],16)}fu.render=Fu;const wu=Q({__name:"TabPanels",setup(c,{expose:e}){e();const u={theme:p({root:`bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0
        pt-[0.875rem] pb-[1.125rem] px-[1.125rem] outline-none`}),get TabPanels(){return fu},get ptViewMerge(){return du}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}});function _u(c,e,V,u,C,x){return s(),M(u.TabPanels,{unstyled:"",pt:u.theme,ptOptions:{mergeProps:u.ptViewMerge}},{default:n(()=>[U(c.$slots,"default")]),_:3},8,["pt","ptOptions"])}const pu=J(wu,[["render",_u]]);var Pu={root:function(e){var V=e.instance;return["p-tabpanel",{"p-tabpanel-active":V.active}]}},Su=mu.extend({name:"tabpanel",classes:Pu}),Ru={name:"BaseTabPanel",extends:iu,props:{value:{type:[String,Number],default:void 0},as:{type:[String,Object],default:"DIV"},asChild:{type:Boolean,default:!1},header:null,headerStyle:null,headerClass:null,headerProps:null,headerActionProps:null,contentStyle:null,contentClass:null,contentProps:null,disabled:Boolean},style:Su,provide:function(){return{$pcTabPanel:this,$parentInstance:this}}},xu={name:"TabPanel",extends:Ru,inheritAttrs:!1,inject:["$pcTabs"],computed:{active:function(){var e;return yu((e=this.$pcTabs)===null||e===void 0?void 0:e.d_value,this.value)},id:function(){var e;return"".concat((e=this.$pcTabs)===null||e===void 0?void 0:e.$id,"_tabpanel_").concat(this.value)},ariaLabelledby:function(){var e;return"".concat((e=this.$pcTabs)===null||e===void 0?void 0:e.$id,"_tab_").concat(this.value)},attrs:function(){return uu(this.a11yAttrs,this.ptmi("root",this.ptParams))},a11yAttrs:function(){var e;return{id:this.id,tabindex:(e=this.$pcTabs)===null||e===void 0?void 0:e.tabindex,role:"tabpanel","aria-labelledby":this.ariaLabelledby,"data-pc-name":"tabpanel","data-p-active":this.active}},ptParams:function(){return{context:{active:this.active}}}}};function Uu(c,e,V,u,C,x){var l,v;return x.$pcTabs?(s(),b(h,{key:1},[c.asChild?U(c.$slots,"default",{key:1,class:Bu(c.cx("root")),active:x.active,a11yAttrs:x.a11yAttrs}):(s(),b(h,{key:0},[!((l=x.$pcTabs)!==null&&l!==void 0&&l.lazy)||x.active?Vu((s(),M(Eu(c.as),uu({key:0,class:c.cx("root")},x.attrs),{default:n(function(){return[U(c.$slots,"default")]}),_:3},16,["class"])),[[ku,(v=x.$pcTabs)!==null&&v!==void 0&&v.lazy?!0:x.active]]):y("",!0)],64))],64)):U(c.$slots,"default",{key:0})}xu.render=Uu;const Mu=Q({__name:"TabPanel",setup(c,{expose:e}){e();const V=c,u=p({root:""}),C={props:V,theme:u,get TabPanel(){return xu},get ptViewMerge(){return du}};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}});function hu(c,e,V,u,C,x){return s(),M(u.TabPanel,{value:u.props.value,unstyled:"",pt:u.theme,ptOptions:{mergeProps:u.ptViewMerge}},{default:n(()=>[U(c.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const vu=J(Mu,[["render",hu]]),Lu=Q({__name:"SimplifiedImportPanel",setup(c,{expose:e}){e();const{importExport:V,attributeTemplates:u}=X(),C=p(null),x=p(!1),l=p(!1),v=p(!1),D=p(null),B=p(null),k=p(0),S=p({createMissingRefs:!0,onConflict:"upsert",autoLinkToParts:!1,createMissingTemplates:!0}),T=p({includeSampleData:!0,categorySlug:"",brandSlug:""}),I=p([]),j=p([]),G=p({Brand:!0,PartCategory:!0,AttributeGroup:!0,AttributeTemplate:!0,AttributeSynonymGroup:!1,AttributeSynonym:!1,Part:!0,CatalogItem:!0,EquipmentModel:!1,PartAttribute:!0,CatalogItemAttribute:!0,EquipmentModelAttribute:!1,PartApplicability:!0,EquipmentApplicability:!1}),W=[{label:"Создать или обновить (upsert)",value:"upsert"},{label:"Только обновить (update_only)",value:"update_only"},{label:"Пропустить (skip)",value:"skip"},{label:"Ошибка (error)",value:"error"}];Cu(async()=>{k.value=0,await z()});const Y=o=>{if(o===0)return"0 Bytes";const r=1024,E=["Bytes","KB","MB","GB"],g=Math.floor(Math.log(o)/Math.log(r));return parseFloat((o/Math.pow(r,g)).toFixed(2))+" "+E[g]},Z=()=>{C.value=null,B.value=null},z=async()=>{v.value=!0;try{const o=await u.findMany({limit:1e3});o&&o.templates&&(j.value=o.templates.map(r=>({label:`${r.title} (${r.name})`,value:r.name})))}catch(o){console.error("Failed to load attribute templates:",o)}finally{v.value=!1}},L=async()=>{l.value=!0;try{if(k.value===0){const F={includeSampleData:T.value.includeSampleData,attributeTemplates:I.value.length>0?I.value:void 0,categorySlug:T.value.categorySlug||void 0,brandSlug:T.value.brandSlug||void 0},_=await V.createSimplifiedTemplate(F);if(!_)return;const w=i(_.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),P=URL.createObjectURL(w),A=document.createElement("a");A.href=P,A.download=_.fileName,A.click(),URL.revokeObjectURL(P);return}const o=await V.exportTemplate({include:G.value});if(!o)return;const r=i(o.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),E=URL.createObjectURL(r),g=document.createElement("a");g.href=E,g.download=o.fileName,g.click(),URL.revokeObjectURL(E)}catch(o){console.error("Create template failed:",o)}finally{l.value=!1}},$=o=>{const r=o.target;C.value=r.files?.[0]??null,B.value=null};function O(o){return new Promise((r,E)=>{const g=new FileReader;g.onload=()=>r(String(g.result).split(",")[1]||""),g.onerror=E,g.readAsDataURL(o)})}const N=async()=>{if(C.value){D.value="validate",x.value=!0;try{const o=await O(C.value),r=await V.validateSimplifiedImport({base64:o,options:S.value});B.value=r}catch(o){console.error("Validation failed:",o)}finally{x.value=!1,D.value=null}}},R=async()=>{if(C.value){D.value="execute",x.value=!0;try{const o=await O(C.value),r=await V.simplifiedImport({base64:o,options:S.value});if(!r)return;B.value=r,r.reportBase64&&q(r.reportBase64)}catch(o){console.error("Execute failed:",o)}finally{x.value=!1,D.value=null}}};function q(o){const r=i(o,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),E=URL.createObjectURL(r),g=document.createElement("a");g.href=E,g.download=`simplified-import-report-${Date.now()}.xlsx`,g.click(),URL.revokeObjectURL(E)}function i(o,r="",E=512){const g=atob(o),F=[];for(let w=0;w<g.length;w+=E){const P=g.slice(w,w+E),A=new Array(P.length);for(let K=0;K<P.length;K++)A[K]=P.charCodeAt(K);const gu=new Uint8Array(A);F.push(gu)}return new Blob(F,{type:r})}const f={importExport:V,attributeTemplates:u,file:C,loading:x,loadingTemplate:l,loadingTemplates:v,currentAction:D,result:B,activeTabIndex:k,importOptions:S,templateOptions:T,selectedTemplates:I,availableTemplates:j,universalInclude:G,conflictOptions:W,formatFileSize:Y,clearFile:Z,loadAttributeTemplates:z,onCreateTemplate:L,onFileChange:$,fileToBase64:O,onValidate:N,onExecute:R,downloadReport:q,b64toBlob:i,VCard:eu,VCheckbox:tu,VInputText:lu,VButton:au,VSelect:ou,VMultiSelect:Au,DangerButton:cu,Icon:bu,VTabs:su,VTabList:nu,VTab:ru,VTabPanels:pu,VTabPanel:vu};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}}),Ou={class:"flex items-center gap-2 mb-4"},qu={class:"space-y-6"},ju={class:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},Gu={class:"flex items-start gap-3"},zu={class:"space-y-4"},Nu={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ku={class:"space-y-2"},Xu={class:"flex items-center space-x-2"},Hu={class:"space-y-4"},Ju={class:"grid grid-cols-1 gap-4"},Qu={class:"grid grid-cols-2 md:grid-cols-3 gap-2 text-sm"},Wu={class:"flex items-center gap-2"},Yu={class:"flex items-center gap-2"},Zu={class:"flex items-center gap-2"},$u={class:"flex items-center gap-2"},ue={class:"flex items-center gap-2"},ee={class:"flex items-center gap-2"},te={class:"flex items-center gap-2"},le={class:"flex items-center gap-2"},ae={class:"flex items-center gap-2"},oe={class:"flex items-center gap-2"},re={class:"flex items-center gap-2"},ne={class:"flex items-center gap-2"},se={class:"flex items-center gap-2"},ie={class:"flex items-center gap-2"},de={class:"space-y-4"},me={class:"flex items-center gap-2"},ce={key:0,class:"mt-2 text-sm text-surface-600"},be={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},fe={class:"space-y-2 pt-6"},pe={class:"flex items-center space-x-2"},xe={class:"flex items-center space-x-2"},ve={class:"flex items-center space-x-2"},ge={class:"flex flex-col gap-2"},ye={key:0,class:"text-xs text-surface-500 text-center"},Ve={key:0,class:"space-y-3"},Ee={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},Ce={class:"p-3 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800"},Be={class:"text-lg font-bold text-green-800 dark:text-green-200"},ke={class:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800"},Ae={class:"text-lg font-bold text-blue-800 dark:text-blue-200"},De={class:"p-3 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800"},Te={class:"text-lg font-bold text-purple-800 dark:text-purple-200"},Ie={class:"p-3 bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-800"},Fe={class:"text-lg font-bold text-orange-800 dark:text-orange-200"},we={key:0,class:"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800"},_e={class:"text-sm text-yellow-700 dark:text-yellow-300"},Pe={key:1,class:"space-y-2"},Se={class:"max-h-32 overflow-y-auto space-y-1"},Re={key:0,class:"text-xs text-surface-500"},Ue={key:2,class:"space-y-2"},Me={class:"max-h-24 overflow-y-auto space-y-1"},he={key:0,class:"text-xs text-surface-500"},Le={key:3};function Oe(c,e,V,u,C,x){return s(),M(u.VCard,{class:"p-6"},{title:n(()=>[t("div",Ou,[a(u.Icon,{name:"file-spreadsheet",class:"w-5 h-5 text-primary-600"}),e[24]||(e[24]=t("h2",{class:"text-lg font-semibold"},"Упрощенный импорт каталога",-1))])]),content:n(()=>[t("div",qu,[t("div",ju,[t("div",Gu,[a(u.Icon,{name:"info",class:"w-5 h-5 text-blue-600 mt-0.5"}),e[25]||(e[25]=t("div",null,[t("h3",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1"}," Форматы импорта "),t("p",{class:"text-sm text-blue-700 dark:text-blue-200"},[d(' 1) Простой: один лист "CatalogItems". Атрибуты — колонки с префиксом "attr_" (sku, brand, attr_inner_diameter, ...) '),t("br"),d("2) Универсальный: несколько листов по моделям (Brand, PartCategory, Part, CatalogItem, ...) ")])],-1))])]),a(u.VTabs,{value:u.activeTabIndex,"onUpdate:value":e[18]||(e[18]=l=>u.activeTabIndex=l)},{default:n(()=>[a(u.VTabList,null,{default:n(()=>[a(u.VTab,{value:0},{default:n(()=>e[26]||(e[26]=[d("Простой шаблон")])),_:1,__:[26]}),a(u.VTab,{value:1},{default:n(()=>e[27]||(e[27]=[d("Универсальный шаблон")])),_:1,__:[27]})]),_:1}),a(u.VTabPanels,null,{default:n(()=>[a(u.VTabPanel,{value:0},{default:n(()=>[t("div",zu,[e[31]||(e[31]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Создать шаблон",-1)),t("div",Nu,[t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Атрибуты для включения ",-1)),a(u.VMultiSelect,{modelValue:u.selectedTemplates,"onUpdate:modelValue":e[0]||(e[0]=l=>u.selectedTemplates=l),options:u.availableTemplates,optionLabel:"label",optionValue:"value",placeholder:"Выберите шаблоны атрибутов",filter:"",loading:u.loadingTemplates,class:"w-full"},null,8,["modelValue","options","loading"])]),t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Настройки шаблона ",-1)),t("div",Ku,[t("div",Xu,[a(u.VCheckbox,{modelValue:u.templateOptions.includeSampleData,"onUpdate:modelValue":e[1]||(e[1]=l=>u.templateOptions.includeSampleData=l),inputId:"includeSampleData",binary:!0},null,8,["modelValue"]),e[29]||(e[29]=t("label",{for:"includeSampleData",class:"text-sm"},"Включить примерные данные",-1))]),a(u.VInputText,{modelValue:u.templateOptions.categorySlug,"onUpdate:modelValue":e[2]||(e[2]=l=>u.templateOptions.categorySlug=l),placeholder:"Категория для примеров (slug)",class:"w-full"},null,8,["modelValue"]),a(u.VInputText,{modelValue:u.templateOptions.brandSlug,"onUpdate:modelValue":e[3]||(e[3]=l=>u.templateOptions.brandSlug=l),placeholder:"Бренд для примеров (slug)",class:"w-full"},null,8,["modelValue"])])])]),a(u.VButton,{onClick:u.onCreateTemplate,disabled:u.loadingTemplate,loading:u.loadingTemplate,severity:"info",outlined:"",class:"w-full"},{default:n(()=>[d(m(u.loadingTemplate?"Создание...":"Скачать упрощенный шаблон"),1)]),_:1},8,["disabled","loading"])])]),_:1}),a(u.VTabPanel,{value:1},{default:n(()=>[t("div",Hu,[e[47]||(e[47]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Создать универсальный шаблон",-1)),t("div",Ju,[t("div",null,[e[46]||(e[46]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Выберите листы моделей",-1)),t("div",Qu,[t("label",Wu,[a(u.VCheckbox,{modelValue:u.universalInclude.Brand,"onUpdate:modelValue":e[4]||(e[4]=l=>u.universalInclude.Brand=l),binary:!0},null,8,["modelValue"]),e[32]||(e[32]=d("Brand"))]),t("label",Yu,[a(u.VCheckbox,{modelValue:u.universalInclude.PartCategory,"onUpdate:modelValue":e[5]||(e[5]=l=>u.universalInclude.PartCategory=l),binary:!0},null,8,["modelValue"]),e[33]||(e[33]=d("PartCategory"))]),t("label",Zu,[a(u.VCheckbox,{modelValue:u.universalInclude.AttributeGroup,"onUpdate:modelValue":e[6]||(e[6]=l=>u.universalInclude.AttributeGroup=l),binary:!0},null,8,["modelValue"]),e[34]||(e[34]=d("AttributeGroup"))]),t("label",$u,[a(u.VCheckbox,{modelValue:u.universalInclude.AttributeTemplate,"onUpdate:modelValue":e[7]||(e[7]=l=>u.universalInclude.AttributeTemplate=l),binary:!0},null,8,["modelValue"]),e[35]||(e[35]=d("AttributeTemplate"))]),t("label",ue,[a(u.VCheckbox,{modelValue:u.universalInclude.AttributeSynonymGroup,"onUpdate:modelValue":e[8]||(e[8]=l=>u.universalInclude.AttributeSynonymGroup=l),binary:!0},null,8,["modelValue"]),e[36]||(e[36]=d("AttributeSynonymGroup"))]),t("label",ee,[a(u.VCheckbox,{modelValue:u.universalInclude.AttributeSynonym,"onUpdate:modelValue":e[9]||(e[9]=l=>u.universalInclude.AttributeSynonym=l),binary:!0},null,8,["modelValue"]),e[37]||(e[37]=d("AttributeSynonym"))]),t("label",te,[a(u.VCheckbox,{modelValue:u.universalInclude.Part,"onUpdate:modelValue":e[10]||(e[10]=l=>u.universalInclude.Part=l),binary:!0},null,8,["modelValue"]),e[38]||(e[38]=d("Part"))]),t("label",le,[a(u.VCheckbox,{modelValue:u.universalInclude.CatalogItem,"onUpdate:modelValue":e[11]||(e[11]=l=>u.universalInclude.CatalogItem=l),binary:!0},null,8,["modelValue"]),e[39]||(e[39]=d("CatalogItem"))]),t("label",ae,[a(u.VCheckbox,{modelValue:u.universalInclude.EquipmentModel,"onUpdate:modelValue":e[12]||(e[12]=l=>u.universalInclude.EquipmentModel=l),binary:!0},null,8,["modelValue"]),e[40]||(e[40]=d("EquipmentModel"))]),t("label",oe,[a(u.VCheckbox,{modelValue:u.universalInclude.PartAttribute,"onUpdate:modelValue":e[13]||(e[13]=l=>u.universalInclude.PartAttribute=l),binary:!0},null,8,["modelValue"]),e[41]||(e[41]=d("PartAttribute"))]),t("label",re,[a(u.VCheckbox,{modelValue:u.universalInclude.CatalogItemAttribute,"onUpdate:modelValue":e[14]||(e[14]=l=>u.universalInclude.CatalogItemAttribute=l),binary:!0},null,8,["modelValue"]),e[42]||(e[42]=d("CatalogItemAttribute"))]),t("label",ne,[a(u.VCheckbox,{modelValue:u.universalInclude.EquipmentModelAttribute,"onUpdate:modelValue":e[15]||(e[15]=l=>u.universalInclude.EquipmentModelAttribute=l),binary:!0},null,8,["modelValue"]),e[43]||(e[43]=d("EquipmentModelAttribute"))]),t("label",se,[a(u.VCheckbox,{modelValue:u.universalInclude.PartApplicability,"onUpdate:modelValue":e[16]||(e[16]=l=>u.universalInclude.PartApplicability=l),binary:!0},null,8,["modelValue"]),e[44]||(e[44]=d("PartApplicability"))]),t("label",ie,[a(u.VCheckbox,{modelValue:u.universalInclude.EquipmentApplicability,"onUpdate:modelValue":e[17]||(e[17]=l=>u.universalInclude.EquipmentApplicability=l),binary:!0},null,8,["modelValue"]),e[45]||(e[45]=d("EquipmentApplicability"))])])])]),a(u.VButton,{onClick:u.onCreateTemplate,disabled:u.loadingTemplate,loading:u.loadingTemplate,severity:"info",outlined:"",class:"w-full"},{default:n(()=>[d(m(u.loadingTemplate?"Создание...":"Скачать универсальный шаблон"),1)]),_:1},8,["disabled","loading"])])]),_:1})]),_:1})]),_:1},8,["value"]),t("div",de,[e[63]||(e[63]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Импорт файла",-1)),t("div",null,[e[49]||(e[49]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Файл Excel (.xlsx) ",-1)),t("div",me,[t("input",{type:"file",onChange:u.onFileChange,accept:".xlsx",class:"flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),u.file?(s(),M(u.DangerButton,{key:0,onClick:u.clearFile,severity:"secondary",outlined:"",size:"small"},{default:n(()=>e[48]||(e[48]=[d("Очистить")])),_:1,__:[48]})):y("",!0)]),u.file?(s(),b("div",ce," Выбран файл: "+m(u.file.name)+" ("+m(u.formatFileSize(u.file.size))+") ",1)):y("",!0)]),t("div",be,[t("div",null,[e[50]||(e[50]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Режим конфликтов ",-1)),a(u.VSelect,{modelValue:u.importOptions.onConflict,"onUpdate:modelValue":e[19]||(e[19]=l=>u.importOptions.onConflict=l),options:u.conflictOptions,optionLabel:"label",optionValue:"value",placeholder:"Выберите режим",class:"w-full"},null,8,["modelValue"])]),t("div",fe,[t("div",pe,[a(u.VCheckbox,{modelValue:u.importOptions.createMissingRefs,"onUpdate:modelValue":e[20]||(e[20]=l=>u.importOptions.createMissingRefs=l),inputId:"createMissingRefs",binary:!0},null,8,["modelValue"]),e[51]||(e[51]=t("label",{for:"createMissingRefs",class:"text-sm"},"Создавать отсутствующие ссылки",-1))]),t("div",xe,[a(u.VCheckbox,{modelValue:u.importOptions.createMissingTemplates,"onUpdate:modelValue":e[21]||(e[21]=l=>u.importOptions.createMissingTemplates=l),inputId:"createMissingTemplates",binary:!0},null,8,["modelValue"]),e[52]||(e[52]=t("label",{for:"createMissingTemplates",class:"text-sm"},"Создавать новые шаблоны атрибутов",-1))]),t("div",ve,[a(u.VCheckbox,{modelValue:u.importOptions.autoLinkToParts,"onUpdate:modelValue":e[22]||(e[22]=l=>u.importOptions.autoLinkToParts=l),inputId:"autoLinkToParts",binary:!0},null,8,["modelValue"]),e[53]||(e[53]=t("label",{for:"autoLinkToParts",class:"text-sm"},"Автоматически связывать с группами",-1))])])]),t("div",ge,[a(u.VButton,{onClick:u.onValidate,disabled:u.loading||!u.file||u.activeTabIndex!==0,loading:u.loading&&u.currentAction==="validate",severity:"info",class:"w-full"},{default:n(()=>[d(m(u.loading&&u.currentAction==="validate"?"Проверка...":"Проверить файл (Простой режим)"),1)]),_:1},8,["disabled","loading"]),a(u.VButton,{onClick:u.onExecute,disabled:u.loading||!u.file,loading:u.loading&&u.currentAction==="execute",severity:"success",class:"w-full"},{default:n(()=>[d(m(u.loading&&u.currentAction==="execute"?"Выполняю...":"Выполнить упрощенный импорт"),1)]),_:1},8,["disabled","loading"]),u.activeTabIndex!==0?(s(),b("div",ye,"Валидация доступна только для простого режима")):y("",!0)]),u.result?(s(),b("div",Ve,[e[62]||(e[62]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Результат:",-1)),t("div",Ee,[t("div",Ce,[e[54]||(e[54]=t("div",{class:"text-xs font-medium text-green-700 dark:text-green-300"},"Создано позиций",-1)),t("div",Be,m(u.result.counters.catalogItemsCreated),1)]),t("div",ke,[e[55]||(e[55]=t("div",{class:"text-xs font-medium text-blue-700 dark:text-blue-300"},"Обновлено позиций",-1)),t("div",Ae,m(u.result.counters.catalogItemsUpdated),1)]),t("div",De,[e[56]||(e[56]=t("div",{class:"text-xs font-medium text-purple-700 dark:text-purple-300"},"Атрибутов добавлено",-1)),t("div",Te,m(u.result.counters.attributesCreated),1)]),t("div",Ie,[e[57]||(e[57]=t("div",{class:"text-xs font-medium text-orange-700 dark:text-orange-300"},"Новых шаблонов",-1)),t("div",Fe,m(u.result.counters.templatesCreated),1)])]),u.result.createdTemplates?.length?(s(),b("div",we,[e[58]||(e[58]=t("div",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1"},"Созданные шаблоны атрибутов:",-1)),t("div",_e,m(u.result.createdTemplates.join(", ")),1)])):y("",!0),u.result.errors?.length?(s(),b("div",Pe,[e[59]||(e[59]=t("h4",{class:"text-sm font-medium text-red-700 dark:text-red-400"},"Ошибки:",-1)),t("div",Se,[(s(!0),b(h,null,H(u.result.errors.slice(0,10),(l,v)=>(s(),b("div",{key:v,class:"text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"},m(l.field?`${l.field} (строка ${l.rowIndex})`:`Строка ${l.rowIndex}`)+": "+m(l.message),1))),128)),u.result.errors.length>10?(s(),b("div",Re,"... и еще "+m(u.result.errors.length-10)+" ошибок",1)):y("",!0)])])):y("",!0),u.result.warnings?.length?(s(),b("div",Ue,[e[60]||(e[60]=t("h4",{class:"text-sm font-medium text-yellow-700 dark:text-yellow-400"},"Предупреждения:",-1)),t("div",Me,[(s(!0),b(h,null,H(u.result.warnings.slice(0,5),(l,v)=>(s(),b("div",{key:v,class:"text-xs p-2 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 rounded"},m(l.field?`${l.field} (строка ${l.rowIndex})`:`Строка ${l.rowIndex}`)+": "+m(l.message),1))),128)),u.result.warnings.length>5?(s(),b("div",he,"... и еще "+m(u.result.warnings.length-5)+" предупреждений",1)):y("",!0)])])):y("",!0),u.result.reportBase64?(s(),b("div",Le,[a(u.VButton,{onClick:e[23]||(e[23]=l=>u.downloadReport(u.result.reportBase64)),severity:"secondary",outlined:"",size:"small"},{default:n(()=>e[61]||(e[61]=[d("Скачать отчёт")])),_:1,__:[61]})])):y("",!0)])):y("",!0)])])]),_:1})}const qe=J(Lu,[["render",Oe]]),je=Q({__name:"ImportExportPanel",setup(c,{expose:e}){e();const{importExport:V,excelImport:u}=X(),C=p("simplified"),x=p({Brand:!0,PartCategory:!0,AttributeGroup:!1,AttributeTemplate:!0,AttributeSynonymGroup:!1,AttributeSynonym:!1,Part:!0,CatalogItem:!0,EquipmentModel:!0,PartAttribute:!1,CatalogItemAttribute:!1,EquipmentModelAttribute:!1,PartApplicability:!1,EquipmentApplicability:!1}),l=p(""),v=p("upsert"),D=p(!1),B=p(null),k=p(!1),S=p(!1),T=p(null),I=p(null),j=[{label:"Создать или обновить (upsert)",value:"upsert"},{label:"Только обновить (update_only)",value:"update_only"},{label:"Пропустить (skip)",value:"skip"},{label:"Ошибка (error)",value:"error"}],G=i=>{if(i===0)return"0 Bytes";const f=1024,o=["Bytes","KB","MB","GB"],r=Math.floor(Math.log(i)/Math.log(f));return parseFloat((i/Math.pow(f,r)).toFixed(2))+" "+o[r]},W=()=>{B.value=null,I.value=null},Y=async()=>{k.value=!0;try{const i=l.value.split(",").map(g=>g.trim()).filter(Boolean),f=await V.exportXlsx({include:x.value,filters:{brandSlugs:i},meta:{createMissingRefs:D.value,onConflict:v.value}});if(!f)return;const o=R(f.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),r=URL.createObjectURL(o),E=document.createElement("a");E.href=r,E.download=f.fileName,E.click(),URL.revokeObjectURL(r)}catch(i){console.error("Export failed:",i)}finally{k.value=!1}},Z=async()=>{S.value=!0;try{const i=await V.exportTemplate({include:x.value});if(!i)return;const f=R(i.base64,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),o=URL.createObjectURL(f),r=document.createElement("a");r.href=o,r.download=i.fileName,r.click(),URL.revokeObjectURL(o)}catch(i){console.error("Export template failed:",i)}finally{S.value=!1}},z=i=>{const f=i.target;B.value=f.files?.[0]??null,I.value=null};function L(i){return new Promise((f,o)=>{const r=new FileReader;r.onload=()=>f(String(r.result).split(",")[1]||""),r.onerror=o,r.readAsDataURL(i)})}const $=async()=>{if(B.value){T.value="dryRun",k.value=!0;try{const i=await L(B.value),f=await X().excelImport.dryRun({base64:i,overrides:{createMissingRefs:D.value,onConflict:v.value}});I.value=f}catch(i){console.error("Dry run failed:",i)}finally{k.value=!1,T.value=null}}},O=async()=>{if(B.value){T.value="execute",k.value=!0;try{const i=await L(B.value),f=await X().excelImport.execute({base64:i,overrides:{createMissingRefs:D.value,onConflict:v.value}});if(!f)return;I.value=f,f.reportBase64&&N(f.reportBase64)}catch(i){console.error("Execute failed:",i)}finally{k.value=!1,T.value=null}}};function N(i){const f=R(i,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),o=URL.createObjectURL(f),r=document.createElement("a");r.href=o,r.download=`import-report-${Date.now()}.xlsx`,r.click(),URL.revokeObjectURL(o)}function R(i,f="",o=512){const r=atob(i),E=[];for(let F=0;F<r.length;F+=o){const _=r.slice(F,F+o),w=new Array(_.length);for(let A=0;A<_.length;A++)w[A]=_.charCodeAt(A);const P=new Uint8Array(w);E.push(P)}return new Blob(E,{type:f})}const q={importExport:V,excelImport:u,activeTab:C,include:x,brandSlugsInput:l,onConflict:v,createMissingRefs:D,file:B,loading:k,loadingTemplate:S,currentAction:T,dryRunResult:I,conflictOptions:j,formatFileSize:G,clearFile:W,onExport:Y,onExportTemplate:Z,onFileChange:z,fileToBase64:L,onDryRun:$,onExecute:O,downloadReport:N,b64toBlob:R,VCard:eu,VCheckbox:tu,VInputText:lu,VButton:au,VSelect:ou,VTabs:su,VTabList:nu,VTab:ru,VTabPanels:pu,VTabPanel:vu,DangerButton:cu,Icon:bu,SimplifiedImportPanel:qe};return Object.defineProperty(q,"__isScriptSetup",{enumerable:!1,value:!0}),q}}),Ge={class:"w-full max-w-7xl"},ze={class:"mb-6"},Ne={class:"grid grid-cols-1 xl:grid-cols-3 gap-6"},Ke={class:"space-y-4"},Xe={class:"grid grid-cols-1 gap-2"},He={class:"flex items-center space-x-2"},Je={class:"flex items-center space-x-2"},Qe={class:"flex items-center space-x-2"},We={class:"flex items-center space-x-2"},Ye={class:"flex items-center space-x-2"},Ze={class:"flex items-center space-x-2"},$e={class:"flex items-center space-x-2"},u4={class:"flex items-center space-x-2"},e4={class:"flex items-center space-x-2"},t4={class:"grid grid-cols-1 gap-2"},l4={class:"flex items-center space-x-2"},a4={class:"flex items-center space-x-2"},o4={class:"flex items-center space-x-2"},r4={class:"flex items-center space-x-2"},n4={class:"flex items-center space-x-2"},s4={class:"flex flex-col gap-2"},i4={class:"space-y-4"},d4={class:"flex items-center gap-2"},m4={key:0,class:"mt-2 text-sm text-surface-600"},c4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},b4={class:"flex items-center space-x-2 pt-6"},f4={class:"flex flex-col gap-2"},p4={key:0,class:"space-y-3"},x4={key:0,class:"grid grid-cols-2 gap-2"},v4={class:"text-xs font-medium text-surface-700 dark:text-surface-300"},g4={class:"text-xs text-surface-600 dark:text-surface-400"},y4={key:0},V4={key:1},E4={key:2},C4={key:1,class:"space-y-2"},B4={class:"max-h-32 overflow-y-auto space-y-1"},k4={key:0,class:"text-xs text-surface-500"},A4={key:2};function D4(c,e,V,u,C,x){return s(),b("div",Ge,[t("div",ze,[a(u.VTabs,{modelValue:u.activeTab,"onUpdate:modelValue":e[18]||(e[18]=l=>u.activeTab=l),class:"w-full"},{default:n(()=>[a(u.VTabList,{class:"grid grid-cols-2 w-full"},{default:n(()=>[a(u.VTab,{value:"simplified",class:"flex items-center gap-2"},{default:n(()=>[a(u.Icon,{name:"file-spreadsheet",class:"w-4 h-4"}),e[19]||(e[19]=d(" Упрощенный импорт "))]),_:1,__:[19]}),a(u.VTab,{value:"advanced",class:"flex items-center gap-2"},{default:n(()=>[a(u.Icon,{name:"settings",class:"w-4 h-4"}),e[20]||(e[20]=d(" Расширенный импорт/экспорт "))]),_:1,__:[20]})]),_:1}),a(u.VTabPanels,{class:"mt-6"},{default:n(()=>[a(u.VTabPanel,{value:"simplified"},{default:n(()=>[a(u.SimplifiedImportPanel)]),_:1}),a(u.VTabPanel,{value:"advanced"},{default:n(()=>[t("div",Ne,[a(u.VCard,{class:"p-6"},{title:n(()=>e[21]||(e[21]=[t("h2",{class:"text-lg font-semibold mb-4"},"Экспорт каталога",-1)])),content:n(()=>[t("div",Ke,[t("div",null,[e[31]||(e[31]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Основные сущности",-1)),t("div",Xe,[t("div",He,[a(u.VCheckbox,{modelValue:u.include.Brand,"onUpdate:modelValue":e[0]||(e[0]=l=>u.include.Brand=l),inputId:"brand",binary:!0},null,8,["modelValue"]),e[22]||(e[22]=t("label",{for:"brand",class:"text-sm"},"Бренды",-1))]),t("div",Je,[a(u.VCheckbox,{modelValue:u.include.PartCategory,"onUpdate:modelValue":e[1]||(e[1]=l=>u.include.PartCategory=l),inputId:"partCategory",binary:!0},null,8,["modelValue"]),e[23]||(e[23]=t("label",{for:"partCategory",class:"text-sm"},"Категории запчастей",-1))]),t("div",Qe,[a(u.VCheckbox,{modelValue:u.include.AttributeGroup,"onUpdate:modelValue":e[2]||(e[2]=l=>u.include.AttributeGroup=l),inputId:"attributeGroup",binary:!0},null,8,["modelValue"]),e[24]||(e[24]=t("label",{for:"attributeGroup",class:"text-sm"},"Группы атрибутов",-1))]),t("div",We,[a(u.VCheckbox,{modelValue:u.include.AttributeTemplate,"onUpdate:modelValue":e[3]||(e[3]=l=>u.include.AttributeTemplate=l),inputId:"attributeTemplate",binary:!0},null,8,["modelValue"]),e[25]||(e[25]=t("label",{for:"attributeTemplate",class:"text-sm"},"Шаблоны атрибутов",-1))]),t("div",Ye,[a(u.VCheckbox,{modelValue:u.include.AttributeSynonymGroup,"onUpdate:modelValue":e[4]||(e[4]=l=>u.include.AttributeSynonymGroup=l),inputId:"synonymGroup",binary:!0},null,8,["modelValue"]),e[26]||(e[26]=t("label",{for:"synonymGroup",class:"text-sm"},"Группы синонимов",-1))]),t("div",Ze,[a(u.VCheckbox,{modelValue:u.include.AttributeSynonym,"onUpdate:modelValue":e[5]||(e[5]=l=>u.include.AttributeSynonym=l),inputId:"synonym",binary:!0},null,8,["modelValue"]),e[27]||(e[27]=t("label",{for:"synonym",class:"text-sm"},"Синонимы атрибутов",-1))]),t("div",$e,[a(u.VCheckbox,{modelValue:u.include.Part,"onUpdate:modelValue":e[6]||(e[6]=l=>u.include.Part=l),inputId:"part",binary:!0},null,8,["modelValue"]),e[28]||(e[28]=t("label",{for:"part",class:"text-sm"},"Группы взаимозаменяемости",-1))]),t("div",u4,[a(u.VCheckbox,{modelValue:u.include.CatalogItem,"onUpdate:modelValue":e[7]||(e[7]=l=>u.include.CatalogItem=l),inputId:"catalogItem",binary:!0},null,8,["modelValue"]),e[29]||(e[29]=t("label",{for:"catalogItem",class:"text-sm"},"Каталожные позиции",-1))]),t("div",e4,[a(u.VCheckbox,{modelValue:u.include.EquipmentModel,"onUpdate:modelValue":e[8]||(e[8]=l=>u.include.EquipmentModel=l),inputId:"equipmentModel",binary:!0},null,8,["modelValue"]),e[30]||(e[30]=t("label",{for:"equipmentModel",class:"text-sm"},"Модели техники",-1))])])]),t("div",null,[e[37]||(e[37]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Атрибуты и связи",-1)),t("div",t4,[t("div",l4,[a(u.VCheckbox,{modelValue:u.include.PartAttribute,"onUpdate:modelValue":e[9]||(e[9]=l=>u.include.PartAttribute=l),inputId:"partAttribute",binary:!0},null,8,["modelValue"]),e[32]||(e[32]=t("label",{for:"partAttribute",class:"text-sm"},"Атрибуты запчастей",-1))]),t("div",a4,[a(u.VCheckbox,{modelValue:u.include.CatalogItemAttribute,"onUpdate:modelValue":e[10]||(e[10]=l=>u.include.CatalogItemAttribute=l),inputId:"catalogItemAttribute",binary:!0},null,8,["modelValue"]),e[33]||(e[33]=t("label",{for:"catalogItemAttribute",class:"text-sm"},"Атрибуты позиций",-1))]),t("div",o4,[a(u.VCheckbox,{modelValue:u.include.EquipmentModelAttribute,"onUpdate:modelValue":e[11]||(e[11]=l=>u.include.EquipmentModelAttribute=l),inputId:"equipmentModelAttribute",binary:!0},null,8,["modelValue"]),e[34]||(e[34]=t("label",{for:"equipmentModelAttribute",class:"text-sm"},"Атрибуты техники",-1))]),t("div",r4,[a(u.VCheckbox,{modelValue:u.include.PartApplicability,"onUpdate:modelValue":e[12]||(e[12]=l=>u.include.PartApplicability=l),inputId:"partApplicability",binary:!0},null,8,["modelValue"]),e[35]||(e[35]=t("label",{for:"partApplicability",class:"text-sm"},"Применимость запчастей",-1))]),t("div",n4,[a(u.VCheckbox,{modelValue:u.include.EquipmentApplicability,"onUpdate:modelValue":e[13]||(e[13]=l=>u.include.EquipmentApplicability=l),inputId:"equipmentApplicability",binary:!0},null,8,["modelValue"]),e[36]||(e[36]=t("label",{for:"equipmentApplicability",class:"text-sm"},"Применимость к технике",-1))])])]),t("div",null,[e[38]||(e[38]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Фильтры",-1)),a(u.VInputText,{modelValue:u.brandSlugsInput,"onUpdate:modelValue":e[14]||(e[14]=l=>u.brandSlugsInput=l),placeholder:"Бренды через запятую (например: cat,komatsu)",class:"w-full"},null,8,["modelValue"])]),t("div",s4,[a(u.VButton,{onClick:u.onExport,disabled:u.loading,loading:u.loading,class:"w-full"},{default:n(()=>[d(m(u.loading?"Экспорт...":"Скачать данные"),1)]),_:1},8,["disabled","loading"]),a(u.VButton,{onClick:u.onExportTemplate,disabled:u.loading,loading:u.loadingTemplate,severity:"secondary",outlined:"",class:"w-full"},{default:n(()=>[d(m(u.loadingTemplate?"Создание...":"Скачать шаблон"),1)]),_:1},8,["disabled","loading"])])])]),_:1}),a(u.VCard,{class:"p-6"},{title:n(()=>e[39]||(e[39]=[t("h2",{class:"text-lg font-semibold mb-4"},"Импорт каталога",-1)])),content:n(()=>[t("div",i4,[t("div",null,[e[41]||(e[41]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Файл Excel (.xlsx) ",-1)),t("div",d4,[t("input",{type:"file",onChange:u.onFileChange,accept:".xlsx",class:"flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),u.file?(s(),M(u.DangerButton,{key:0,onClick:u.clearFile,severity:"secondary",outlined:"",size:"small"},{default:n(()=>e[40]||(e[40]=[d(" Очистить ")])),_:1,__:[40]})):y("",!0)]),u.file?(s(),b("div",m4," Выбран файл: "+m(u.file.name)+" ("+m(u.formatFileSize(u.file.size))+") ",1)):y("",!0)]),t("div",c4,[t("div",null,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Режим конфликтов ",-1)),a(u.VSelect,{modelValue:u.onConflict,"onUpdate:modelValue":e[15]||(e[15]=l=>u.onConflict=l),options:u.conflictOptions,optionLabel:"label",optionValue:"value",placeholder:"Выберите режим",class:"w-full"},null,8,["modelValue"])]),t("div",b4,[a(u.VCheckbox,{modelValue:u.createMissingRefs,"onUpdate:modelValue":e[16]||(e[16]=l=>u.createMissingRefs=l),inputId:"createMissingRefs",binary:!0},null,8,["modelValue"]),e[43]||(e[43]=t("label",{for:"createMissingRefs",class:"text-sm"},"Создавать отсутствующие ссылки",-1))])]),t("div",f4,[a(u.VButton,{onClick:u.onDryRun,disabled:u.loading||!u.file,loading:u.loading&&u.currentAction==="dryRun",severity:"info",class:"w-full"},{default:n(()=>[d(m(u.loading&&u.currentAction==="dryRun"?"Проверка...":"Проверить файл (Dry Run)"),1)]),_:1},8,["disabled","loading"]),a(u.VButton,{onClick:u.onExecute,disabled:u.loading||!u.file,loading:u.loading&&u.currentAction==="execute",severity:"success",class:"w-full"},{default:n(()=>[d(m(u.loading&&u.currentAction==="execute"?"Выполняю...":"Выполнить импорт"),1)]),_:1},8,["disabled","loading"])]),u.dryRunResult?(s(),b("div",p4,[e[46]||(e[46]=t("h3",{class:"text-sm font-medium text-surface-700 dark:text-surface-300"},"Результат:",-1)),u.dryRunResult.perSheet?(s(),b("div",x4,[(s(!0),b(h,null,H(u.dryRunResult.perSheet,(l,v)=>(s(),b("div",{key:v,class:"p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[t("div",v4,m(v),1),t("div",g4,[d(" Строк: "+m(l.rowsSeen)+" | Валидных: "+m(l.rowsValid)+" ",1),l.created?(s(),b("span",y4,"| Создано: "+m(l.created),1)):y("",!0),l.updated?(s(),b("span",V4,"| Обновлено: "+m(l.updated),1)):y("",!0),l.errorsCount?(s(),b("span",E4,"| Ошибок: "+m(l.errorsCount),1)):y("",!0)])]))),128))])):y("",!0),u.dryRunResult.errors?.length?(s(),b("div",C4,[e[44]||(e[44]=t("h4",{class:"text-sm font-medium text-red-700 dark:text-red-400"},"Ошибки:",-1)),t("div",B4,[(s(!0),b(h,null,H(u.dryRunResult.errors.slice(0,10),(l,v)=>(s(),b("div",{key:v,class:"text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"},m(l.sheet)+":"+m(l.rowIndex)+" - "+m(l.message),1))),128)),u.dryRunResult.errors.length>10?(s(),b("div",k4," ... и еще "+m(u.dryRunResult.errors.length-10)+" ошибок ",1)):y("",!0)])])):y("",!0),u.dryRunResult.reportBase64?(s(),b("div",A4,[a(u.VButton,{onClick:e[17]||(e[17]=l=>u.downloadReport(u.dryRunResult.reportBase64)),severity:"secondary",outlined:"",size:"small"},{default:n(()=>e[45]||(e[45]=[d(" Скачать отчёт ")])),_:1,__:[45]})])):y("",!0)])):y("",!0)])]),_:1}),a(u.VCard,{class:"p-6"},{title:n(()=>e[47]||(e[47]=[t("h2",{class:"text-lg font-semibold mb-4"},"История импортов",-1)])),content:n(()=>e[48]||(e[48]=[t("div",{class:"text-sm text-surface-600 dark:text-surface-400"}," Функционал истории импортов будет добавлен в следующей итерации ",-1)])),_:1})])]),_:1})]),_:1})]),_:1},8,["modelValue"])])])}const r0=J(je,[["render",D4]]);export{r0 as default};
