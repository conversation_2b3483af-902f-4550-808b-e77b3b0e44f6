var Ue=Object.create,de=Object.defineProperty,ze=Object.getOwnPropertyDescriptor,ye=Object.getOwnPropertyNames,Ve=Object.getPrototypeOf,$e=Object.prototype.hasOwnProperty,_=(e,t)=>function(){return t||(0,e[ye(e)[0]])((t={exports:{}}).exports,t),t.exports},Ke=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(var o=ye(t),s=0,i=o.length,u;s<i;s++)u=o[s],!$e.call(e,u)&&u!==r&&de(e,u,{get:(a=>t[a]).bind(null,u),enumerable:!(n=ze(t,u))||n.enumerable});return e},v=(e,t,r)=>(r=e!=null?Ue(Ve(e)):{},Ke(de(r,"default",{value:e,enumerable:!0}),e)),me=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(n){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},t.exports.__esModule=!0,t.exports.default=t.exports,r(n)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),We=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=me().default;function n(o,s){if(r(o)!="object"||!o)return o;var i=o[Symbol.toPrimitive];if(i!==void 0){var u=i.call(o,s||"default");if(r(u)!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return(s==="string"?String:Number)(o)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),Ge=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=me().default,n=We();function o(s){var i=n(s,"string");return r(i)=="symbol"?i:i+""}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports}}),S=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=Ge();function n(o,s,i){return(s=r(s))in o?Object.defineProperty(o,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[s]=i,o}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),x=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=S();function n(s,i){var u=Object.keys(s);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(s);i&&(a=a.filter(function(c){return Object.getOwnPropertyDescriptor(s,c).enumerable})),u.push.apply(u,a)}return u}function o(s){for(var i=1;i<arguments.length;i++){var u=arguments[i]!=null?arguments[i]:{};i%2?n(Object(u),!0).forEach(function(a){r(s,a,u[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(u)):n(Object(u)).forEach(function(a){Object.defineProperty(s,a,Object.getOwnPropertyDescriptor(u,a))})}return s}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports}});function A(e){const t={subscribe(r){let n=null,o=!1,s=!1,i=!1;function u(){if(n===null){i=!0;return}s||(s=!0,typeof n=="function"?n():n&&n.unsubscribe())}return n=e({next(a){var c;o||(c=r.next)===null||c===void 0||c.call(r,a)},error(a){var c;o||(o=!0,(c=r.error)===null||c===void 0||c.call(r,a),u())},complete(){var a;o||(o=!0,(a=r.complete)===null||a===void 0||a.call(r),u())}}),i&&u(),{unsubscribe:u}},pipe(...r){return r.reduce(Fe,t)}};return t}function Fe(e,t){return t(e)}function He(e){const t=new AbortController;return new Promise((n,o)=>{let s=!1;function i(){s||(s=!0,u.unsubscribe())}t.signal.addEventListener("abort",()=>{o(t.signal.reason)});const u=e.subscribe({next(a){s=!0,n(a),i()},error(a){o(a)},complete(){t.abort(),i()}})})}function Ye(e){return t=>{let r=0,n=null;const o=[];function s(){n||(n=t.subscribe({next(u){for(const c of o){var a;(a=c.next)===null||a===void 0||a.call(c,u)}},error(u){for(const c of o){var a;(a=c.error)===null||a===void 0||a.call(c,u)}},complete(){for(const a of o){var u;(u=a.complete)===null||u===void 0||u.call(a)}}}))}function i(){if(r===0&&n){const u=n;n=null,u.unsubscribe()}}return A(u=>(r++,o.push(u),s(),{unsubscribe(){r--,i();const a=o.findIndex(c=>c===u);a>-1&&o.splice(a,1)}}))}}function Je(e){return t=>A(r=>t.subscribe({next(n){var o;(o=e.next)===null||o===void 0||o.call(e,n),r.next(n)},error(n){var o;(o=e.error)===null||o===void 0||o.call(e,n),r.error(n)},complete(){var n;(n=e.complete)===null||n===void 0||n.call(e),r.complete()}}))}function Qe(e){let t=e;const r=[],n=i=>{t!==void 0&&i.next(t),r.push(i)},o=i=>{r.splice(r.indexOf(i),1)},s=A(i=>(n(i),()=>{o(i)}));return s.next=i=>{if(t!==i){t=i;for(const u of r)u.next(i)}},s.get=()=>t,s}function Xe(e){return A(t=>{function r(o=0,s=e.op){const i=e.links[o];if(!i)throw new Error("No more links to execute - did you forget to add an ending link?");return i({op:s,next(a){return r(o+1,a)}})}return r().subscribe(t)})}function N(e){return!!e&&!Array.isArray(e)&&typeof e=="object"}var Ze=Object.create,he=Object.defineProperty,et=Object.getOwnPropertyDescriptor,ve=Object.getOwnPropertyNames,tt=Object.getPrototypeOf,rt=Object.prototype.hasOwnProperty,D=(e,t)=>function(){return t||(0,e[ve(e)[0]])((t={exports:{}}).exports,t),t.exports},nt=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(var o=ve(t),s=0,i=o.length,u;s<i;s++)u=o[s],!rt.call(e,u)&&u!==r&&he(e,u,{get:(a=>t[a]).bind(null,u),enumerable:!(n=et(t,u))||n.enumerable});return e},W=(e,t,r)=>(r=e!=null?Ze(tt(e)):{},nt(he(r,"default",{value:e,enumerable:!0}),e));const be=()=>{},ne=e=>{Object.freeze&&Object.freeze(e)};function ge(e,t,r){var n;const o=t.join(".");return(n=r[o])!==null&&n!==void 0||(r[o]=new Proxy(be,{get(s,i){if(!(typeof i!="string"||i==="then"))return ge(e,[...t,i],r)},apply(s,i,u){const a=t[t.length-1];let c={args:u,path:t};return a==="call"?c={args:u.length>=2?[u[1]]:[],path:t.slice(0,-1)}:a==="apply"&&(c={args:u.length>=2?u[1]:[],path:t.slice(0,-1)}),ne(c.args),ne(c.path),e(c)}})),r[o]}const ot=e=>ge(e,[],Object.create(null)),st=e=>new Proxy(be,{get(t,r){if(r!=="then")return e(r)}});var we=D({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(n){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},t.exports.__esModule=!0,t.exports.default=t.exports,r(n)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),it=D({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=we().default;function n(o,s){if(r(o)!="object"||!o)return o;var i=o[Symbol.toPrimitive];if(i!==void 0){var u=i.call(o,s||"default");if(r(u)!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return(s==="string"?String:Number)(o)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),ut=D({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=we().default,n=it();function o(s){var i=n(s,"string");return r(i)=="symbol"?i:i+""}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports}}),xe=D({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=ut();function n(o,s,i){return(s=r(s))in o?Object.defineProperty(o,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[s]=i,o}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),re=D({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=xe();function n(s,i){var u=Object.keys(s);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(s);i&&(a=a.filter(function(c){return Object.getOwnPropertyDescriptor(s,c).enumerable})),u.push.apply(u,a)}return u}function o(s){for(var i=1;i<arguments.length;i++){var u=arguments[i]!=null?arguments[i]:{};i%2?n(Object(u),!0).forEach(function(a){r(s,a,u[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(u)):n(Object(u)).forEach(function(a){Object.defineProperty(s,a,Object.getOwnPropertyDescriptor(u,a))})}return s}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports}});W(re());W(xe());var B=W(re());function at(e,t){if("error"in e){const n=t.deserialize(e.error);return{ok:!1,error:(0,B.default)((0,B.default)({},e),{},{error:n})}}return{ok:!0,result:(0,B.default)((0,B.default)({},e.result),(!e.result.type||e.result.type==="data")&&{type:"data",data:t.deserialize(e.result.data)})}}var F=class extends Error{constructor(){super("Unable to transform response from server")}};function ct(e,t){let r;try{r=at(e,t)}catch{throw new F}if(!r.ok&&(!N(r.error.error)||typeof r.error.error.code!="number"))throw new F;if(r.ok&&!N(r.result))throw new F;return r}W(re());var U=v(S()),I=v(x());function lt(e){return e instanceof K}function ft(e){return N(e)&&N(e.error)&&typeof e.error.code=="number"&&typeof e.error.message=="string"}function pt(e,t){return typeof e=="string"?e:N(e)&&typeof e.message=="string"?e.message:t}var K=class V extends Error{constructor(t,r){var n,o;const s=r?.cause;super(t,{cause:s}),(0,U.default)(this,"cause",void 0),(0,U.default)(this,"shape",void 0),(0,U.default)(this,"data",void 0),(0,U.default)(this,"meta",void 0),this.meta=r?.meta,this.cause=s,this.shape=r==null||(n=r.result)===null||n===void 0?void 0:n.error,this.data=r==null||(o=r.result)===null||o===void 0?void 0:o.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,V.prototype)}static from(t,r={}){const n=t;return lt(n)?(r.meta&&(n.meta=(0,I.default)((0,I.default)({},n.meta),r.meta)),n):ft(n)?new V(n.error.message,(0,I.default)((0,I.default)({},r),{},{result:n})):new V(pt(n,"Unknown error"),(0,I.default)((0,I.default)({},r),{},{cause:n}))}};function dt(e){const t=e;return t?"input"in t?t:{input:t,output:t}:{input:{serialize:r=>r,deserialize:r=>r},output:{serialize:r=>r,deserialize:r=>r}}}const oe=e=>typeof e=="function";function yt(e){if(e)return e;if(typeof window<"u"&&oe(window.fetch))return window.fetch;if(typeof globalThis<"u"&&oe(globalThis.fetch))return globalThis.fetch;throw new Error("No fetch implementation found")}var R=v(x());function mt(e){return{url:e.url.toString(),fetch:e.fetch,transformer:dt(e.transformer),methodOverride:e.methodOverride}}function ht(e){const t={};for(let r=0;r<e.length;r++){const n=e[r];t[r]=n}return t}const vt={query:"GET",mutation:"POST",subscription:"PATCH"};function Pe(e){return"input"in e?e.transformer.input.serialize(e.input):ht(e.inputs.map(t=>e.transformer.input.serialize(t)))}const _e=e=>{const t=e.url.split("?");let n=t[0].replace(/\/$/,"")+"/"+e.path;const o=[];if(t[1]&&o.push(t[1]),"inputs"in e&&o.push("batch=1"),e.type==="query"||e.type==="subscription"){const s=Pe(e);s!==void 0&&e.methodOverride!=="POST"&&o.push(`input=${encodeURIComponent(JSON.stringify(s))}`)}return o.length&&(n+="?"+o.join("&")),n},bt=e=>{if(e.type==="query"&&e.methodOverride!=="POST")return;const t=Pe(e);return t!==void 0?JSON.stringify(t):void 0},gt=e=>_t((0,R.default)((0,R.default)({},e),{},{contentTypeHeader:"application/json",getUrl:_e,getBody:bt}));var wt=class extends Error{constructor(){const e="AbortError";super(e),this.name=e,this.message=e}};const xt=e=>{var t;if(e?.aborted)throw(t=e.throwIfAborted)===null||t===void 0||t.call(e),typeof DOMException<"u"?new DOMException("AbortError","AbortError"):new wt};async function Pt(e){var t;xt(e.signal);const r=e.getUrl(e),n=e.getBody(e),{type:o}=e,s=await(async()=>{const u=await e.headers();return Symbol.iterator in u?Object.fromEntries(u):u})(),i=(0,R.default)((0,R.default)((0,R.default)({},e.contentTypeHeader?{"content-type":e.contentTypeHeader}:{}),e.trpcAcceptHeader?{"trpc-accept":e.trpcAcceptHeader}:void 0),s);return yt(e.fetch)(r,{method:(t=e.methodOverride)!==null&&t!==void 0?t:vt[o],signal:e.signal,body:n,headers:i})}async function _t(e){const t={},r=await Pt(e);t.response=r;const n=await r.json();return t.responseJSON=n,{json:n,meta:t}}v(x());const se=()=>{throw new Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function ie(e){let t=null,r=null;const n=()=>{clearTimeout(r),r=null,t=null};function o(u){const a=[[]];let c=0;for(;;){const p=u[c];if(!p)break;const f=a[a.length-1];if(p.aborted){var d;(d=p.reject)===null||d===void 0||d.call(p,new Error("Aborted")),c++;continue}if(e.validate(f.concat(p).map(y=>y.key))){f.push(p),c++;continue}if(f.length===0){var l;(l=p.reject)===null||l===void 0||l.call(p,new Error("Input is too big for a single dispatch")),c++;continue}a.push([])}return a}function s(){const u=o(t);n();for(const a of u){if(!a.length)continue;const c={items:a};for(const l of a)l.batch=c;e.fetch(c.items.map(l=>l.key)).then(async l=>{await Promise.all(l.map(async(f,h)=>{const y=c.items[h];try{var b;const g=await Promise.resolve(f);(b=y.resolve)===null||b===void 0||b.call(y,g)}catch(g){var E;(E=y.reject)===null||E===void 0||E.call(y,g)}y.batch=null,y.reject=null,y.resolve=null}));for(const f of c.items){var p;(p=f.reject)===null||p===void 0||p.call(f,new Error("Missing result")),f.batch=null}}).catch(l=>{for(const f of c.items){var p;(p=f.reject)===null||p===void 0||p.call(f,l),f.batch=null}})}}function i(u){var a;const c={aborted:!1,key:u,batch:null,resolve:se,reject:se},d=new Promise((l,p)=>{var f;c.reject=p,c.resolve=l,(f=t)!==null&&f!==void 0||(t=[]),t.push(c)});return(a=r)!==null&&a!==void 0||(r=setTimeout(s)),d}return{load:i}}function Ot(...e){const t=new AbortController,r=e.length;let n=0;const o=()=>{++n===r&&t.abort()};for(const s of e)s?.aborted?o():s?.addEventListener("abort",o,{once:!0});return t.signal}var z=v(x());function jt(e){var t,r;const n=mt(e),o=(t=e.maxURLLength)!==null&&t!==void 0?t:1/0,s=(r=e.maxItems)!==null&&r!==void 0?r:1/0;return()=>{const i=d=>({validate(l){if(o===1/0&&s===1/0)return!0;if(l.length>s)return!1;const p=l.map(y=>y.path).join(","),f=l.map(y=>y.input);return _e((0,z.default)((0,z.default)({},n),{},{type:d,path:p,inputs:f,signal:null})).length<=o},async fetch(l){const p=l.map(g=>g.path).join(","),f=l.map(g=>g.input),h=Ot(...l.map(g=>g.signal)),y=await gt((0,z.default)((0,z.default)({},n),{},{path:p,inputs:f,type:d,headers(){return e.headers?typeof e.headers=="function"?e.headers({opList:l}):e.headers:{}},signal:h}));return(Array.isArray(y.json)?y.json:l.map(()=>y.json)).map(g=>({meta:y.meta,json:g}))}}),u=ie(i("query")),a=ie(i("mutation")),c={query:u,mutation:a};return({op:d})=>A(l=>{/* istanbul ignore if -- @preserve */if(d.type==="subscription")throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");const f=c[d.type].load(d);let h;return f.then(y=>{h=y;const b=ct(y.json,n.transformer.output);if(!b.ok){l.error(K.from(b.error,{meta:y.meta}));return}l.next({context:y.meta,result:b.result}),l.complete()}).catch(y=>{l.error(K.from(y,{meta:h?.meta}))}),()=>{}})}}var w=v(x());function St(e){return typeof FormData>"u"?!1:e instanceof FormData}const H={css:{query:["72e3ff","3fb0d8"],mutation:["c5a3fc","904dfc"],subscription:["ff49e1","d83fbe"]},ansi:{regular:{query:["\x1B[30;46m","\x1B[97;46m"],mutation:["\x1B[30;45m","\x1B[97;45m"],subscription:["\x1B[30;42m","\x1B[97;42m"]},bold:{query:["\x1B[1;30;46m","\x1B[1;97;46m"],mutation:["\x1B[1;30;45m","\x1B[1;97;45m"],subscription:["\x1B[1;30;42m","\x1B[1;97;42m"]}}};function Et(e){const{direction:t,type:r,withContext:n,path:o,id:s,input:i}=e,u=[],a=[];if(e.colorMode==="none")u.push(t==="up"?">>":"<<",r,`#${s}`,o);else if(e.colorMode==="ansi"){const[c,d]=H.ansi.regular[r],[l,p]=H.ansi.bold[r];u.push(t==="up"?c:d,t==="up"?">>":"<<",r,t==="up"?l:p,`#${s}`,o,"\x1B[0m")}else{const[c,d]=H.css[r],l=`
    background-color: #${t==="up"?c:d};
    color: ${t==="up"?"black":"white"};
    padding: 2px;
  `;u.push("%c",t==="up"?">>":"<<",r,`#${s}`,`%c${o}%c`,"%O"),a.push(l,`${l}; font-weight: bold;`,`${l}; font-weight: normal;`)}return t==="up"?a.push(n?{input:i,context:e.context}:{input:i}):a.push((0,w.default)({input:i,result:e.result,elapsedMs:e.elapsedMs},n&&{context:e.context})),{parts:u,args:a}}const It=({c:e=console,colorMode:t="css",withContext:r})=>n=>{const o=n.input,s=St(o)?Object.fromEntries(o):o,{parts:i,args:u}=Et((0,w.default)((0,w.default)({},n),{},{colorMode:t,input:s,withContext:r})),a=n.direction==="down"&&n.result&&(n.result instanceof Error||"error"in n.result.result&&n.result.result.error)?"error":"log";e[a].apply(null,[i.join(" ")].concat(u))};function Tt(e={}){var t,r;const{enabled:n=()=>!0}=e,o=(t=e.colorMode)!==null&&t!==void 0?t:typeof window>"u"?"ansi":"css",s=(r=e.withContext)!==null&&r!==void 0?r:o==="css",{logger:i=It({c:e.console,colorMode:o,withContext:s})}=e;return()=>({op:u,next:a})=>A(c=>{n((0,w.default)((0,w.default)({},u),{},{direction:"up"}))&&i((0,w.default)((0,w.default)({},u),{},{direction:"up"}));const d=Date.now();function l(p){const f=Date.now()-d;n((0,w.default)((0,w.default)({},u),{},{direction:"down",result:p}))&&i((0,w.default)((0,w.default)({},u),{},{direction:"down",elapsedMs:f,result:p}))}return a(u).pipe(Je({next(p){l(p)},error(p){l(p)}})).subscribe(c)})}const Oe=(e,...t)=>typeof e=="function"?e(...t):e;v(S());function kt(){let e,t;return{promise:new Promise((n,o)=>{e=n,t=o}),resolve:e,reject:t}}async function At(e){const t=await Oe(e.url);if(!e.connectionParams)return t;const n=`${t.includes("?")?"&":"?"}connectionParams=1`;return t+n}async function Rt(e){const t={method:"connectionParams",data:await Oe(e)};return JSON.stringify(t)}v(S());var j=v(S());function Ct(e){const{promise:t,resolve:r,reject:n}=kt();return e.addEventListener("open",()=>{e.removeEventListener("error",n),r()}),e.addEventListener("error",n),t}function Nt(e,{intervalMs:t,pongTimeoutMs:r}){let n,o;function s(){n=setTimeout(()=>{e.send("PING"),o=setTimeout(()=>{e.close()},r)},t)}function i(){clearTimeout(n),s()}function u(){clearTimeout(o),i()}e.addEventListener("open",s),e.addEventListener("message",({data:a})=>{clearTimeout(n),s(),a==="PONG"&&u()}),e.addEventListener("close",()=>{clearTimeout(n),clearTimeout(o)})}var qt=class Q{constructor(t){var r;if((0,j.default)(this,"id",++Q.connectCount),(0,j.default)(this,"WebSocketPonyfill",void 0),(0,j.default)(this,"urlOptions",void 0),(0,j.default)(this,"keepAliveOpts",void 0),(0,j.default)(this,"wsObservable",Qe(null)),(0,j.default)(this,"openPromise",null),this.WebSocketPonyfill=(r=t.WebSocketPonyfill)!==null&&r!==void 0?r:WebSocket,!this.WebSocketPonyfill)throw new Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");this.urlOptions=t.urlOptions,this.keepAliveOpts=t.keepAlive}get ws(){return this.wsObservable.get()}set ws(t){this.wsObservable.next(t)}isOpen(){return!!this.ws&&this.ws.readyState===this.WebSocketPonyfill.OPEN&&!this.openPromise}isClosed(){return!!this.ws&&(this.ws.readyState===this.WebSocketPonyfill.CLOSING||this.ws.readyState===this.WebSocketPonyfill.CLOSED)}async open(){var t=this;if(t.openPromise)return t.openPromise;t.id=++Q.connectCount;const r=At(t.urlOptions).then(n=>new t.WebSocketPonyfill(n));t.openPromise=r.then(async n=>{t.ws=n,n.addEventListener("message",function({data:o}){o==="PING"&&this.send("PONG")}),t.keepAliveOpts.enabled&&Nt(n,t.keepAliveOpts),n.addEventListener("close",()=>{t.ws===n&&(t.ws=null)}),await Ct(n),t.urlOptions.connectionParams&&n.send(await Rt(t.urlOptions.connectionParams))});try{await t.openPromise}finally{t.openPromise=null}}async close(){var t=this;try{await t.openPromise}finally{var r;(r=t.ws)===null||r===void 0||r.close()}}};(0,j.default)(qt,"connectCount",0);v(S());v(x());var Y=v(S()),ue=v(x()),Mt=class{constructor(e){(0,Y.default)(this,"links",void 0),(0,Y.default)(this,"runtime",void 0),(0,Y.default)(this,"requestId",void 0),this.requestId=0,this.runtime={},this.links=e.links.map(t=>t(this.runtime))}$request(e){var t;return Xe({links:this.links,op:(0,ue.default)((0,ue.default)({},e),{},{context:(t=e.context)!==null&&t!==void 0?t:{},id:++this.requestId})}).pipe(Ye())}async requestAsPromise(e){var t=this;try{const r=t.$request(e);return(await He(r)).result.data}catch(r){throw K.from(r)}}query(e,t,r){return this.requestAsPromise({type:"query",path:e,input:t,context:r?.context,signal:r?.signal})}mutation(e,t,r){return this.requestAsPromise({type:"mutation",path:e,input:t,context:r?.context,signal:r?.signal})}subscription(e,t,r){return this.$request({type:"subscription",path:e,input:t,context:r.context,signal:r.signal}).subscribe({next(o){switch(o.result.type){case"state":{var s;(s=r.onConnectionStateChange)===null||s===void 0||s.call(r,o.result);break}case"started":{var i;(i=r.onStarted)===null||i===void 0||i.call(r,{context:o.context});break}case"stopped":{var u;(u=r.onStopped)===null||u===void 0||u.call(r);break}case"data":case void 0:{var a;(a=r.onData)===null||a===void 0||a.call(r,o.result.data);break}}},error(o){var s;(s=r.onError)===null||s===void 0||s.call(r,o)},complete(){var o;(o=r.onComplete)===null||o===void 0||o.call(r)}})}};const Lt=Symbol.for("trpc_untypedClient"),Dt={query:"query",mutate:"mutation",subscribe:"subscription"},Bt=e=>Dt[e];function Ut(e){const t=ot(({path:r,args:n})=>{const o=[...r],s=Bt(o.pop()),i=o.join(".");return e[s](i,...n)});return st(r=>r===Lt?e:t[r])}function zt(e){const t=new Mt(e);return Ut(t)}v(x());v(x());var Vt=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(e,t){function r(o){var s,i,u,a=2;for(typeof Symbol<"u"&&(i=Symbol.asyncIterator,u=Symbol.iterator);a--;){if(i&&(s=o[i])!=null)return s.call(o);if(u&&(s=o[u])!=null)return new n(s.call(o));i="@@asyncIterator",u="@@iterator"}throw new TypeError("Object is not async iterable")}function n(o){function s(i){if(Object(i)!==i)return Promise.reject(new TypeError(i+" is not an object."));var u=i.done;return Promise.resolve(i.value).then(function(a){return{value:a,done:u}})}return n=function(u){this.s=u,this.n=u.next},n.prototype={s:null,n:null,next:function(){return s(this.n.apply(this.s,arguments))},return:function(u){var a=this.s.return;return a===void 0?Promise.resolve({value:u,done:!0}):s(a.apply(this.s,arguments))},throw:function(u){var a=this.s.return;return a===void 0?Promise.reject(u):s(a.apply(this.s,arguments))}},new n(o)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}});v(Vt());v(x());var $t=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(e,t){function r(){var n=typeof SuppressedError=="function"?SuppressedError:function(u,a){var c=Error();return c.name="SuppressedError",c.error=u,c.suppressed=a,c},o={},s=[];function i(u,a){if(a!=null){if(Object(a)!==a)throw new TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(u)var c=a[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(c===void 0&&(c=a[Symbol.dispose||Symbol.for("Symbol.dispose")],u))var d=c;if(typeof c!="function")throw new TypeError("Object is not disposable.");d&&(c=function(){try{d.call(a)}catch(p){return Promise.reject(p)}}),s.push({v:a,d:c,a:u})}else u&&s.push({d:a,a:u});return a}return{e:o,u:i.bind(null,!1),a:i.bind(null,!0),d:function(){var a,c=this.e,d=0;function l(){for(;a=s.pop();)try{if(!a.a&&d===1)return d=0,s.push(a),Promise.resolve().then(l);if(a.d){var f=a.d.call(a.v);if(a.a)return d|=2,Promise.resolve(f).then(l,p)}else d|=1}catch(h){return p(h)}if(d===1)return c!==o?Promise.reject(c):Promise.resolve();if(c!==o)throw c}function p(f){return c=c!==o?new n(f,c):f,l()}return l()}}}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),je=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(e,t){function r(n,o){this.v=n,this.k=o}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),Kt=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(e,t){var r=je();function n(o){return new r(o,0)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),Wt=_({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(e,t){var r=je();function n(s){return function(){return new o(s.apply(this,arguments))}}function o(s){var i,u;function a(d,l){try{var p=s[d](l),f=p.value,h=f instanceof r;Promise.resolve(h?f.v:f).then(function(y){if(h){var b=d==="return"?"return":"next";if(!f.k||y.done)return a(b,y);y=s[b](y).value}c(p.done?"return":"normal",y)},function(y){a("throw",y)})}catch(y){c("throw",y)}}function c(d,l){switch(d){case"return":i.resolve({value:l,done:!0});break;case"throw":i.reject(l);break;default:i.resolve({value:l,done:!1})}(i=i.next)?a(i.key,i.arg):u=null}this._invoke=function(d,l){return new Promise(function(p,f){var h={key:d,arg:l,resolve:p,reject:f,next:null};u?u=u.next=h:(i=u=h,a(d,l))})},typeof s.return!="function"&&(this.return=void 0)}o.prototype[typeof Symbol=="function"&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},o.prototype.next=function(s){return this._invoke("next",s)},o.prototype.throw=function(s){return this._invoke("throw",s)},o.prototype.return=function(s){return this._invoke("return",s)},t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}});v($t());v(Kt());v(Wt());v(x());class Gt{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(t,r){this.keyToValue.set(t,r),this.valueToKey.set(r,t)}getByKey(t){return this.keyToValue.get(t)}getByValue(t){return this.valueToKey.get(t)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class Se{constructor(t){this.generateIdentifier=t,this.kv=new Gt}register(t,r){this.kv.getByValue(t)||(r||(r=this.generateIdentifier(t)),this.kv.set(r,t))}clear(){this.kv.clear()}getIdentifier(t){return this.kv.getByValue(t)}getValue(t){return this.kv.getByKey(t)}}class Ft extends Se{constructor(){super(t=>t.name),this.classToAllowedProps=new Map}register(t,r){typeof r=="object"?(r.allowProps&&this.classToAllowedProps.set(t,r.allowProps),super.register(t,r.identifier)):super.register(t,r)}getAllowedProps(t){return this.classToAllowedProps.get(t)}}function Ht(e){if("values"in Object)return Object.values(e);const t=[];for(const r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}function Yt(e,t){const r=Ht(e);if("find"in r)return r.find(t);const n=r;for(let o=0;o<n.length;o++){const s=n[o];if(t(s))return s}}function k(e,t){Object.entries(e).forEach(([r,n])=>t(n,r))}function $(e,t){return e.indexOf(t)!==-1}function ae(e,t){for(let r=0;r<e.length;r++){const n=e[r];if(t(n))return n}}class Jt{constructor(){this.transfomers={}}register(t){this.transfomers[t.name]=t}findApplicable(t){return Yt(this.transfomers,r=>r.isApplicable(t))}findByName(t){return this.transfomers[t]}}const Qt=e=>Object.prototype.toString.call(e).slice(8,-1),Ee=e=>typeof e>"u",Xt=e=>e===null,q=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,X=e=>q(e)&&Object.keys(e).length===0,O=e=>Array.isArray(e),Zt=e=>typeof e=="string",er=e=>typeof e=="number"&&!isNaN(e),tr=e=>typeof e=="boolean",rr=e=>e instanceof RegExp,M=e=>e instanceof Map,L=e=>e instanceof Set,Ie=e=>Qt(e)==="Symbol",nr=e=>e instanceof Date&&!isNaN(e.valueOf()),or=e=>e instanceof Error,ce=e=>typeof e=="number"&&isNaN(e),sr=e=>tr(e)||Xt(e)||Ee(e)||er(e)||Zt(e)||Ie(e),ir=e=>typeof e=="bigint",ur=e=>e===1/0||e===-1/0,ar=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),cr=e=>e instanceof URL,Te=e=>e.replace(/\./g,"\\."),J=e=>e.map(String).map(Te).join("."),C=e=>{const t=[];let r="";for(let o=0;o<e.length;o++){let s=e.charAt(o);if(s==="\\"&&e.charAt(o+1)==="."){r+=".",o++;continue}if(s==="."){t.push(r),r="";continue}r+=s}const n=r;return t.push(n),t};function P(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}const ke=[P(Ee,"undefined",()=>null,()=>{}),P(ir,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),P(nr,"Date",e=>e.toISOString(),e=>new Date(e)),P(or,"Error",(e,t)=>{const r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(n=>{r[n]=e[n]}),r},(e,t)=>{const r=new Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(n=>{r[n]=e[n]}),r}),P(rr,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),r=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,r)}),P(L,"set",e=>[...e.values()],e=>new Set(e)),P(M,"map",e=>[...e.entries()],e=>new Map(e)),P(e=>ce(e)||ur(e),"number",e=>ce(e)?"NaN":e>0?"Infinity":"-Infinity",Number),P(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),P(cr,"URL",e=>e.toString(),e=>new URL(e))];function G(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}const Ae=G((e,t)=>Ie(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{const n=r.symbolRegistry.getValue(t[1]);if(!n)throw new Error("Trying to deserialize unknown symbol");return n}),lr=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),Re=G(ar,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const r=lr[t[1]];if(!r)throw new Error("Trying to deserialize unknown typed array");return new r(e)});function Ce(e,t){return e?.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}const Ne=G(Ce,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};const n={};return r.forEach(o=>{n[o]=e[o]}),n},(e,t,r)=>{const n=r.classRegistry.getValue(t[1]);if(!n)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),e)}),qe=G((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{const n=r.customTransformerRegistry.findByName(t[1]);if(!n)throw new Error("Trying to deserialize unknown custom value");return n.deserialize(e)}),fr=[Ne,Ae,qe,Re],le=(e,t)=>{const r=ae(fr,o=>o.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};const n=ae(ke,o=>o.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation}},Me={};ke.forEach(e=>{Me[e.annotation]=e});const pr=(e,t,r)=>{if(O(t))switch(t[0]){case"symbol":return Ae.untransform(e,t,r);case"class":return Ne.untransform(e,t,r);case"custom":return qe.untransform(e,t,r);case"typed-array":return Re.untransform(e,t,r);default:throw new Error("Unknown transformation: "+t)}else{const n=Me[t];if(!n)throw new Error("Unknown transformation: "+t);return n.untransform(e,r)}},T=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function Le(e){if($(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if($(e,"prototype"))throw new Error("prototype is not allowed as a property");if($(e,"constructor"))throw new Error("constructor is not allowed as a property")}const dr=(e,t)=>{Le(t);for(let r=0;r<t.length;r++){const n=t[r];if(L(e))e=T(e,+n);else if(M(e)){const o=+n,s=+t[++r]==0?"key":"value",i=T(e,o);switch(s){case"key":e=i;break;case"value":e=e.get(i);break}}else e=e[n]}return e},Z=(e,t,r)=>{if(Le(t),t.length===0)return r(e);let n=e;for(let s=0;s<t.length-1;s++){const i=t[s];if(O(n)){const u=+i;n=n[u]}else if(q(n))n=n[i];else if(L(n)){const u=+i;n=T(n,u)}else if(M(n)){if(s===t.length-2)break;const a=+i,c=+t[++s]==0?"key":"value",d=T(n,a);switch(c){case"key":n=d;break;case"value":n=n.get(d);break}}}const o=t[t.length-1];if(O(n)?n[+o]=r(n[+o]):q(n)&&(n[o]=r(n[o])),L(n)){const s=T(n,+o),i=r(s);s!==i&&(n.delete(s),n.add(i))}if(M(n)){const s=+t[t.length-2],i=T(n,s);switch(+o==0?"key":"value"){case"key":{const a=r(i);n.set(a,n.get(i)),a!==i&&n.delete(i);break}case"value":{n.set(i,r(n.get(i)));break}}}return e};function ee(e,t,r=[]){if(!e)return;if(!O(e)){k(e,(s,i)=>ee(s,t,[...r,...C(i)]));return}const[n,o]=e;o&&k(o,(s,i)=>{ee(s,t,[...r,...C(i)])}),t(n,r)}function yr(e,t,r){return ee(t,(n,o)=>{e=Z(e,o,s=>pr(s,n,r))}),e}function mr(e,t){function r(n,o){const s=dr(e,C(o));n.map(C).forEach(i=>{e=Z(e,i,()=>s)})}if(O(t)){const[n,o]=t;n.forEach(s=>{e=Z(e,C(s),()=>e)}),o&&k(o,r)}else k(t,r);return e}const hr=(e,t)=>q(e)||O(e)||M(e)||L(e)||Ce(e,t);function vr(e,t,r){const n=r.get(e);n?n.push(t):r.set(e,[t])}function br(e,t){const r={};let n;return e.forEach(o=>{if(o.length<=1)return;t||(o=o.map(u=>u.map(String)).sort((u,a)=>u.length-a.length));const[s,...i]=o;s.length===0?n=i.map(J):r[J(s)]=i.map(J)}),n?X(r)?[n]:[n,r]:X(r)?void 0:r}const De=(e,t,r,n,o=[],s=[],i=new Map)=>{const u=sr(e);if(!u){vr(e,o,t);const f=i.get(e);if(f)return n?{transformedValue:null}:f}if(!hr(e,r)){const f=le(e,r),h=f?{transformedValue:f.value,annotations:[f.type]}:{transformedValue:e};return u||i.set(e,h),h}if($(s,e))return{transformedValue:null};const a=le(e,r),c=a?.value??e,d=O(c)?[]:{},l={};k(c,(f,h)=>{if(h==="__proto__"||h==="constructor"||h==="prototype")throw new Error(`Detected property ${h}. This is a prototype pollution risk, please remove it from your object.`);const y=De(f,t,r,n,[...o,h],[...s,e],i);d[h]=y.transformedValue,O(y.annotations)?l[h]=y.annotations:q(y.annotations)&&k(y.annotations,(b,E)=>{l[Te(h)+"."+E]=b})});const p=X(l)?{transformedValue:d,annotations:a?[a.type]:void 0}:{transformedValue:d,annotations:a?[a.type,l]:l};return u||i.set(e,p),p};function Be(e){return Object.prototype.toString.call(e).slice(8,-1)}function fe(e){return Be(e)==="Array"}function gr(e){if(Be(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function wr(e,t,r,n,o){const s={}.propertyIsEnumerable.call(n,t)?"enumerable":"nonenumerable";s==="enumerable"&&(e[t]=r),o&&s==="nonenumerable"&&Object.defineProperty(e,t,{value:r,enumerable:!1,writable:!0,configurable:!0})}function te(e,t={}){if(fe(e))return e.map(o=>te(o,t));if(!gr(e))return e;const r=Object.getOwnPropertyNames(e),n=Object.getOwnPropertySymbols(e);return[...r,...n].reduce((o,s)=>{if(fe(t.props)&&!t.props.includes(s))return o;const i=e[s],u=te(i,t);return wr(o,s,u,e,t.nonenumerable),o},{})}class m{constructor({dedupe:t=!1}={}){this.classRegistry=new Ft,this.symbolRegistry=new Se(r=>r.description??""),this.customTransformerRegistry=new Jt,this.allowedErrorProps=[],this.dedupe=t}serialize(t){const r=new Map,n=De(t,r,this,this.dedupe),o={json:n.transformedValue};n.annotations&&(o.meta={...o.meta,values:n.annotations});const s=br(r,this.dedupe);return s&&(o.meta={...o.meta,referentialEqualities:s}),o}deserialize(t){const{json:r,meta:n}=t;let o=te(r);return n?.values&&(o=yr(o,n.values,this)),n?.referentialEqualities&&(o=mr(o,n.referentialEqualities)),o}stringify(t){return JSON.stringify(this.serialize(t))}parse(t){return this.deserialize(JSON.parse(t))}registerClass(t,r){this.classRegistry.register(t,r)}registerSymbol(t,r){this.symbolRegistry.register(t,r)}registerCustom(t,r){this.customTransformerRegistry.register({name:r,...t})}allowErrorProps(...t){this.allowedErrorProps.push(...t)}}m.defaultInstance=new m;m.serialize=m.defaultInstance.serialize.bind(m.defaultInstance);m.deserialize=m.defaultInstance.deserialize.bind(m.defaultInstance);m.stringify=m.defaultInstance.stringify.bind(m.defaultInstance);m.parse=m.defaultInstance.parse.bind(m.defaultInstance);m.registerClass=m.defaultInstance.registerClass.bind(m.defaultInstance);m.registerSymbol=m.defaultInstance.registerSymbol.bind(m.defaultInstance);m.registerCustom=m.defaultInstance.registerCustom.bind(m.defaultInstance);m.allowErrorProps=m.defaultInstance.allowErrorProps.bind(m.defaultInstance);m.serialize;m.deserialize;m.stringify;m.parse;m.registerClass;m.registerCustom;m.registerSymbol;m.allowErrorProps;const xr={};var pe={};const Pr=xr?.PUBLIC_API_URL||(typeof window<"u"?window.__PUBLIC_API_URL__:void 0)||(typeof process<"u"?pe?.PUBLIC_API_URL:void 0)||(typeof process<"u"?pe?.API_URL:void 0)||"http://localhost:3000",_r=`${String(Pr).replace(/\/$/,"")}/trpc`,Or=zt({links:[Tt({enabled:()=>!1}),jt({url:_r,transformer:m,fetch:(e,t)=>fetch(e,{...t,credentials:"include"})})]});export{Or as t};
