import{I as R}from"./InputText.CPqCR4in.js";import U from"./Button.CplYapw1.js";import{S as w}from"./Select.B5f7pqRM.js";import{t as x}from"./trpc.CMxyjkwB.js";import{u as S}from"./useToast.Cyn6G0qw.js";import{_ as O}from"./utils.BWEB-mtU.js";import{d as I,c as a,a as o,b as C,e as t,F as P,r as N,o as d,f as T}from"./index.CpC-7sc3.js";import{t as c,r as l,a as j,n as L}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./index.DqILEIKx.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DV5zenSI.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CwY1vywt.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.2frgj6Y9.js";const M=I({__name:"AccessControl",setup(F,{expose:e}){e();const n=S(),u=l(""),E=l([]),m=l(null);async function s(){try{const r=await x.crud.user.findMany.query({where:u.value?{OR:[{email:{contains:u.value,mode:"insensitive"}},{name:{contains:u.value,mode:"insensitive"}}]}:void 0,take:20,orderBy:{createdAt:"desc"},select:{id:!0,email:!0,name:!0,role:!0}});E.value=r}catch(r){n.error(r?.message||"Не удалось выполнить поиск пользователей")}}function B(r){m.value=r}const D=[{label:"Не выбрано",value:""},{label:"ADMIN",value:"ADMIN"},{label:"SHOP",value:"SHOP"},{label:"USER",value:"USER"}],p=l(""),i=j({}),v=l(""),f=l("");function h(){const r=v.value.trim();if(!r)return;const y=f.value.split(",").map(V=>V.trim()).filter(Boolean);y.length&&(i[r]=y,v.value="",f.value="")}function g(){for(const r of Object.keys(i))delete i[r]}const b=l(null);async function k(){try{const r=await x.admin.hasPermission.query({permissions:i,userId:m.value?.id,role:p.value||void 0});b.value=r,n.success("Проверка выполнена")}catch(r){n.error(r?.message||"Ошибка проверки прав")}}function A(){if(!p.value){n.warn("Укажите роль для локальной проверки");return}b.value={role:p.value,permissions:i}}const _={toast:n,userQuery:u,userResults:E,selectedUser:m,searchUsers:s,selectUser:B,roleOptions:D,selectedRole:p,permissions:i,permissionResource:v,permissionActions:f,addPermission:h,clearPermissions:g,result:b,check:k,checkRoleLocal:A,InputText:R,Button:U,Select:w};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}}),Q={class:"w-full max-w-5xl"},q={class:"bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"},H={class:"flex gap-2 items-center"},z={key:0,class:"mt-3 max-h-56 overflow-auto"},J=["onClick"],G={class:"text-sm"},K={class:"opacity-70"},W={class:"text-xs opacity-70"},X={class:"bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"},Y={class:"flex gap-2 items-center"},Z={class:"bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4 mb-4"},$={class:"grid grid-cols-2 gap-3 mb-3"},u4={class:"flex gap-2"},e4={key:0,class:"mt-4 text-sm"},o4={class:"font-mono text-xs bg-[--color-muted]/10 p-3 rounded"},s4={class:"flex gap-2"},r4={key:0,class:"mt-4"},t4={class:"bg-[--color-card] border border-[--color-border] rounded-[--radius-md] p-4"},l4={class:"font-mono text-xs whitespace-pre-wrap"};function n4(F,e,n,u,E,m){return d(),a("div",Q,[e[9]||(e[9]=o("h2",{class:"text-xl font-semibold mb-4"},"Контроль доступа",-1)),o("div",q,[e[5]||(e[5]=o("h3",{class:"font-medium mb-2"},"Пользователь",-1)),o("div",H,[t(u.InputText,{modelValue:u.userQuery,"onUpdate:modelValue":e[0]||(e[0]=s=>u.userQuery=s),placeholder:"Поиск по email/имени",class:"w-80"},null,8,["modelValue"]),t(u.Button,{label:"Найти",onClick:u.searchUsers})]),u.userResults.length?(d(),a("div",z,[(d(!0),a(P,null,N(u.userResults,s=>(d(),a("div",{key:s.id,class:L(["px-3 py-2 cursor-pointer hover:bg-[--color-muted]/10 rounded",{"bg-[--color-primary]/10":u.selectedUser?.id===s.id}]),onClick:B=>u.selectUser(s)},[o("div",G,[T(c(s.email)+" ",1),o("span",K,"— "+c(s.name||"без имени"),1)]),o("div",W,"role: "+c(s.role),1)],10,J))),128))])):C("",!0)]),o("div",X,[e[6]||(e[6]=o("h3",{class:"font-medium mb-2"},"Роль (опционально)",-1)),o("div",Y,[t(u.Select,{modelValue:u.selectedRole,"onUpdate:modelValue":e[1]||(e[1]=s=>u.selectedRole=s),options:u.roleOptions,class:"w-60"},null,8,["modelValue"]),t(u.Button,{label:"Сбросить роль",severity:"secondary",onClick:e[2]||(e[2]=s=>u.selectedRole="")})]),e[7]||(e[7]=o("div",{class:"text-xs opacity-70 mt-2"},"Если указан user и роль одновременно — сервер может использовать приоритет/валидацию по необходимости.",-1))]),o("div",Z,[e[8]||(e[8]=o("h3",{class:"font-medium mb-2"},"Проверяемые permissions",-1)),o("div",$,[t(u.InputText,{modelValue:u.permissionResource,"onUpdate:modelValue":e[3]||(e[3]=s=>u.permissionResource=s),placeholder:"Ресурс (например, project)"},null,8,["modelValue"]),t(u.InputText,{modelValue:u.permissionActions,"onUpdate:modelValue":e[4]||(e[4]=s=>u.permissionActions=s),placeholder:"Действия через запятую (например, create,update)"},null,8,["modelValue"])]),o("div",u4,[t(u.Button,{label:"Добавить",onClick:u.addPermission}),t(u.Button,{label:"Очистить",severity:"secondary",onClick:u.clearPermissions})]),Object.keys(u.permissions).length?(d(),a("div",e4,[o("div",o4,c(u.permissions),1)])):C("",!0)]),o("div",s4,[t(u.Button,{label:"Проверить права",onClick:u.check}),t(u.Button,{label:"Проверить роль локально",severity:"secondary",onClick:u.checkRoleLocal})]),u.result?(d(),a("div",r4,[o("div",t4,[o("div",l4,c(JSON.stringify(u.result,null,2)),1)])])):C("",!0)])}const A4=O(M,[["render",n4]]);export{A4 as default};
