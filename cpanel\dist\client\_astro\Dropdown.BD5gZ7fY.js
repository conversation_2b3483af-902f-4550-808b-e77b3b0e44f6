import{S as d}from"./Select.B5f7pqRM.js";import{_ as r}from"./utils.BWEB-mtU.js";import{d as i,g as p,o as c,m,h as f}from"./index.CpC-7sc3.js";const _=i({__name:"Dropdown",props:{modelValue:{},options:{},optionLabel:{default:"label"},optionValue:{default:"value"},placeholder:{default:"Выберите..."},disabled:{type:Boolean,default:!1},showClear:{type:Boolean,default:!1},class:{default:""}},emits:["update:modelValue"],setup(t,{expose:l,emit:e}){l();const o=t,s=e,n=f({get:()=>o.modelValue,set:u=>s("update:modelValue",u)}),a={props:o,emit:s,value:n,Select:d};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}});function b(t,l,e,o,s,n){return c(),p(o.Select,m({modelValue:o.value,"onUpdate:modelValue":l[0]||(l[0]=a=>o.value=a),options:e.options,"option-label":e.optionLabel,"option-value":e.optionValue,placeholder:e.placeholder,disabled:e.disabled,"show-clear":e.showClear,class:e.class},t.$attrs),null,16,["modelValue","options","option-label","option-value","placeholder","disabled","show-clear","class"])}const w=r(_,[["render",b]]);export{w as D};
