import { e as createComponent, f as createAstro, r as renderTemplate, k as renderComponent, n as renderSlot$1, o as renderHead, h as addAttribute } from './astro/server_DbndhTWv.mjs';
import 'kleur/colors';
/* empty css                                  */
import { createElementBlock, openBlock, mergeProps, createElementVNode, renderSlot, defineComponent, useSSRContext, ref, createSlots, renderList, withCtx, createBlock, createCommentVNode, toDisplayString, resolveDynamicComponent, normalizeClass, resolveDirective, withDirectives, resolveComponent, createVNode, Transition, Fragment, createTextVNode, computed, onMounted, watch, onUnmounted, normalizeProps, TransitionGroup, onBeforeUnmount } from 'vue';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/toolbar';
import BaseStyle from '@primevue/core/base/style';
import { p as ptViewMerge, _ as _export_sfc, a as script$6, s as script$7, R as Ripple, O as OverlayEventBus, u as useAuth, n as navigate, $ as $$ClientRouter } from './ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent, ssrRenderSlot, ssrRenderAttrs, ssrInterpolate, ssrRenderClass, ssrRenderAttr, ssrRenderVNode, ssrRenderList } from 'vue/server-renderer';
import { cn } from '@primeuix/utils';
import { style as style$1 } from '@primeuix/styles/avatar';
import { isTouchDevice, absolutePosition, getOuterWidth, addStyle, focus, find, findSingle, setAttribute } from '@primeuix/utils/dom';
import { ZIndex } from '@primeuix/utils/zindex';
import { ConnectedOverlayScrollHandler } from '@primevue/core/utils';
import { style as style$2 } from '@primeuix/styles/menu';
import { resolve, isEmpty } from '@primeuix/utils/object';
/* empty css                              */
import { Monitor, Sun, Moon, Check, Menu as Menu$1 } from 'lucide-vue-next';
import TimesIcon from '@primevue/icons/times';
import { T as ToastEventBus, a as useToast } from './_@astro-renderers_CicWY1rm.mjs';
import { style as style$3 } from '@primeuix/styles/toast';
import CheckIcon from '@primevue/icons/check';
import ExclamationTriangleIcon from '@primevue/icons/exclamationtriangle';
import InfoCircleIcon from '@primevue/icons/infocircle';
import TimesCircleIcon from '@primevue/icons/timescircle';

var classes$3 = {
  root: 'p-toolbar p-component',
  start: 'p-toolbar-start',
  center: 'p-toolbar-center',
  end: 'p-toolbar-end'
};
var ToolbarStyle = BaseStyle.extend({
  name: 'toolbar',
  style: style,
  classes: classes$3
});

var script$1$3 = {
  name: 'BaseToolbar',
  "extends": BaseComponent,
  props: {
    ariaLabelledby: {
      type: String,
      "default": null
    }
  },
  style: ToolbarStyle,
  provide: function provide() {
    return {
      $pcToolbar: this,
      $parentInstance: this
    };
  }
};

var script$5 = {
  name: 'Toolbar',
  "extends": script$1$3,
  inheritAttrs: false
};

var _hoisted_1$4 = ["aria-labelledby"];
function render$4(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    role: "toolbar",
    "aria-labelledby": _ctx.ariaLabelledby
  }, _ctx.ptmi('root')), [createElementVNode("div", mergeProps({
    "class": _ctx.cx('start')
  }, _ctx.ptm('start')), [renderSlot(_ctx.$slots, "start")], 16), createElementVNode("div", mergeProps({
    "class": _ctx.cx('center')
  }, _ctx.ptm('center')), [renderSlot(_ctx.$slots, "center")], 16), createElementVNode("div", mergeProps({
    "class": _ctx.cx('end')
  }, _ctx.ptm('end')), [renderSlot(_ctx.$slots, "end")], 16)], 16, _hoisted_1$4);
}

script$5.render = render$4;

const _sfc_main$9 = /* @__PURE__ */ defineComponent({
  __name: "Toolbar",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `flex items-center justify-between flex-wrap p-3 gap-2
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0
        border border-surface-200 dark:border-surface-700 rounded-md`,
      start: `flex items-center`,
      center: `flex items-center`,
      end: `flex items-center`
    });
    const __returned__ = { theme, get Toolbar() {
      return script$5;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$9(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Toolbar"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$9 = _sfc_main$9.setup;
_sfc_main$9.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Toolbar.vue");
  return _sfc_setup$9 ? _sfc_setup$9(props, ctx) : void 0;
};
const Toolbar = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["ssrRender", _sfc_ssrRender$9]]);

const _sfc_main$8 = /* @__PURE__ */ defineComponent({
  __name: "SecondaryButton",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex cursor-pointer select-none items-center justify-center overflow-hidden relative
        px-3 py-2 gap-2 rounded-md disabled:pointer-events-none disabled:opacity-60 transition-colors duration-200
        bg-surface-100 enabled:hover:bg-surface-200 enabled:active:bg-surface-300
        border border-surface-100 enabled:hover:border-surface-200 enabled:active:border-surface-300
        text-surface-600 enabled:hover:text-surface-700 enabled:active:text-surface-800
        dark:bg-surface-800 dark:enabled:hover:bg-surface-700 dark:enabled:active:bg-surface-600
        dark:border-surface-800 dark:enabled:hover:border-surface-700 dark:enabled:active:border-surface-600
        dark:text-surface-300 dark:enabled:hover:text-surface-200 dark:enabled:active:text-surface-100
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2
        focus-visible:outline-surface-600 dark:focus-visible:outline-surface-300
        p-vertical:flex-col p-fluid:w-full p-fluid:p-icon-only:w-10
        p-icon-only:w-10 p-icon-only:px-0 p-icon-only:gap-0
        p-icon-only:p-rounded:rounded-full p-icon-only:p-rounded:h-10
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-[1.125rem] p-large:px-[0.875rem] p-large:py-[0.625rem]
        p-raised:shadow-sm p-rounded:rounded-[2rem]
        p-outlined:bg-transparent enabled:hover:p-outlined:bg-surface-50 enabled:active:p-outlined:bg-surface-100
        p-outlined:border-surface-200 enabled:hover:p-outlined:border-surface-200 enabled:active:p-outlined:border-surface-200
        p-outlined:text-surface-500 enabled:hover:p-outlined:text-surface-500 enabled:active:p-outlined:text-surface-500
        dark:p-outlined:bg-transparent dark:enabled:hover:p-outlined:bg-white/5 dark:enabled:active:p-outlined:bg-white/15
        dark:p-outlined:border-surface-700 dark:enabled:hover:p-outlined:border-surface-700 dark:enabled:active:p-outlined:border-surface-700
        dark:p-outlined:text-surface-400 dark:enabled:hover:p-outlined:text-surface-400 dark:enabled:active:p-outlined:text-surface-400
        p-text:bg-transparent enabled:hover:p-text:bg-surface-50 enabled:active:p-text:bg-surface-100
        p-text:border-transparent enabled:hover:p-text:border-transparent enabled:active:p-text:border-transparent
        p-text:text-surface-500 enabled:hover:p-text:text-surface-500 enabled:active:p-text:text-surface-500
        dark:p-text:bg-transparent dark:enabled:hover:p-text:bg-surface-800 dark:enabled:active:p-text:bg-surface-700
        dark:p-text:border-transparent dark:enabled:hover:p-text:border-transparent dark:enabled:active:p-text:border-transparent
        dark:p-text:text-surface-400 dark:enabled:hover:p-text:text-surface-400 dark:enabled:active:p-text:text-surface-400
    `,
      loadingIcon: ``,
      icon: `p-right:order-1 p-bottom:order-2`,
      label: `font-medium p-icon-only:invisible p-icon-only:w-0
        p-small:text-sm p-large:text-[1.125rem]`,
      pcBadge: {
        root: `min-w-4 h-4 leading-4`
      }
    });
    const __returned__ = { theme, get Button() {
      return script$6;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$8(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Button"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$8 = _sfc_main$8.setup;
_sfc_main$8.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/SecondaryButton.vue");
  return _sfc_setup$8 ? _sfc_setup$8(props, ctx) : void 0;
};
const SecondaryButton = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["ssrRender", _sfc_ssrRender$8]]);

var classes$2 = {
  root: function root(_ref) {
    var props = _ref.props;
    return ['p-avatar p-component', {
      'p-avatar-image': props.image != null,
      'p-avatar-circle': props.shape === 'circle',
      'p-avatar-lg': props.size === 'large',
      'p-avatar-xl': props.size === 'xlarge'
    }];
  },
  label: 'p-avatar-label',
  icon: 'p-avatar-icon'
};
var AvatarStyle = BaseStyle.extend({
  name: 'avatar',
  style: style$1,
  classes: classes$2
});

var script$1$2 = {
  name: 'BaseAvatar',
  "extends": BaseComponent,
  props: {
    label: {
      type: String,
      "default": null
    },
    icon: {
      type: String,
      "default": null
    },
    image: {
      type: String,
      "default": null
    },
    size: {
      type: String,
      "default": 'normal'
    },
    shape: {
      type: String,
      "default": 'square'
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    }
  },
  style: AvatarStyle,
  provide: function provide() {
    return {
      $pcAvatar: this,
      $parentInstance: this
    };
  }
};

function _typeof$5(o) { "@babel/helpers - typeof"; return _typeof$5 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$5(o); }
function _defineProperty$5(e, r, t) { return (r = _toPropertyKey$5(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$5(t) { var i = _toPrimitive$5(t, "string"); return "symbol" == _typeof$5(i) ? i : i + ""; }
function _toPrimitive$5(t, r) { if ("object" != _typeof$5(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$5(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script$4 = {
  name: 'Avatar',
  "extends": script$1$2,
  inheritAttrs: false,
  emits: ['error'],
  methods: {
    onError: function onError(event) {
      this.$emit('error', event);
    }
  },
  computed: {
    dataP: function dataP() {
      return cn(_defineProperty$5(_defineProperty$5({}, this.shape, this.shape), this.size, this.size));
    }
  }
};

var _hoisted_1$3 = ["aria-labelledby", "aria-label", "data-p"];
var _hoisted_2$3 = ["data-p"];
var _hoisted_3$3 = ["data-p"];
var _hoisted_4$2 = ["src", "alt", "data-p"];
function render$3(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel
  }, _ctx.ptmi('root'), {
    "data-p": $options.dataP
  }), [renderSlot(_ctx.$slots, "default", {}, function () {
    return [_ctx.label ? (openBlock(), createElementBlock("span", mergeProps({
      key: 0,
      "class": _ctx.cx('label')
    }, _ctx.ptm('label'), {
      "data-p": $options.dataP
    }), toDisplayString(_ctx.label), 17, _hoisted_2$3)) : _ctx.$slots.icon ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.icon), {
      key: 1,
      "class": normalizeClass(_ctx.cx('icon'))
    }, null, 8, ["class"])) : _ctx.icon ? (openBlock(), createElementBlock("span", mergeProps({
      key: 2,
      "class": [_ctx.cx('icon'), _ctx.icon]
    }, _ctx.ptm('icon'), {
      "data-p": $options.dataP
    }), null, 16, _hoisted_3$3)) : _ctx.image ? (openBlock(), createElementBlock("img", mergeProps({
      key: 3,
      src: _ctx.image,
      alt: _ctx.ariaLabel,
      onError: _cache[0] || (_cache[0] = function () {
        return $options.onError && $options.onError.apply($options, arguments);
      })
    }, _ctx.ptm('image'), {
      "data-p": $options.dataP
    }), null, 16, _hoisted_4$2)) : createCommentVNode("", true)];
  })], 16, _hoisted_1$3);
}

script$4.render = render$3;

const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "Avatar",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex items-center justify-center
        w-8 h-8 text-base rounded-md
        bg-surface-200 dark:bg-surface-700
        has-[img]:bg-transparent
        p-circle:rounded-full
        p-large:w-12 p-large:h-12 p-large:text-2xl
        p-xlarge:w-16 p-xlarge:h-16 p-xlarge:text-[2rem]`,
      label: ``,
      icon: `text-base p-large:text-2xl p-xlarge:text-[2rem]`,
      image: `p-circle:rounded-full w-full h-full`
    });
    const __returned__ = { theme, get Avatar() {
      return script$4;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$7(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Avatar"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$7 = _sfc_main$7.setup;
_sfc_main$7.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Avatar.vue");
  return _sfc_setup$7 ? _sfc_setup$7(props, ctx) : void 0;
};
const Avatar = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["ssrRender", _sfc_ssrRender$7]]);

var classes$1 = {
  root: function root(_ref) {
    var props = _ref.props;
    return ['p-menu p-component', {
      'p-menu-overlay': props.popup
    }];
  },
  start: 'p-menu-start',
  list: 'p-menu-list',
  submenuLabel: 'p-menu-submenu-label',
  separator: 'p-menu-separator',
  end: 'p-menu-end',
  item: function item(_ref2) {
    var instance = _ref2.instance;
    return ['p-menu-item', {
      'p-focus': instance.id === instance.focusedOptionId,
      'p-disabled': instance.disabled()
    }];
  },
  itemContent: 'p-menu-item-content',
  itemLink: 'p-menu-item-link',
  itemIcon: 'p-menu-item-icon',
  itemLabel: 'p-menu-item-label'
};
var MenuStyle = BaseStyle.extend({
  name: 'menu',
  style: style$2,
  classes: classes$1
});

var script$2$1 = {
  name: 'BaseMenu',
  "extends": BaseComponent,
  props: {
    popup: {
      type: Boolean,
      "default": false
    },
    model: {
      type: Array,
      "default": null
    },
    appendTo: {
      type: [String, Object],
      "default": 'body'
    },
    autoZIndex: {
      type: Boolean,
      "default": true
    },
    baseZIndex: {
      type: Number,
      "default": 0
    },
    tabindex: {
      type: Number,
      "default": 0
    },
    ariaLabel: {
      type: String,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    }
  },
  style: MenuStyle,
  provide: function provide() {
    return {
      $pcMenu: this,
      $parentInstance: this
    };
  }
};

var script$1$1 = {
  name: 'Menuitem',
  hostName: 'Menu',
  "extends": BaseComponent,
  inheritAttrs: false,
  emits: ['item-click', 'item-mousemove'],
  props: {
    item: null,
    templates: null,
    id: null,
    focusedOptionId: null,
    index: null
  },
  methods: {
    getItemProp: function getItemProp(processedItem, name) {
      return processedItem && processedItem.item ? resolve(processedItem.item[name]) : undefined;
    },
    getPTOptions: function getPTOptions(key) {
      return this.ptm(key, {
        context: {
          item: this.item,
          index: this.index,
          focused: this.isItemFocused(),
          disabled: this.disabled()
        }
      });
    },
    isItemFocused: function isItemFocused() {
      return this.focusedOptionId === this.id;
    },
    onItemClick: function onItemClick(event) {
      var command = this.getItemProp(this.item, 'command');
      command && command({
        originalEvent: event,
        item: this.item.item
      });
      this.$emit('item-click', {
        originalEvent: event,
        item: this.item,
        id: this.id
      });
    },
    onItemMouseMove: function onItemMouseMove(event) {
      this.$emit('item-mousemove', {
        originalEvent: event,
        item: this.item,
        id: this.id
      });
    },
    visible: function visible() {
      return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;
    },
    disabled: function disabled() {
      return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;
    },
    label: function label() {
      return typeof this.item.label === 'function' ? this.item.label() : this.item.label;
    },
    getMenuItemProps: function getMenuItemProps(item) {
      return {
        action: mergeProps({
          "class": this.cx('itemLink'),
          tabindex: '-1'
        }, this.getPTOptions('itemLink')),
        icon: mergeProps({
          "class": [this.cx('itemIcon'), item.icon]
        }, this.getPTOptions('itemIcon')),
        label: mergeProps({
          "class": this.cx('itemLabel')
        }, this.getPTOptions('itemLabel'))
      };
    }
  },
  computed: {
    dataP: function dataP() {
      return cn({
        focus: this.isItemFocused(),
        disabled: this.disabled()
      });
    }
  },
  directives: {
    ripple: Ripple
  }
};

var _hoisted_1$1$1 = ["id", "aria-label", "aria-disabled", "data-p-focused", "data-p-disabled", "data-p"];
var _hoisted_2$1 = ["data-p"];
var _hoisted_3$1 = ["href", "target"];
var _hoisted_4$1 = ["data-p"];
var _hoisted_5$1 = ["data-p"];
function render$1$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return $options.visible() ? (openBlock(), createElementBlock("li", mergeProps({
    key: 0,
    id: $props.id,
    "class": [_ctx.cx('item'), $props.item["class"]],
    role: "menuitem",
    style: $props.item.style,
    "aria-label": $options.label(),
    "aria-disabled": $options.disabled(),
    "data-p-focused": $options.isItemFocused(),
    "data-p-disabled": $options.disabled() || false,
    "data-p": $options.dataP
  }, $options.getPTOptions('item')), [createElementVNode("div", mergeProps({
    "class": _ctx.cx('itemContent'),
    onClick: _cache[0] || (_cache[0] = function ($event) {
      return $options.onItemClick($event);
    }),
    onMousemove: _cache[1] || (_cache[1] = function ($event) {
      return $options.onItemMouseMove($event);
    }),
    "data-p": $options.dataP
  }, $options.getPTOptions('itemContent')), [!$props.templates.item ? withDirectives((openBlock(), createElementBlock("a", mergeProps({
    key: 0,
    href: $props.item.url,
    "class": _ctx.cx('itemLink'),
    target: $props.item.target,
    tabindex: "-1"
  }, $options.getPTOptions('itemLink')), [$props.templates.itemicon ? (openBlock(), createBlock(resolveDynamicComponent($props.templates.itemicon), {
    key: 0,
    item: $props.item,
    "class": normalizeClass(_ctx.cx('itemIcon'))
  }, null, 8, ["item", "class"])) : $props.item.icon ? (openBlock(), createElementBlock("span", mergeProps({
    key: 1,
    "class": [_ctx.cx('itemIcon'), $props.item.icon],
    "data-p": $options.dataP
  }, $options.getPTOptions('itemIcon')), null, 16, _hoisted_4$1)) : createCommentVNode("", true), createElementVNode("span", mergeProps({
    "class": _ctx.cx('itemLabel'),
    "data-p": $options.dataP
  }, $options.getPTOptions('itemLabel')), toDisplayString($options.label()), 17, _hoisted_5$1)], 16, _hoisted_3$1)), [[_directive_ripple]]) : $props.templates.item ? (openBlock(), createBlock(resolveDynamicComponent($props.templates.item), {
    key: 1,
    item: $props.item,
    label: $options.label(),
    props: $options.getMenuItemProps($props.item)
  }, null, 8, ["item", "label", "props"])) : createCommentVNode("", true)], 16, _hoisted_2$1)], 16, _hoisted_1$1$1)) : createCommentVNode("", true);
}

script$1$1.render = render$1$1;

function _toConsumableArray$1(r) { return _arrayWithoutHoles$1(r) || _iterableToArray$1(r) || _unsupportedIterableToArray$1(r) || _nonIterableSpread$1(); }
function _nonIterableSpread$1() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray$1(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray$1(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0; } }
function _iterableToArray$1(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles$1(r) { if (Array.isArray(r)) return _arrayLikeToArray$1(r); }
function _arrayLikeToArray$1(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var script$3 = {
  name: 'Menu',
  "extends": script$2$1,
  inheritAttrs: false,
  emits: ['show', 'hide', 'focus', 'blur'],
  data: function data() {
    return {
      overlayVisible: false,
      focused: false,
      focusedOptionIndex: -1,
      selectedOptionIndex: -1
    };
  },
  target: null,
  outsideClickListener: null,
  scrollHandler: null,
  resizeListener: null,
  container: null,
  list: null,
  mounted: function mounted() {
    if (!this.popup) {
      this.bindResizeListener();
      this.bindOutsideClickListener();
    }
  },
  beforeUnmount: function beforeUnmount() {
    this.unbindResizeListener();
    this.unbindOutsideClickListener();
    if (this.scrollHandler) {
      this.scrollHandler.destroy();
      this.scrollHandler = null;
    }
    this.target = null;
    if (this.container && this.autoZIndex) {
      ZIndex.clear(this.container);
    }
    this.container = null;
  },
  methods: {
    itemClick: function itemClick(event) {
      var item = event.item;
      if (this.disabled(item)) {
        return;
      }
      if (item.command) {
        item.command(event);
      }
      if (this.overlayVisible) this.hide();
      if (!this.popup && this.focusedOptionIndex !== event.id) {
        this.focusedOptionIndex = event.id;
      }
    },
    itemMouseMove: function itemMouseMove(event) {
      if (this.focused) {
        this.focusedOptionIndex = event.id;
      }
    },
    onListFocus: function onListFocus(event) {
      this.focused = true;
      !this.popup && this.changeFocusedOptionIndex(0);
      this.$emit('focus', event);
    },
    onListBlur: function onListBlur(event) {
      this.focused = false;
      this.focusedOptionIndex = -1;
      this.$emit('blur', event);
    },
    onListKeyDown: function onListKeyDown(event) {
      switch (event.code) {
        case 'ArrowDown':
          this.onArrowDownKey(event);
          break;
        case 'ArrowUp':
          this.onArrowUpKey(event);
          break;
        case 'Home':
          this.onHomeKey(event);
          break;
        case 'End':
          this.onEndKey(event);
          break;
        case 'Enter':
        case 'NumpadEnter':
          this.onEnterKey(event);
          break;
        case 'Space':
          this.onSpaceKey(event);
          break;
        case 'Escape':
          if (this.popup) {
            focus(this.target);
            this.hide();
          }
        case 'Tab':
          this.overlayVisible && this.hide();
          break;
      }
    },
    onArrowDownKey: function onArrowDownKey(event) {
      var optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);
      this.changeFocusedOptionIndex(optionIndex);
      event.preventDefault();
    },
    onArrowUpKey: function onArrowUpKey(event) {
      if (event.altKey && this.popup) {
        focus(this.target);
        this.hide();
        event.preventDefault();
      } else {
        var optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);
        this.changeFocusedOptionIndex(optionIndex);
        event.preventDefault();
      }
    },
    onHomeKey: function onHomeKey(event) {
      this.changeFocusedOptionIndex(0);
      event.preventDefault();
    },
    onEndKey: function onEndKey(event) {
      this.changeFocusedOptionIndex(find(this.container, 'li[data-pc-section="item"][data-p-disabled="false"]').length - 1);
      event.preventDefault();
    },
    onEnterKey: function onEnterKey(event) {
      var element = findSingle(this.list, "li[id=\"".concat("".concat(this.focusedOptionIndex), "\"]"));
      var anchorElement = element && findSingle(element, 'a[data-pc-section="itemlink"]');
      this.popup && focus(this.target);
      anchorElement ? anchorElement.click() : element && element.click();
      event.preventDefault();
    },
    onSpaceKey: function onSpaceKey(event) {
      this.onEnterKey(event);
    },
    findNextOptionIndex: function findNextOptionIndex(index) {
      var links = find(this.container, 'li[data-pc-section="item"][data-p-disabled="false"]');
      var matchedOptionIndex = _toConsumableArray$1(links).findIndex(function (link) {
        return link.id === index;
      });
      return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;
    },
    findPrevOptionIndex: function findPrevOptionIndex(index) {
      var links = find(this.container, 'li[data-pc-section="item"][data-p-disabled="false"]');
      var matchedOptionIndex = _toConsumableArray$1(links).findIndex(function (link) {
        return link.id === index;
      });
      return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;
    },
    changeFocusedOptionIndex: function changeFocusedOptionIndex(index) {
      var links = find(this.container, 'li[data-pc-section="item"][data-p-disabled="false"]');
      var order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;
      order > -1 && (this.focusedOptionIndex = links[order].getAttribute('id'));
    },
    toggle: function toggle(event, target) {
      if (this.overlayVisible) this.hide();else this.show(event, target);
    },
    show: function show(event, target) {
      this.overlayVisible = true;
      this.target = target !== null && target !== void 0 ? target : event.currentTarget;
    },
    hide: function hide() {
      this.overlayVisible = false;
      this.target = null;
    },
    onEnter: function onEnter(el) {
      addStyle(el, {
        position: 'absolute',
        top: '0'
      });
      this.alignOverlay();
      this.bindOutsideClickListener();
      this.bindResizeListener();
      this.bindScrollListener();
      if (this.autoZIndex) {
        ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);
      }
      if (this.popup) {
        focus(this.list);
      }
      this.$emit('show');
    },
    onLeave: function onLeave() {
      this.unbindOutsideClickListener();
      this.unbindResizeListener();
      this.unbindScrollListener();
      this.$emit('hide');
    },
    onAfterLeave: function onAfterLeave(el) {
      if (this.autoZIndex) {
        ZIndex.clear(el);
      }
    },
    alignOverlay: function alignOverlay() {
      absolutePosition(this.container, this.target);
      var targetWidth = getOuterWidth(this.target);
      if (targetWidth > getOuterWidth(this.container)) {
        this.container.style.minWidth = getOuterWidth(this.target) + 'px';
      }
    },
    bindOutsideClickListener: function bindOutsideClickListener() {
      var _this = this;
      if (!this.outsideClickListener) {
        this.outsideClickListener = function (event) {
          var isOutsideContainer = _this.container && !_this.container.contains(event.target);
          var isOutsideTarget = !(_this.target && (_this.target === event.target || _this.target.contains(event.target)));
          if (_this.overlayVisible && isOutsideContainer && isOutsideTarget) {
            _this.hide();
          } else if (!_this.popup && isOutsideContainer && isOutsideTarget) {
            _this.focusedOptionIndex = -1;
          }
        };
        document.addEventListener('click', this.outsideClickListener, true);
      }
    },
    unbindOutsideClickListener: function unbindOutsideClickListener() {
      if (this.outsideClickListener) {
        document.removeEventListener('click', this.outsideClickListener, true);
        this.outsideClickListener = null;
      }
    },
    bindScrollListener: function bindScrollListener() {
      var _this2 = this;
      if (!this.scrollHandler) {
        this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, function () {
          if (_this2.overlayVisible) {
            _this2.hide();
          }
        });
      }
      this.scrollHandler.bindScrollListener();
    },
    unbindScrollListener: function unbindScrollListener() {
      if (this.scrollHandler) {
        this.scrollHandler.unbindScrollListener();
      }
    },
    bindResizeListener: function bindResizeListener() {
      var _this3 = this;
      if (!this.resizeListener) {
        this.resizeListener = function () {
          if (_this3.overlayVisible && !isTouchDevice()) {
            _this3.hide();
          }
        };
        window.addEventListener('resize', this.resizeListener);
      }
    },
    unbindResizeListener: function unbindResizeListener() {
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
        this.resizeListener = null;
      }
    },
    visible: function visible(item) {
      return typeof item.visible === 'function' ? item.visible() : item.visible !== false;
    },
    disabled: function disabled(item) {
      return typeof item.disabled === 'function' ? item.disabled() : item.disabled;
    },
    label: function label(item) {
      return typeof item.label === 'function' ? item.label() : item.label;
    },
    onOverlayClick: function onOverlayClick(event) {
      OverlayEventBus.emit('overlay-click', {
        originalEvent: event,
        target: this.target
      });
    },
    containerRef: function containerRef(el) {
      this.container = el;
    },
    listRef: function listRef(el) {
      this.list = el;
    }
  },
  computed: {
    focusedOptionId: function focusedOptionId() {
      return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;
    },
    dataP: function dataP() {
      return cn({
        popup: this.popup
      });
    }
  },
  components: {
    PVMenuitem: script$1$1,
    Portal: script$7
  }
};

var _hoisted_1$2 = ["id", "data-p"];
var _hoisted_2$2 = ["id", "tabindex", "aria-activedescendant", "aria-label", "aria-labelledby"];
var _hoisted_3$2 = ["id"];
function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_PVMenuitem = resolveComponent("PVMenuitem");
  var _component_Portal = resolveComponent("Portal");
  return openBlock(), createBlock(_component_Portal, {
    appendTo: _ctx.appendTo,
    disabled: !_ctx.popup
  }, {
    "default": withCtx(function () {
      return [createVNode(Transition, mergeProps({
        name: "p-connected-overlay",
        onEnter: $options.onEnter,
        onLeave: $options.onLeave,
        onAfterLeave: $options.onAfterLeave
      }, _ctx.ptm('transition')), {
        "default": withCtx(function () {
          return [(_ctx.popup ? $data.overlayVisible : true) ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            ref: $options.containerRef,
            id: _ctx.$id,
            "class": _ctx.cx('root'),
            onClick: _cache[3] || (_cache[3] = function () {
              return $options.onOverlayClick && $options.onOverlayClick.apply($options, arguments);
            }),
            "data-p": $options.dataP
          }, _ctx.ptmi('root')), [_ctx.$slots.start ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            "class": _ctx.cx('start')
          }, _ctx.ptm('start')), [renderSlot(_ctx.$slots, "start")], 16)) : createCommentVNode("", true), createElementVNode("ul", mergeProps({
            ref: $options.listRef,
            id: _ctx.$id + '_list',
            "class": _ctx.cx('list'),
            role: "menu",
            tabindex: _ctx.tabindex,
            "aria-activedescendant": $data.focused ? $options.focusedOptionId : undefined,
            "aria-label": _ctx.ariaLabel,
            "aria-labelledby": _ctx.ariaLabelledby,
            onFocus: _cache[0] || (_cache[0] = function () {
              return $options.onListFocus && $options.onListFocus.apply($options, arguments);
            }),
            onBlur: _cache[1] || (_cache[1] = function () {
              return $options.onListBlur && $options.onListBlur.apply($options, arguments);
            }),
            onKeydown: _cache[2] || (_cache[2] = function () {
              return $options.onListKeyDown && $options.onListKeyDown.apply($options, arguments);
            })
          }, _ctx.ptm('list')), [(openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.model, function (item, i) {
            return openBlock(), createElementBlock(Fragment, {
              key: $options.label(item) + i.toString()
            }, [item.items && $options.visible(item) && !item.separator ? (openBlock(), createElementBlock(Fragment, {
              key: 0
            }, [item.items ? (openBlock(), createElementBlock("li", mergeProps({
              key: 0,
              id: _ctx.$id + '_' + i,
              "class": [_ctx.cx('submenuLabel'), item["class"]],
              role: "none"
            }, {
              ref_for: true
            }, _ctx.ptm('submenuLabel')), [renderSlot(_ctx.$slots, _ctx.$slots.submenulabel ? 'submenulabel' : 'submenuheader', {
              item: item
            }, function () {
              return [createTextVNode(toDisplayString($options.label(item)), 1)];
            })], 16, _hoisted_3$2)) : createCommentVNode("", true), (openBlock(true), createElementBlock(Fragment, null, renderList(item.items, function (child, j) {
              return openBlock(), createElementBlock(Fragment, {
                key: child.label + i + '_' + j
              }, [$options.visible(child) && !child.separator ? (openBlock(), createBlock(_component_PVMenuitem, {
                key: 0,
                id: _ctx.$id + '_' + i + '_' + j,
                item: child,
                templates: _ctx.$slots,
                focusedOptionId: $options.focusedOptionId,
                unstyled: _ctx.unstyled,
                onItemClick: $options.itemClick,
                onItemMousemove: $options.itemMouseMove,
                pt: _ctx.pt
              }, null, 8, ["id", "item", "templates", "focusedOptionId", "unstyled", "onItemClick", "onItemMousemove", "pt"])) : $options.visible(child) && child.separator ? (openBlock(), createElementBlock("li", mergeProps({
                key: 'separator' + i + j,
                "class": [_ctx.cx('separator'), item["class"]],
                style: child.style,
                role: "separator"
              }, {
                ref_for: true
              }, _ctx.ptm('separator')), null, 16)) : createCommentVNode("", true)], 64);
            }), 128))], 64)) : $options.visible(item) && item.separator ? (openBlock(), createElementBlock("li", mergeProps({
              key: 'separator' + i.toString(),
              "class": [_ctx.cx('separator'), item["class"]],
              style: item.style,
              role: "separator"
            }, {
              ref_for: true
            }, _ctx.ptm('separator')), null, 16)) : (openBlock(), createBlock(_component_PVMenuitem, {
              key: $options.label(item) + i.toString(),
              id: _ctx.$id + '_' + i,
              item: item,
              index: i,
              templates: _ctx.$slots,
              focusedOptionId: $options.focusedOptionId,
              unstyled: _ctx.unstyled,
              onItemClick: $options.itemClick,
              onItemMousemove: $options.itemMouseMove,
              pt: _ctx.pt
            }, null, 8, ["id", "item", "index", "templates", "focusedOptionId", "unstyled", "onItemClick", "onItemMousemove", "pt"]))], 64);
          }), 128))], 16, _hoisted_2$2), _ctx.$slots.end ? (openBlock(), createElementBlock("div", mergeProps({
            key: 1,
            "class": _ctx.cx('end')
          }, _ctx.ptm('end')), [renderSlot(_ctx.$slots, "end")], 16)) : createCommentVNode("", true)], 16, _hoisted_1$2)) : createCommentVNode("", true)];
        }),
        _: 3
      }, 16, ["onEnter", "onLeave", "onAfterLeave"])];
    }),
    _: 3
  }, 8, ["appendTo", "disabled"]);
}

script$3.render = render$2;

const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "Menu",
  setup(__props, { expose: __expose }) {
    const theme = ref({
      root: `bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0 
        border border-surface-200 dark:border-surface-700
        rounded-md min-w-52
        p-popup:shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]`,
      list: `m-0 p-1 list-none outline-none flex flex-col gap-[2px]`,
      item: `p-disabled:opacity-60 p-disabled:pointer-events-none`,
      itemContent: `group transition-colors duration-200 rounded-sm text-surface-700 dark:text-surface-0
        p-focus:bg-surface-100 dark:p-focus:bg-surface-800 p-focus:text-surface-800 dark:p-focus:text-surface-0
        hover:bg-surface-100 dark:hover:bg-surface-800 hover:text-surface-800 dark:hover:text-surface-0`,
      itemLink: `cursor-pointer flex items-center no-underline overflow-hidden relative text-inherit
        px-3 py-2 gap-2 select-none outline-none`,
      itemIcon: `text-surface-400 dark:text-surface-500
        p-focus:text-surface-500 dark:p-focus:text-surface-400
        group-hover:text-surface-500 dark:group-hover:text-surface-400`,
      itemLabel: ``,
      submenuLabel: `bg-transparent px-3 py-2 text-surface-500 dark:text-surface-400 font-semibold`,
      separator: `border-t border-surface-200 dark:border-surface-700`,
      transition: {
        enterFromClass: "opacity-0 scale-y-75",
        enterActiveClass: "transition duration-120 ease-[cubic-bezier(0,0,0.2,1)]",
        leaveActiveClass: "transition-opacity duration-100 ease-linear",
        leaveToClass: "opacity-0"
      }
    });
    const el = ref();
    __expose({
      toggle: (event) => el.value.toggle(event)
    });
    const __returned__ = { theme, el, get Menu() {
      return script$3;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$6(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Menu"], mergeProps({
    ref: "el",
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Menu.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const Menu = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["ssrRender", _sfc_ssrRender$6]]);

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "Icon",
  props: {
    name: {},
    size: { default: 16 },
    color: {},
    class: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const iconMap = {
      "plus": "\u2795",
      "edit": "\u270F\uFE0F",
      "trash": "\u{1F5D1}\uFE0F",
      "search": "\u{1F50D}",
      "filter": "\u{1F53D}",
      "check": "\u2705",
      "x": "\u274C",
      "chevron-up": "\u2B06\uFE0F",
      "chevron-down": "\u2B07\uFE0F",
      "folder": "\u{1F4C1}",
      "ruler": "\u{1F4CF}",
      "tag": "\u{1F3F7}\uFE0F",
      "template": "\u{1F4CB}",
      "info": "\u2139\uFE0F",
      "target": "\u{1F3AF}",
      "grid": "\u229E",
      "list": "\u2630"
    };
    const iconClass = computed(() => {
      return "inline-block";
    });
    const iconStyle = computed(() => {
      return {
        fontSize: typeof props.size === "number" ? `${props.size}px` : props.size,
        color: props.color
      };
    });
    const iconContent = computed(() => {
      return iconMap[props.name] || "?";
    });
    const __returned__ = { props, iconMap, iconClass, iconStyle, iconContent };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _cssVars = { style: {
    "--45b0056e": $setup.iconContent
  } };
  _push(`<i${ssrRenderAttrs(mergeProps({
    class: $setup.iconClass,
    style: $setup.iconStyle
  }, _ctx.$attrs, _attrs, _cssVars))} data-v-1ba1abfe></i>`);
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/Icon.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const Icon = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5], ["__scopeId", "data-v-1ba1abfe"]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "UserMenu",
  setup(__props, { expose: __expose }) {
    __expose();
    const {
      user,
      displayName,
      userAvatar,
      userRole,
      signOut,
      loadingState
    } = useAuth();
    const menu = ref();
    const isMenuOpen = ref(false);
    const userInitials = computed(() => {
      if (!displayName.value) return "U";
      return displayName.value.split(" ").map((name) => name.charAt(0)).join("").toUpperCase().slice(0, 2);
    });
    const roleLabel = computed(() => {
      switch (userRole.value) {
        case "ADMIN":
          return "\u0410\u0434\u043C\u0438\u043D\u0438\u0441\u0442\u0440\u0430\u0442\u043E\u0440";
        case "SHOP":
          return "\u0412\u043B\u0430\u0434\u0435\u043B\u0435\u0446 \u043C\u0430\u0433\u0430\u0437\u0438\u043D\u0430";
        case "USER":
          return "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C";
        case "GUEST":
          return "\u0413\u043E\u0441\u0442\u044C";
        default:
          return "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C";
      }
    });
    const isSigningOut = computed(() => loadingState.signOut);
    const menuItems = computed(() => [
      {
        label: "\u041F\u0440\u043E\u0444\u0438\u043B\u044C",
        icon: "pi pi-user",
        command: () => {
          navigate();
        }
      },
      {
        label: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",
        icon: "pi pi-cog",
        command: () => {
          navigate();
        }
      },
      {
        separator: true
      },
      {
        label: isSigningOut.value ? "\u0412\u044B\u0445\u043E\u0434..." : "\u0412\u044B\u0439\u0442\u0438",
        icon: "pi pi-sign-out",
        class: "text-red-600",
        command: handleSignOut
      }
    ]);
    const toggleMenu = (event) => {
      menu.value.toggle(event);
    };
    const handleSignOut = async () => {
      navigate();
    };
    const __returned__ = { user, displayName, userAvatar, userRole, signOut, loadingState, menu, isMenuOpen, userInitials, roleLabel, isSigningOut, menuItems, toggleMenu, handleSignOut, SecondaryButton, Avatar, Menu, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "relative" }, _attrs))} data-v-b7dcda07>`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    onClick: $setup.toggleMenu,
    text: "",
    class: "flex items-center space-x-3"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Avatar"], {
          image: $setup.userAvatar,
          label: $setup.userInitials,
          size: "normal",
          shape: "circle"
        }, null, _parent2, _scopeId));
        _push2(`<div class="hidden md:block text-left" data-v-b7dcda07${_scopeId}><p class="text-sm font-medium text-surface-700" data-v-b7dcda07${_scopeId}>${ssrInterpolate($setup.displayName)}</p><p class="text-xs text-surface-500" data-v-b7dcda07${_scopeId}>${ssrInterpolate($setup.roleLabel)}</p></div>`);
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-chevron-down",
          class: ["text-surface-400 transition-transform duration-200", { "rotate-180": $setup.isMenuOpen }]
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Avatar"], {
            image: $setup.userAvatar,
            label: $setup.userInitials,
            size: "normal",
            shape: "circle"
          }, null, 8, ["image", "label"]),
          createVNode("div", { class: "hidden md:block text-left" }, [
            createVNode("p", { class: "text-sm font-medium text-surface-700" }, toDisplayString($setup.displayName), 1),
            createVNode("p", { class: "text-xs text-surface-500" }, toDisplayString($setup.roleLabel), 1)
          ]),
          createVNode($setup["Icon"], {
            name: "pi pi-chevron-down",
            class: ["text-surface-400 transition-transform duration-200", { "rotate-180": $setup.isMenuOpen }]
          }, null, 8, ["class"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Menu"], {
    ref: "menu",
    model: $setup.menuItems,
    popup: true,
    class: "w-56"
  }, {
    start: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="px-4 py-3 border-b border-surface-200" data-v-b7dcda07${_scopeId}><p class="text-sm font-medium text-surface-900" data-v-b7dcda07${_scopeId}>${ssrInterpolate($setup.displayName)}</p><p class="text-sm text-surface-500" data-v-b7dcda07${_scopeId}>${ssrInterpolate($setup.user?.email)}</p><p class="text-xs text-surface-400 mt-1" data-v-b7dcda07${_scopeId}>${ssrInterpolate($setup.roleLabel)}</p></div>`);
      } else {
        return [
          createVNode("div", { class: "px-4 py-3 border-b border-surface-200" }, [
            createVNode("p", { class: "text-sm font-medium text-surface-900" }, toDisplayString($setup.displayName), 1),
            createVNode("p", { class: "text-sm text-surface-500" }, toDisplayString($setup.user?.email), 1),
            createVNode("p", { class: "text-xs text-surface-400 mt-1" }, toDisplayString($setup.roleLabel), 1)
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($setup.isMenuOpen) {
    _push(`<div class="fixed inset-0 z-40" data-v-b7dcda07></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/auth/UserMenu.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const UserMenu = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4], ["__scopeId", "data-v-b7dcda07"]]);

const STORAGE_KEY = "parttec-theme";
const currentTheme = ref("system");
const systemPrefersDark = ref(false);
const activeTheme = computed(() => {
  if (currentTheme.value === "system") {
    return systemPrefersDark.value ? "dark" : "light";
  }
  return currentTheme.value;
});
const applyTheme = (theme) => {
  if (typeof document !== "undefined") {
    const root = document.documentElement;
    if (theme === "dark") {
      root.setAttribute("data-theme", "dark");
      root.classList.add("dark");
    } else {
      root.removeAttribute("data-theme");
      root.classList.remove("dark");
    }
  }
};
const updateSystemTheme = () => {
  if (typeof window !== "undefined" && window.matchMedia) {
    systemPrefersDark.value = window.matchMedia("(prefers-color-scheme: dark)").matches;
  }
};
const loadTheme = () => {
  if (typeof window !== "undefined") {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored && ["light", "dark", "system"].includes(stored)) {
      return stored;
    }
  }
  return "system";
};
const saveTheme = (theme) => {
  if (typeof window !== "undefined") {
    localStorage.setItem(STORAGE_KEY, theme);
  }
};
const useTheme = () => {
  onMounted(() => {
    currentTheme.value = loadTheme();
    updateSystemTheme();
    if (typeof window !== "undefined" && window.matchMedia) {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e) => {
        systemPrefersDark.value = e.matches;
      };
      mediaQuery.addEventListener("change", handleChange);
      return () => {
        mediaQuery.removeEventListener("change", handleChange);
      };
    }
  });
  watch(activeTheme, (newTheme) => {
    applyTheme(newTheme);
  }, { immediate: true });
  watch(currentTheme, (newTheme) => {
    saveTheme(newTheme);
  });
  const setTheme = (theme) => {
    currentTheme.value = theme;
  };
  const toggleTheme = () => {
    if (currentTheme.value === "system") {
      const newTheme = systemPrefersDark.value ? "light" : "dark";
      setTheme(newTheme);
    } else if (currentTheme.value === "light") {
      setTheme("dark");
    } else {
      setTheme("light");
    }
  };
  const resetToSystem = () => {
    setTheme("system");
  };
  const isDark = computed(() => activeTheme.value === "dark");
  const isLight = computed(() => activeTheme.value === "light");
  const isSystem = computed(() => currentTheme.value === "system");
  const themeIcon = computed(() => {
    switch (currentTheme.value) {
      case "light":
        return "Sun";
      case "dark":
        return "Moon";
      case "system":
        return "Monitor";
      default:
        return "Monitor";
    }
  });
  const themeName = computed(() => {
    switch (currentTheme.value) {
      case "light":
        return "Светлая";
      case "dark":
        return "Темная";
      case "system":
        return "Системная";
      default:
        return "Системная";
    }
  });
  return {
    // Состояние (только для чтения)
    currentTheme: computed(() => currentTheme.value),
    activeTheme,
    systemPrefersDark: computed(() => systemPrefersDark.value),
    // Вычисляемые значения
    isDark,
    isLight,
    isSystem,
    themeIcon,
    themeName,
    // Методы
    setTheme,
    toggleTheme,
    resetToSystem
  };
};

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "ThemeToggle",
  props: {
    mode: { default: "toggle" },
    showLabel: { type: Boolean, default: false },
    buttonClass: { default: "" }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const {
      currentTheme,
      activeTheme,
      isDark,
      isLight,
      isSystem,
      themeIcon,
      themeName,
      systemPrefersDark,
      setTheme,
      toggleTheme
    } = useTheme();
    const showMenu = ref(false);
    const themeIconComponent = computed(() => {
      switch (currentTheme.value) {
        case "light":
          return Moon;
        // Показываем луну, так как следующая тема будет темная
        case "dark":
          return Sun;
        // Показываем солнце, так как следующая тема будет светлая
        case "system":
          return systemPrefersDark.value ? Sun : Moon;
        // Показываем противоположную системной
        default:
          return Monitor;
      }
    });
    const themes = [
      { value: "light", label: "\u0421\u0432\u0435\u0442\u043B\u0430\u044F", icon: Sun },
      { value: "dark", label: "\u0422\u0435\u043C\u043D\u0430\u044F", icon: Moon },
      { value: "system", label: "\u0421\u0438\u0441\u0442\u0435\u043C\u043D\u0430\u044F", icon: Monitor }
    ];
    const toggleMenu = () => {
      showMenu.value = !showMenu.value;
    };
    const selectTheme = (theme) => {
      setTheme(theme);
      showMenu.value = false;
    };
    const handleClickOutside = (event) => {
      const target = event.target;
      if (!target.closest(".theme-toggle")) {
        showMenu.value = false;
      }
    };
    onMounted(() => {
      document.addEventListener("click", handleClickOutside);
    });
    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
    });
    const __returned__ = { props, currentTheme, activeTheme, isDark, isLight, isSystem, themeIcon, themeName, systemPrefersDark, setTheme, toggleTheme, showMenu, themeIconComponent, themes, toggleMenu, selectTheme, handleClickOutside, get Check() {
      return Check;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "theme-toggle" }, _attrs))} data-v-94d58d16>`);
  if ($props.mode === "toggle") {
    _push(`<button class="${ssrRenderClass([$props.buttonClass, "p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"])}"${ssrRenderAttr("title", `\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0442\u0435\u043C\u0443 (\u0442\u0435\u043A\u0443\u0449\u0430\u044F: ${$setup.themeName})`)} data-v-94d58d16>`);
    ssrRenderVNode(_push, createVNode(resolveDynamicComponent($setup.themeIconComponent), { size: 20 }, null), _parent);
    if ($props.showLabel) {
      _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate($setup.themeName)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</button>`);
  } else if ($props.mode === "menu") {
    _push(`<div class="relative" data-v-94d58d16><button class="${ssrRenderClass([$props.buttonClass, "p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors flex items-center"])}"${ssrRenderAttr("title", `\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0442\u0435\u043C\u0443 (\u0442\u0435\u043A\u0443\u0449\u0430\u044F: ${$setup.themeName})`)} data-v-94d58d16>`);
    ssrRenderVNode(_push, createVNode(resolveDynamicComponent($setup.themeIconComponent), { size: 20 }, null), _parent);
    if ($props.showLabel) {
      _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate($setup.themeName)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</button>`);
    if ($setup.showMenu) {
      _push(`<div class="absolute right-0 mt-2 w-48 bg-[--color-card] rounded-[--radius-md] [box-shadow:var(--shadow-lg)] border border-[--color-border] z-50" data-v-94d58d16><div class="py-1" data-v-94d58d16><!--[-->`);
      ssrRenderList($setup.themes, (theme) => {
        _push(`<button class="${ssrRenderClass([{
          "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.currentTheme === theme.value
        }, "flex items-center w-full px-4 py-2 text-sm text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"])}" data-v-94d58d16>`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(theme.icon), {
          size: 16,
          class: "mr-3"
        }, null), _parent);
        _push(`<span data-v-94d58d16>${ssrInterpolate(theme.label)}</span>`);
        if ($setup.currentTheme === theme.value) {
          _push(ssrRenderComponent($setup["Check"], {
            size: 16,
            class: "ml-auto text-[--p-primary-color]"
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</button>`);
      });
      _push(`<!--]--></div></div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else if ($props.mode === "buttons") {
    _push(`<div class="flex rounded-[--radius-md] border border-[--color-border] overflow-hidden" data-v-94d58d16><!--[-->`);
    ssrRenderList($setup.themes, (theme) => {
      _push(`<button class="${ssrRenderClass([{
        "bg-[--color-primary] text-[--color-primary-foreground]": $setup.currentTheme === theme.value,
        "bg-[--color-card] text-[--color-foreground] hover:bg-[--p-content-hover-background]": $setup.currentTheme !== theme.value
      }, "flex items-center px-3 py-2 text-sm transition-colors border-r border-[--color-border] last:border-r-0"])}"${ssrRenderAttr("title", `\u0412\u044B\u0431\u0440\u0430\u0442\u044C ${theme.label.toLowerCase()} \u0442\u0435\u043C\u0443`)} data-v-94d58d16>`);
      ssrRenderVNode(_push, createVNode(resolveDynamicComponent(theme.icon), { size: 16 }, null), _parent);
      if ($props.showLabel) {
        _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate(theme.label)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</button>`);
    });
    _push(`<!--]--></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/ThemeToggle.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const ThemeToggle = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3], ["__scopeId", "data-v-94d58d16"]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "AdminToolbar",
  setup(__props, { expose: __expose }) {
    __expose();
    const showMobileMenu = ref(false);
    const currentPath = computed(() => {
      if (typeof window !== "undefined") {
        return window.location.pathname;
      }
      return "";
    });
    const navigateTo = (path) => {
      navigate();
      showMobileMenu.value = false;
    };
    const isActive = (path) => {
      if (path === "/admin") {
        return currentPath.value === "/admin";
      }
      return currentPath.value.startsWith(path);
    };
    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value;
    };
    const __returned__ = { showMobileMenu, currentPath, navigateTo, isActive, toggleMobileMenu, Toolbar, SecondaryButton, UserMenu, ThemeToggle, get Menu() {
      return Menu$1;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<!--[-->`);
  _push(ssrRenderComponent($setup["Toolbar"], { class: "bg-surface-0 dark:bg-surface-100 shadow-sm border-b border-surface-200 dark:border-surface-700 px-4 sm:px-6 lg:px-8" }, {
    start: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><img class="h-8 w-8" src="/favicon.svg" alt="PartTec"${_scopeId}></div><div class="ml-4"${_scopeId}><h1 class="text-xl font-semibold text-surface-900 dark:text-surface-50"${_scopeId}> PartTec Admin </h1></div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center" }, [
            createVNode("div", { class: "flex-shrink-0" }, [
              createVNode("img", {
                class: "h-8 w-8",
                src: "/favicon.svg",
                alt: "PartTec"
              })
            ]),
            createVNode("div", { class: "ml-4" }, [
              createVNode("h1", { class: "text-xl font-semibold text-surface-900 dark:text-surface-50" }, " PartTec Admin ")
            ])
          ])
        ];
      }
    }),
    center: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<nav class="hidden md:flex space-x-2"${_scopeId}></nav>`);
      } else {
        return [
          createVNode("nav", { class: "hidden md:flex space-x-2" })
        ];
      }
    }),
    end: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center space-x-4"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["ThemeToggle"], {
          mode: "menu",
          class: "hidden sm:block"
        }, null, _parent2, _scopeId));
        _push2(`<button class="md:hidden p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Menu"], { size: 20 }, null, _parent2, _scopeId));
        _push2(`</button>`);
        _push2(ssrRenderComponent($setup["UserMenu"], null, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center space-x-4" }, [
            createVNode($setup["ThemeToggle"], {
              mode: "menu",
              class: "hidden sm:block"
            }),
            createVNode("button", {
              onClick: $setup.toggleMobileMenu,
              class: "md:hidden p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"
            }, [
              createVNode($setup["Menu"], { size: 20 })
            ]),
            createVNode($setup["UserMenu"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($setup.showMobileMenu) {
    _push(`<div class="md:hidden bg-surface-0 dark:bg-surface-100 border-b border-surface-200 dark:border-surface-700 px-4 py-2"><nav class="space-y-1">`);
    _push(ssrRenderComponent($setup["SecondaryButton"], {
      label: "\u0413\u043B\u0430\u0432\u043D\u0430\u044F",
      text: "",
      class: "w-full justify-start",
      onClick: ($event) => $setup.navigateTo("/admin")
    }, null, _parent));
    _push(ssrRenderComponent($setup["SecondaryButton"], {
      label: "\u041A\u0430\u0442\u0430\u043B\u043E\u0433",
      text: "",
      class: "w-full justify-start",
      onClick: ($event) => $setup.navigateTo("/admin/catalog")
    }, null, _parent));
    _push(ssrRenderComponent($setup["SecondaryButton"], {
      label: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438",
      text: "",
      class: "w-full justify-start",
      onClick: ($event) => $setup.navigateTo("/admin/users")
    }, null, _parent));
    _push(ssrRenderComponent($setup["SecondaryButton"], {
      label: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",
      text: "",
      class: "w-full justify-start",
      onClick: ($event) => $setup.navigateTo("/admin/settings")
    }, null, _parent));
    _push(`<div class="border-t border-surface-200 dark:border-surface-700 my-2"></div><div class="px-2 py-1">`);
    _push(ssrRenderComponent($setup["ThemeToggle"], {
      mode: "buttons",
      "show-label": "",
      class: "w-full"
    }, null, _parent));
    _push(`</div></nav></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<!--]-->`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/AdminToolbar.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const AdminToolbar = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "AdminSidebar",
  setup(__props, { expose: __expose }) {
    __expose();
    const currentPath = computed(() => {
      if (typeof window !== "undefined") {
        return window.location.pathname;
      }
      return "";
    });
    const navigateTo = (path) => {
      navigate();
    };
    const isActive = (path) => {
      return currentPath.value.startsWith(path);
    };
    const isActiveExact = (path) => currentPath.value === path;
    const __returned__ = { currentPath, navigateTo, isActive, isActiveExact, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<aside${ssrRenderAttrs(mergeProps({ class: "bg-surface-section border-surface-border min-h-full w-64 border-r shadow-sm" }, _attrs))}><div class="p-6"><div class="mb-6"><h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">\u041E\u0441\u043D\u043E\u0432\u043D\u043E\u0435</h3><div class="space-y-1">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0414\u0430\u0448\u0431\u043E\u0440\u0434",
    text: "",
    "aria-current": $setup.isActiveExact("/admin") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActiveExact("/admin") },
    onClick: ($event) => $setup.navigateTo("/admin")
  }, null, _parent));
  _push(`</div></div><div class="mb-6"><h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">\u041A\u0430\u0442\u0430\u043B\u043E\u0433</h3><div class="flex flex-col items-start space-y-1">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0441\u0442\u0440\u0430\u043D\u0438\u0446",
    text: "",
    "aria-current": $setup.isActive("/admin/templates") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/templates") },
    onClick: ($event) => $setup.navigateTo("/admin/templates")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438",
    text: "",
    "aria-current": $setup.isActive("/admin/parts") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/parts") },
    onClick: ($event) => $setup.navigateTo("/admin/parts")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438",
    text: "",
    "aria-current": $setup.isActive("/admin/catalogitems") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/catalogitems") },
    onClick: ($event) => $setup.navigateTo("/admin/catalogitems")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041F\u043E\u0434\u0431\u043E\u0440 \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043E\u0432",
    text: "",
    "aria-current": $setup.isActive("/admin/proposals") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/proposals") },
    onClick: ($event) => $setup.navigateTo("/admin/proposals")
  }, null, _parent));
  _push(`</div></div><div class="mb-6"><h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">\u0421\u043F\u0440\u0430\u0432\u043E\u0447\u043D\u0438\u043A\u0438</h3><div class="flex flex-col items-start space-y-1">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B",
    text: "",
    "aria-current": $setup.isActive("/admin/attributes") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/attributes") },
    onClick: ($event) => $setup.navigateTo("/admin/attributes")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0411\u0440\u0435\u043D\u0434\u044B",
    text: "",
    "aria-current": $setup.isActive("/admin/brands") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/brands") },
    onClick: ($event) => $setup.navigateTo("/admin/brands")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
    text: "",
    "aria-current": $setup.isActive("/admin/categories") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/categories") },
    onClick: ($event) => $setup.navigateTo("/admin/categories")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438",
    text: "",
    "aria-current": $setup.isActive("/admin/equipment") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/equipment") },
    onClick: ($event) => $setup.navigateTo("/admin/equipment")
  }, null, _parent));
  _push(`</div></div><div><h3 class="text-surface-500 mb-3 text-xs font-semibold tracking-wider uppercase">\u0421\u0438\u0441\u0442\u0435\u043C\u0430</h3><div class="space-y-1">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0418\u043C\u043F\u043E\u0440\u0442 / \u042D\u043A\u0441\u043F\u043E\u0440\u0442",
    text: "",
    "aria-current": $setup.isActive("/admin/import-export") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/import-export") },
    onClick: ($event) => $setup.navigateTo("/admin/import-export")
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438",
    text: "",
    "aria-current": $setup.isActive("/admin/users") ? "page" : void 0,
    class: { "bg-primary-50 text-primary-600": $setup.isActive("/admin/users") },
    onClick: ($event) => $setup.navigateTo("/admin/users")
  }, null, _parent));
  _push(`</div></div></div></aside>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/AdminSidebar.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const AdminSidebar = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

function _typeof$4(o) { "@babel/helpers - typeof"; return _typeof$4 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$4(o); }
function _defineProperty$4(e, r, t) { return (r = _toPropertyKey$4(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$4(t) { var i = _toPrimitive$4(t, "string"); return "symbol" == _typeof$4(i) ? i : i + ""; }
function _toPrimitive$4(t, r) { if ("object" != _typeof$4(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$4(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

// Position
var inlineStyles = {
  root: function root(_ref) {
    var position = _ref.position;
    return {
      position: 'fixed',
      top: position === 'top-right' || position === 'top-left' || position === 'top-center' ? '20px' : position === 'center' ? '50%' : null,
      right: (position === 'top-right' || position === 'bottom-right') && '20px',
      bottom: (position === 'bottom-left' || position === 'bottom-right' || position === 'bottom-center') && '20px',
      left: position === 'top-left' || position === 'bottom-left' ? '20px' : position === 'center' || position === 'top-center' || position === 'bottom-center' ? '50%' : null
    };
  }
};
var classes = {
  root: function root(_ref2) {
    var props = _ref2.props;
    return ['p-toast p-component p-toast-' + props.position];
  },
  message: function message(_ref3) {
    var props = _ref3.props;
    return ['p-toast-message', {
      'p-toast-message-info': props.message.severity === 'info' || props.message.severity === undefined,
      'p-toast-message-warn': props.message.severity === 'warn',
      'p-toast-message-error': props.message.severity === 'error',
      'p-toast-message-success': props.message.severity === 'success',
      'p-toast-message-secondary': props.message.severity === 'secondary',
      'p-toast-message-contrast': props.message.severity === 'contrast'
    }];
  },
  messageContent: 'p-toast-message-content',
  messageIcon: function messageIcon(_ref4) {
    var props = _ref4.props;
    return ['p-toast-message-icon', _defineProperty$4(_defineProperty$4(_defineProperty$4(_defineProperty$4({}, props.infoIcon, props.message.severity === 'info'), props.warnIcon, props.message.severity === 'warn'), props.errorIcon, props.message.severity === 'error'), props.successIcon, props.message.severity === 'success')];
  },
  messageText: 'p-toast-message-text',
  summary: 'p-toast-summary',
  detail: 'p-toast-detail',
  closeButton: 'p-toast-close-button',
  closeIcon: 'p-toast-close-icon'
};
var ToastStyle = BaseStyle.extend({
  name: 'toast',
  style: style$3,
  classes: classes,
  inlineStyles: inlineStyles
});

var script$2 = {
  name: 'BaseToast',
  "extends": BaseComponent,
  props: {
    group: {
      type: String,
      "default": null
    },
    position: {
      type: String,
      "default": 'top-right'
    },
    autoZIndex: {
      type: Boolean,
      "default": true
    },
    baseZIndex: {
      type: Number,
      "default": 0
    },
    breakpoints: {
      type: Object,
      "default": null
    },
    closeIcon: {
      type: String,
      "default": undefined
    },
    infoIcon: {
      type: String,
      "default": undefined
    },
    warnIcon: {
      type: String,
      "default": undefined
    },
    errorIcon: {
      type: String,
      "default": undefined
    },
    successIcon: {
      type: String,
      "default": undefined
    },
    closeButtonProps: {
      type: null,
      "default": null
    },
    onMouseEnter: {
      type: Function,
      "default": undefined
    },
    onMouseLeave: {
      type: Function,
      "default": undefined
    },
    onClick: {
      type: Function,
      "default": undefined
    }
  },
  style: ToastStyle,
  provide: function provide() {
    return {
      $pcToast: this,
      $parentInstance: this
    };
  }
};

function _typeof$3(o) { "@babel/helpers - typeof"; return _typeof$3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$3(o); }
function _defineProperty$3(e, r, t) { return (r = _toPropertyKey$3(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$3(t) { var i = _toPrimitive$3(t, "string"); return "symbol" == _typeof$3(i) ? i : i + ""; }
function _toPrimitive$3(t, r) { if ("object" != _typeof$3(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$3(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var script$1 = {
  name: 'ToastMessage',
  hostName: 'Toast',
  "extends": BaseComponent,
  emits: ['close'],
  closeTimeout: null,
  createdAt: null,
  lifeRemaining: null,
  props: {
    message: {
      type: null,
      "default": null
    },
    templates: {
      type: Object,
      "default": null
    },
    closeIcon: {
      type: String,
      "default": null
    },
    infoIcon: {
      type: String,
      "default": null
    },
    warnIcon: {
      type: String,
      "default": null
    },
    errorIcon: {
      type: String,
      "default": null
    },
    successIcon: {
      type: String,
      "default": null
    },
    closeButtonProps: {
      type: null,
      "default": null
    },
    onMouseEnter: {
      type: Function,
      "default": undefined
    },
    onMouseLeave: {
      type: Function,
      "default": undefined
    },
    onClick: {
      type: Function,
      "default": undefined
    }
  },
  mounted: function mounted() {
    if (this.message.life) {
      this.lifeRemaining = this.message.life;
      this.startTimeout();
    }
  },
  beforeUnmount: function beforeUnmount() {
    this.clearCloseTimeout();
  },
  methods: {
    startTimeout: function startTimeout() {
      var _this = this;
      this.createdAt = new Date().valueOf();
      this.closeTimeout = setTimeout(function () {
        _this.close({
          message: _this.message,
          type: 'life-end'
        });
      }, this.lifeRemaining);
    },
    close: function close(params) {
      this.$emit('close', params);
    },
    onCloseClick: function onCloseClick() {
      this.clearCloseTimeout();
      this.close({
        message: this.message,
        type: 'close'
      });
    },
    clearCloseTimeout: function clearCloseTimeout() {
      if (this.closeTimeout) {
        clearTimeout(this.closeTimeout);
        this.closeTimeout = null;
      }
    },
    onMessageClick: function onMessageClick(event) {
      var _this$onClick;
      (_this$onClick = this.onClick) === null || _this$onClick === void 0 || _this$onClick.call(this, {
        originalEvent: event,
        message: this.message
      });
    },
    handleMouseEnter: function handleMouseEnter(event) {
      if (this.onMouseEnter) {
        this.onMouseEnter({
          originalEvent: event,
          message: this.message
        });
        if (event.defaultPrevented) {
          return;
        }
        if (this.message.life) {
          this.lifeRemaining = this.createdAt + this.lifeRemaining - new Date().valueOf();
          this.createdAt = null;
          this.clearCloseTimeout();
        }
      }
    },
    handleMouseLeave: function handleMouseLeave(event) {
      if (this.onMouseLeave) {
        this.onMouseLeave({
          originalEvent: event,
          message: this.message
        });
        if (event.defaultPrevented) {
          return;
        }
        if (this.message.life) {
          this.startTimeout();
        }
      }
    }
  },
  computed: {
    iconComponent: function iconComponent() {
      return {
        info: !this.infoIcon && InfoCircleIcon,
        success: !this.successIcon && CheckIcon,
        warn: !this.warnIcon && ExclamationTriangleIcon,
        error: !this.errorIcon && TimesCircleIcon
      }[this.message.severity];
    },
    closeAriaLabel: function closeAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.close : undefined;
    },
    dataP: function dataP() {
      return cn(_defineProperty$3({}, this.message.severity, this.message.severity));
    }
  },
  components: {
    TimesIcon: TimesIcon,
    InfoCircleIcon: InfoCircleIcon,
    CheckIcon: CheckIcon,
    ExclamationTriangleIcon: ExclamationTriangleIcon,
    TimesCircleIcon: TimesCircleIcon
  },
  directives: {
    ripple: Ripple
  }
};

function _typeof$2(o) { "@babel/helpers - typeof"; return _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$2(o); }
function ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), true).forEach(function (r) { _defineProperty$2(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty$2(e, r, t) { return (r = _toPropertyKey$2(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$2(t) { var i = _toPrimitive$2(t, "string"); return "symbol" == _typeof$2(i) ? i : i + ""; }
function _toPrimitive$2(t, r) { if ("object" != _typeof$2(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$2(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _hoisted_1$1 = ["data-p"];
var _hoisted_2 = ["data-p"];
var _hoisted_3 = ["data-p"];
var _hoisted_4 = ["data-p"];
var _hoisted_5 = ["aria-label", "data-p"];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": [_ctx.cx('message'), $props.message.styleClass],
    role: "alert",
    "aria-live": "assertive",
    "aria-atomic": "true",
    "data-p": $options.dataP
  }, _ctx.ptm('message'), {
    onClick: _cache[1] || (_cache[1] = function () {
      return $options.onMessageClick && $options.onMessageClick.apply($options, arguments);
    }),
    onMouseenter: _cache[2] || (_cache[2] = function () {
      return $options.handleMouseEnter && $options.handleMouseEnter.apply($options, arguments);
    }),
    onMouseleave: _cache[3] || (_cache[3] = function () {
      return $options.handleMouseLeave && $options.handleMouseLeave.apply($options, arguments);
    })
  }), [$props.templates.container ? (openBlock(), createBlock(resolveDynamicComponent($props.templates.container), {
    key: 0,
    message: $props.message,
    closeCallback: $options.onCloseClick
  }, null, 8, ["message", "closeCallback"])) : (openBlock(), createElementBlock("div", mergeProps({
    key: 1,
    "class": [_ctx.cx('messageContent'), $props.message.contentStyleClass]
  }, _ctx.ptm('messageContent')), [!$props.templates.message ? (openBlock(), createElementBlock(Fragment, {
    key: 0
  }, [(openBlock(), createBlock(resolveDynamicComponent($props.templates.messageicon ? $props.templates.messageicon : $props.templates.icon ? $props.templates.icon : $options.iconComponent && $options.iconComponent.name ? $options.iconComponent : 'span'), mergeProps({
    "class": _ctx.cx('messageIcon')
  }, _ctx.ptm('messageIcon')), null, 16, ["class"])), createElementVNode("div", mergeProps({
    "class": _ctx.cx('messageText'),
    "data-p": $options.dataP
  }, _ctx.ptm('messageText')), [createElementVNode("span", mergeProps({
    "class": _ctx.cx('summary'),
    "data-p": $options.dataP
  }, _ctx.ptm('summary')), toDisplayString($props.message.summary), 17, _hoisted_3), $props.message.detail ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx('detail'),
    "data-p": $options.dataP
  }, _ctx.ptm('detail')), toDisplayString($props.message.detail), 17, _hoisted_4)) : createCommentVNode("", true)], 16, _hoisted_2)], 64)) : (openBlock(), createBlock(resolveDynamicComponent($props.templates.message), {
    key: 1,
    message: $props.message
  }, null, 8, ["message"])), $props.message.closable !== false ? (openBlock(), createElementBlock("div", normalizeProps(mergeProps({
    key: 2
  }, _ctx.ptm('buttonContainer'))), [withDirectives((openBlock(), createElementBlock("button", mergeProps({
    "class": _ctx.cx('closeButton'),
    type: "button",
    "aria-label": $options.closeAriaLabel,
    onClick: _cache[0] || (_cache[0] = function () {
      return $options.onCloseClick && $options.onCloseClick.apply($options, arguments);
    }),
    autofocus: "",
    "data-p": $options.dataP
  }, _objectSpread$1(_objectSpread$1({}, $props.closeButtonProps), _ctx.ptm('closeButton'))), [(openBlock(), createBlock(resolveDynamicComponent($props.templates.closeicon || 'TimesIcon'), mergeProps({
    "class": [_ctx.cx('closeIcon'), $props.closeIcon]
  }, _ctx.ptm('closeIcon')), null, 16, ["class"]))], 16, _hoisted_5)), [[_directive_ripple]])], 16)) : createCommentVNode("", true)], 16))], 16, _hoisted_1$1);
}

script$1.render = render$1;

function _typeof$1(o) { "@babel/helpers - typeof"; return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$1(o); }
function _defineProperty$1(e, r, t) { return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$1(t) { var i = _toPrimitive$1(t, "string"); return "symbol" == _typeof$1(i) ? i : i + ""; }
function _toPrimitive$1(t, r) { if ("object" != _typeof$1(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$1(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var messageIdx = 0;
var script = {
  name: 'Toast',
  "extends": script$2,
  inheritAttrs: false,
  emits: ['close', 'life-end'],
  data: function data() {
    return {
      messages: []
    };
  },
  styleElement: null,
  mounted: function mounted() {
    ToastEventBus.on('add', this.onAdd);
    ToastEventBus.on('remove', this.onRemove);
    ToastEventBus.on('remove-group', this.onRemoveGroup);
    ToastEventBus.on('remove-all-groups', this.onRemoveAllGroups);
    if (this.breakpoints) {
      this.createStyle();
    }
  },
  beforeUnmount: function beforeUnmount() {
    this.destroyStyle();
    if (this.$refs.container && this.autoZIndex) {
      ZIndex.clear(this.$refs.container);
    }
    ToastEventBus.off('add', this.onAdd);
    ToastEventBus.off('remove', this.onRemove);
    ToastEventBus.off('remove-group', this.onRemoveGroup);
    ToastEventBus.off('remove-all-groups', this.onRemoveAllGroups);
  },
  methods: {
    add: function add(message) {
      if (message.id == null) {
        message.id = messageIdx++;
      }
      this.messages = [].concat(_toConsumableArray(this.messages), [message]);
    },
    remove: function remove(params) {
      var index = this.messages.findIndex(function (m) {
        return m.id === params.message.id;
      });
      if (index !== -1) {
        this.messages.splice(index, 1);
        this.$emit(params.type, {
          message: params.message
        });
      }
    },
    onAdd: function onAdd(message) {
      if (this.group == message.group) {
        this.add(message);
      }
    },
    onRemove: function onRemove(message) {
      this.remove({
        message: message,
        type: 'close'
      });
    },
    onRemoveGroup: function onRemoveGroup(group) {
      if (this.group === group) {
        this.messages = [];
      }
    },
    onRemoveAllGroups: function onRemoveAllGroups() {
      var _this = this;
      this.messages.forEach(function (message) {
        return _this.$emit('close', {
          message: message
        });
      });
      this.messages = [];
    },
    onEnter: function onEnter() {
      if (this.autoZIndex) {
        ZIndex.set('modal', this.$refs.container, this.baseZIndex || this.$primevue.config.zIndex.modal);
      }
    },
    onLeave: function onLeave() {
      var _this2 = this;
      if (this.$refs.container && this.autoZIndex && isEmpty(this.messages)) {
        setTimeout(function () {
          ZIndex.clear(_this2.$refs.container);
        }, 200);
      }
    },
    createStyle: function createStyle() {
      if (!this.styleElement && !this.isUnstyled) {
        var _this$$primevue;
        this.styleElement = document.createElement('style');
        this.styleElement.type = 'text/css';
        setAttribute(this.styleElement, 'nonce', (_this$$primevue = this.$primevue) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.config) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.csp) === null || _this$$primevue === void 0 ? void 0 : _this$$primevue.nonce);
        document.head.appendChild(this.styleElement);
        var innerHTML = '';
        for (var breakpoint in this.breakpoints) {
          var breakpointStyle = '';
          for (var styleProp in this.breakpoints[breakpoint]) {
            breakpointStyle += styleProp + ':' + this.breakpoints[breakpoint][styleProp] + '!important;';
          }
          innerHTML += "\n                        @media screen and (max-width: ".concat(breakpoint, ") {\n                            .p-toast[").concat(this.$attrSelector, "] {\n                                ").concat(breakpointStyle, "\n                            }\n                        }\n                    ");
        }
        this.styleElement.innerHTML = innerHTML;
      }
    },
    destroyStyle: function destroyStyle() {
      if (this.styleElement) {
        document.head.removeChild(this.styleElement);
        this.styleElement = null;
      }
    }
  },
  computed: {
    dataP: function dataP() {
      return cn(_defineProperty$1({}, this.position, this.position));
    }
  },
  components: {
    ToastMessage: script$1,
    Portal: script$7
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), true).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _hoisted_1 = ["data-p"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_ToastMessage = resolveComponent("ToastMessage");
  var _component_Portal = resolveComponent("Portal");
  return openBlock(), createBlock(_component_Portal, null, {
    "default": withCtx(function () {
      return [createElementVNode("div", mergeProps({
        ref: "container",
        "class": _ctx.cx('root'),
        style: _ctx.sx('root', true, {
          position: _ctx.position
        }),
        "data-p": $options.dataP
      }, _ctx.ptmi('root')), [createVNode(TransitionGroup, mergeProps({
        name: "p-toast-message",
        tag: "div",
        onEnter: $options.onEnter,
        onLeave: $options.onLeave
      }, _objectSpread({}, _ctx.ptm('transition'))), {
        "default": withCtx(function () {
          return [(openBlock(true), createElementBlock(Fragment, null, renderList($data.messages, function (msg) {
            return openBlock(), createBlock(_component_ToastMessage, {
              key: msg.id,
              message: msg,
              templates: _ctx.$slots,
              closeIcon: _ctx.closeIcon,
              infoIcon: _ctx.infoIcon,
              warnIcon: _ctx.warnIcon,
              errorIcon: _ctx.errorIcon,
              successIcon: _ctx.successIcon,
              closeButtonProps: _ctx.closeButtonProps,
              onMouseEnter: _ctx.onMouseEnter,
              onMouseLeave: _ctx.onMouseLeave,
              onClick: _ctx.onClick,
              unstyled: _ctx.unstyled,
              onClose: _cache[0] || (_cache[0] = function ($event) {
                return $options.remove($event);
              }),
              pt: _ctx.pt
            }, null, 8, ["message", "templates", "closeIcon", "infoIcon", "warnIcon", "errorIcon", "successIcon", "closeButtonProps", "onMouseEnter", "onMouseLeave", "onClick", "unstyled", "pt"]);
          }), 128))];
        }),
        _: 1
      }, 16, ["onEnter", "onLeave"])], 16, _hoisted_1)];
    }),
    _: 1
  });
}

script.render = render;

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Toast",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `w-96 rounded-md whitespace-pre-line break-words
        p-top-center:-translate-x-1/2 p-bottom-center:-translate-x-1/2
        p-center:min-w-[20vw] p-center:-translate-x-1/2 p-center:-translate-y-1/2`,
      message: `mb-4 not-p-custom:border not-p-custom:backdrop-blur-sm dark:not-p-custom:backdrop-blur-md not-p-custom:rounded-md
        p-info:bg-blue-50/95 p-info:border-blue-200 p-info:text-blue-600 dark:p-info:bg-blue-500/15 dark:p-info:border-blue-700/35 dark:p-info:text-blue-500
        p-success:bg-green-50/95 p-success:border-green-200 p-success:text-green-600 dark:p-success:bg-green-500/15 dark:p-success:border-green-700/35 dark:p-success:text-green-500
        p-warn:bg-yellow-50/95 p-warn:border-yellow-200 p-warn:text-yellow-600 dark:p-warn:bg-yellow-500/15 dark:p-warn:border-yellow-700/35 dark:p-warn:text-yellow-500
        p-error:bg-red-50/95 p-error:border-red-200 p-error:text-red-600 dark:p-error:bg-red-500/15 dark:p-error:border-red-700/35 dark:p-error:text-red-500
        p-secondary:bg-surface-100 p-secondary:border-surface-200 p-secondary:text-surface-600 dark:p-secondary:bg-surface-800 dark:p-secondary:border-surface-700 dark:p-secondary:text-surface-300
        p-contrast:bg-surface-900 p-contrast:border-surface-950 p-contrast:text-surface-50 dark:p-contrast:bg-surface-0 dark:p-contrast:border-surface-100 dark:p-contrast:text-surface-950`,
      messageContent: `flex items-start p-3 gap-2`,
      messageIcon: `flex-shrink-0 text-lg w-[1.125rem] h-[1.125rem] mt-1`,
      messageText: `flex-auto flex flex-col gap-2`,
      summary: `font-medium text-base`,
      detail: `font-medium text-sm text-surface-700 dark:text-surface-0
        p-contrast:text-surface-0 dark:p-contrast:text-surface-950`,
      buttonContainer: ``,
      closeButton: `flex items-center justify-center overflow-hidden relative cursor-pointer bg-transparent select-none
        transition-colors duration-200 text-inherit w-7 h-7 rounded-full -mt-[25%] -end-1/4 p-0 border-none
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2
        p-info:hover:bg-blue-100 p-info:focus-visible:outline-blue-600 dark:p-info:hover:bg-white/5 dark:p-info:focus-visible:outline-blue-500
        p-success:hover:bg-green-100 p-success:focus-visible:outline-green-600 dark:p-success:hover:bg-white/5 dark:p-success:focus-visible:outline-green-500
        p-warn:hover:bg-yellow-100 p-warn:focus-visible:outline-yellow-600 dark:p-warn:hover:bg-white/5 dark:p-warn:focus-visible:outline-yellow-500
        p-error:hover:bg-red-100 p-error:focus-visible:outline-red-600 dark:p-error:hover:bg-white/5 dark:p-error:focus-visible:outline-red-500
        p-secondary:hover:bg-surface-200 p-secondary:focus-visible:outline-surface-600 dark:p-secondary:hover:bg-surface-700 dark:p-secondary:focus-visible:outline-surface-300
        p-contrast:hover:bg-surface-800 p-contrast:focus-visible:outline-surface-50 dark:p-contrast:hover:bg-surface-100 dark:p-contrast:focus-visible:outline-surface-950`,
      closeIcon: `text-base w-4 h-4`,
      transition: {
        enterFromClass: "opacity-0 translate-y-1/2",
        enterActiveClass: "transition-all duration-500",
        leaveFromClass: "max-h-[1000px]",
        leaveActiveClass: "transition-all duration-500",
        leaveToClass: "max-h-0 opacity-0 mb-0 overflow-hidden"
      }
    });
    const toast = useToast();
    function handleGlobalToast(e) {
      const detail = e.detail;
      if (!detail) return;
      toast.add({
        severity: detail.severity,
        summary: detail.summary,
        detail: detail.detail,
        life: detail.life ?? 4e3
      });
    }
    onMounted(() => {
      if (typeof window !== "undefined") {
        window.addEventListener("app:toast", handleGlobalToast);
      }
    });
    onBeforeUnmount(() => {
      if (typeof window !== "undefined") {
        window.removeEventListener("app:toast", handleGlobalToast);
      }
    });
    const __returned__ = { theme, toast, handleGlobalToast, get TimesIcon() {
      return TimesIcon;
    }, get Toast() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Toast"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({
    closeicon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["TimesIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["TimesIcon"])
        ];
      }
    }),
    _: 2
  }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Toast.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Toast = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Astro = createAstro();
const $$AdminLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$AdminLayout;
  const {
    title = "\u0410\u0434\u043C\u0438\u043D \u043F\u0430\u043D\u0435\u043B\u044C - PartTec",
    description = "\u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439",
    showSidebar = true
  } = Astro2.props;
  return renderTemplate(_a || (_a = __template(['<html lang="ru" class="h-full"> <head><meta charset="UTF-8"><meta name="description"', '><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg">', "<title>", '</title><!-- \u0421\u043A\u0440\u0438\u043F\u0442 \u0434\u043B\u044F \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0435\u043D\u0438\u044F \u043C\u0438\u0433\u0430\u043D\u0438\u044F \u0442\u0435\u043C\u044B --><script src="/theme-init.js"><\/script>', '</head> <body class="h-full"> <div class="min-h-screen bg-surface-50 dark:bg-surface-900"> <!-- Header \u0441 Volt Toolbar --> ', ' <div class="flex bg-surface-50 dark:bg-surface-900 min-h-[calc(100vh-4rem)]"> <!-- Sidebar \u0441 Volt \u043A\u043E\u043C\u043F\u043E\u043D\u0435\u043D\u0442\u0430\u043C\u0438 --> ', ' <!-- \u041E\u0441\u043D\u043E\u0432\u043D\u043E\u0439 \u043A\u043E\u043D\u0442\u0435\u043D\u0442 --> <main class="flex-1 bg-surface-50 dark:bg-surface-900"> <div class="py-6"> <div class="mx-auto flex justify-center py-2 px-5"> ', " </div> </div> </main> </div>  ", " <!-- <GlobalErrorHandlerInit client:load /> --> </div> </body></html>"])), addAttribute(description, "content"), renderComponent($$result, "ClientRouter", $$ClientRouter, {}), title, renderHead(), renderComponent($$result, "AdminToolbar", AdminToolbar, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/parttec/cpanel/src/components/admin/AdminToolbar.vue", "client:component-export": "default" }), showSidebar && renderTemplate`${renderComponent($$result, "AdminSidebar", AdminSidebar, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/parttec/cpanel/src/components/admin/AdminSidebar.vue", "client:component-export": "default" })}`, renderSlot$1($$result, $$slots["default"]), renderComponent($$result, "Toast", Toast, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/volt/Toast.vue", "client:component-export": "default" }));
}, "D:/Dev/parttec/cpanel/src/layouts/AdminLayout.astro", void 0);

export { $$AdminLayout as $, Icon as I, Menu as M, SecondaryButton as S, Toast as T, ThemeToggle as a };
