import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { createElementBlock, openBlock, mergeProps, renderSlot, defineComponent, useSSRContext, ref, withCtx, Fragment, createCommentVNode, withDirectives, createBlock, resolveDynamicComponent, vShow, normalizeClass, onMounted, createTextVNode, createVNode, toDisplayString, renderList } from 'vue';
import { u as useTrpc } from '../../chunks/useTrpc_CFYBp3yo.mjs';
import { V as VCard } from '../../chunks/Card_gUaTQQTl.mjs';
import { V as VCheckbox } from '../../chunks/Checkbox_sKnCOH0n.mjs';
import { I as InputText } from '../../chunks/InputText_DBferNa6.mjs';
import { V as VButton } from '../../chunks/Button_CVFNKcZL.mjs';
import { S as Select } from '../../chunks/Select_CSb2vMfa.mjs';
import { V as VTab, a as VTabList, b as VTabs } from '../../chunks/Tab_Bmc9q_sV.mjs';
import BaseComponent from '@primevue/core/basecomponent';
import BaseStyle from '@primevue/core/base/style';
import { p as ptViewMerge, _ as _export_sfc } from '../../chunks/ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent, ssrRenderSlot, ssrInterpolate, ssrRenderList, ssrRenderAttrs } from 'vue/server-renderer';
import { equals } from '@primeuix/utils/object';
import { D as DangerButton } from '../../chunks/DangerButton_Ufuya2W4.mjs';
import { I as Icon, $ as $$AdminLayout } from '../../chunks/AdminLayout_DWqIyijm.mjs';
import { M as MultiSelect } from '../../chunks/MultiSelect_BoRjnXk1.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

var classes$1 = {
  root: 'p-tabpanels'
};
var TabPanelsStyle = BaseStyle.extend({
  name: 'tabpanels',
  classes: classes$1
});

var script$1$1 = {
  name: 'BaseTabPanels',
  "extends": BaseComponent,
  props: {},
  style: TabPanelsStyle,
  provide: function provide() {
    return {
      $pcTabPanels: this,
      $parentInstance: this
    };
  }
};

var script$2 = {
  name: 'TabPanels',
  "extends": script$1$1,
  inheritAttrs: false
};

function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    role: "presentation"
  }, _ctx.ptmi('root')), [renderSlot(_ctx.$slots, "default")], 16);
}

script$2.render = render$1;

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "TabPanels",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `bg-surface-0 dark:bg-surface-900 text-surface-700 dark:text-surface-0
        pt-[0.875rem] pb-[1.125rem] px-[1.125rem] outline-none`
    });
    const __returned__ = { theme, get TabPanels() {
      return script$2;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["TabPanels"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        ssrRenderSlot(_ctx.$slots, "default", {}, null, _push2, _parent2, _scopeId);
      } else {
        return [
          renderSlot(_ctx.$slots, "default")
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/TabPanels.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const VTabPanels = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3]]);

var classes = {
  root: function root(_ref) {
    var instance = _ref.instance;
    return ['p-tabpanel', {
      'p-tabpanel-active': instance.active
    }];
  }
};
var TabPanelStyle = BaseStyle.extend({
  name: 'tabpanel',
  classes: classes
});

var script$1 = {
  name: 'BaseTabPanel',
  "extends": BaseComponent,
  props: {
    // in Tabs
    value: {
      type: [String, Number],
      "default": undefined
    },
    as: {
      type: [String, Object],
      "default": 'DIV'
    },
    asChild: {
      type: Boolean,
      "default": false
    },
    // in TabView
    header: null,
    headerStyle: null,
    headerClass: null,
    headerProps: null,
    headerActionProps: null,
    contentStyle: null,
    contentClass: null,
    contentProps: null,
    disabled: Boolean
  },
  style: TabPanelStyle,
  provide: function provide() {
    return {
      $pcTabPanel: this,
      $parentInstance: this
    };
  }
};

var script = {
  name: 'TabPanel',
  "extends": script$1,
  inheritAttrs: false,
  inject: ['$pcTabs'],
  computed: {
    active: function active() {
      var _this$$pcTabs;
      return equals((_this$$pcTabs = this.$pcTabs) === null || _this$$pcTabs === void 0 ? void 0 : _this$$pcTabs.d_value, this.value);
    },
    id: function id() {
      var _this$$pcTabs2;
      return "".concat((_this$$pcTabs2 = this.$pcTabs) === null || _this$$pcTabs2 === void 0 ? void 0 : _this$$pcTabs2.$id, "_tabpanel_").concat(this.value);
    },
    ariaLabelledby: function ariaLabelledby() {
      var _this$$pcTabs3;
      return "".concat((_this$$pcTabs3 = this.$pcTabs) === null || _this$$pcTabs3 === void 0 ? void 0 : _this$$pcTabs3.$id, "_tab_").concat(this.value);
    },
    attrs: function attrs() {
      return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));
    },
    a11yAttrs: function a11yAttrs() {
      var _this$$pcTabs4;
      return {
        id: this.id,
        tabindex: (_this$$pcTabs4 = this.$pcTabs) === null || _this$$pcTabs4 === void 0 ? void 0 : _this$$pcTabs4.tabindex,
        role: 'tabpanel',
        'aria-labelledby': this.ariaLabelledby,
        'data-pc-name': 'tabpanel',
        'data-p-active': this.active
      };
    },
    ptParams: function ptParams() {
      return {
        context: {
          active: this.active
        }
      };
    }
  }
};

function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _$options$$pcTabs, _$options$$pcTabs2;
  return !$options.$pcTabs ? renderSlot(_ctx.$slots, "default", {
    key: 0
  }) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [!_ctx.asChild ? (openBlock(), createElementBlock(Fragment, {
    key: 0
  }, [((_$options$$pcTabs = $options.$pcTabs) !== null && _$options$$pcTabs !== void 0 && _$options$$pcTabs.lazy ? $options.active : true) ? withDirectives((openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
    key: 0,
    "class": _ctx.cx('root')
  }, $options.attrs), {
    "default": withCtx(function () {
      return [renderSlot(_ctx.$slots, "default")];
    }),
    _: 3
  }, 16, ["class"])), [[vShow, (_$options$$pcTabs2 = $options.$pcTabs) !== null && _$options$$pcTabs2 !== void 0 && _$options$$pcTabs2.lazy ? true : $options.active]]) : createCommentVNode("", true)], 64)) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    "class": normalizeClass(_ctx.cx('root')),
    active: $options.active,
    a11yAttrs: $options.a11yAttrs
  })], 64));
}

script.render = render;

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "TabPanel",
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const theme = ref({
      root: ``
    });
    const __returned__ = { props, theme, get TabPanel() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["TabPanel"], mergeProps({
    value: $setup.props.value,
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        ssrRenderSlot(_ctx.$slots, "default", {}, null, _push2, _parent2, _scopeId);
      } else {
        return [
          renderSlot(_ctx.$slots, "default")
        ];
      }
    }),
    _: 3
  }, _parent));
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/TabPanel.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const VTabPanel = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SimplifiedImportPanel",
  setup(__props, { expose: __expose }) {
    __expose();
    const { importExport, attributeTemplates } = useTrpc();
    const file = ref(null);
    const loading = ref(false);
    const loadingTemplate = ref(false);
    const loadingTemplates = ref(false);
    const currentAction = ref(null);
    const result = ref(null);
    const activeTabIndex = ref(0);
    const importOptions = ref({
      createMissingRefs: true,
      onConflict: "upsert",
      autoLinkToParts: false,
      createMissingTemplates: true
    });
    const templateOptions = ref({
      includeSampleData: true,
      categorySlug: "",
      brandSlug: ""
    });
    const selectedTemplates = ref([]);
    const availableTemplates = ref([]);
    const universalInclude = ref({
      Brand: true,
      PartCategory: true,
      AttributeGroup: true,
      AttributeTemplate: true,
      AttributeSynonymGroup: false,
      AttributeSynonym: false,
      Part: true,
      CatalogItem: true,
      EquipmentModel: false,
      PartAttribute: true,
      CatalogItemAttribute: true,
      EquipmentModelAttribute: false,
      PartApplicability: true,
      EquipmentApplicability: false
    });
    const conflictOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0438\u043B\u0438 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (upsert)", value: "upsert" },
      { label: "\u0422\u043E\u043B\u044C\u043A\u043E \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (update_only)", value: "update_only" },
      { label: "\u041F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u044C (skip)", value: "skip" },
      { label: "\u041E\u0448\u0438\u0431\u043A\u0430 (error)", value: "error" }
    ];
    onMounted(async () => {
      activeTabIndex.value = 0;
      await loadAttributeTemplates();
    });
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const clearFile = () => {
      file.value = null;
      result.value = null;
    };
    const loadAttributeTemplates = async () => {
      loadingTemplates.value = true;
      try {
        const res = await attributeTemplates.findMany({ limit: 1e3 });
        if (res && res.templates) {
          availableTemplates.value = res.templates.map((t) => ({
            label: `${t.title} (${t.name})`,
            value: t.name
          }));
        }
      } catch (error) {
        console.error("Failed to load attribute templates:", error);
      } finally {
        loadingTemplates.value = false;
      }
    };
    const onCreateTemplate = async () => {
      loadingTemplate.value = true;
      try {
        if (activeTabIndex.value === 0) {
          const input = {
            includeSampleData: templateOptions.value.includeSampleData,
            attributeTemplates: selectedTemplates.value.length > 0 ? selectedTemplates.value : void 0,
            categorySlug: templateOptions.value.categorySlug || void 0,
            brandSlug: templateOptions.value.brandSlug || void 0
          };
          const res2 = await importExport.createSimplifiedTemplate(input);
          if (!res2) return;
          const blob2 = b64toBlob(res2.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
          const url2 = URL.createObjectURL(blob2);
          const a2 = document.createElement("a");
          a2.href = url2;
          a2.download = res2.fileName;
          a2.click();
          URL.revokeObjectURL(url2);
          return;
        }
        const res = await importExport.exportTemplate({ include: universalInclude.value });
        if (!res) return;
        const blob = b64toBlob(res.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = res.fileName;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Create template failed:", error);
      } finally {
        loadingTemplate.value = false;
      }
    };
    const onFileChange = (e) => {
      const target = e.target;
      file.value = target.files?.[0] ?? null;
      result.value = null;
    };
    function fileToBase64(file2) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(String(reader.result).split(",")[1] || "");
        reader.onerror = reject;
        reader.readAsDataURL(file2);
      });
    }
    const onValidate = async () => {
      if (!file.value) return;
      currentAction.value = "validate";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await importExport.validateSimplifiedImport({
          base64,
          options: importOptions.value
        });
        result.value = res;
      } catch (error) {
        console.error("Validation failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    const onExecute = async () => {
      if (!file.value) return;
      currentAction.value = "execute";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await importExport.simplifiedImport({
          base64,
          options: importOptions.value
        });
        if (!res) return;
        result.value = res;
        if (res.reportBase64) downloadReport(res.reportBase64);
      } catch (error) {
        console.error("Execute failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    function downloadReport(base64) {
      const blob = b64toBlob(base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `simplified-import-report-${Date.now()}.xlsx`;
      a.click();
      URL.revokeObjectURL(url);
    }
    function b64toBlob(base64, contentType = "", sliceSize = 512) {
      const byteCharacters = atob(base64);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
      const blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }
    const __returned__ = { importExport, attributeTemplates, file, loading, loadingTemplate, loadingTemplates, currentAction, result, activeTabIndex, importOptions, templateOptions, selectedTemplates, availableTemplates, universalInclude, conflictOptions, formatFileSize, clearFile, loadAttributeTemplates, onCreateTemplate, onFileChange, fileToBase64, onValidate, onExecute, downloadReport, b64toBlob, VCard, VCheckbox, VInputText: InputText, VButton, VSelect: Select, VMultiSelect: MultiSelect, DangerButton, Icon, VTabs, VTabList, VTab, VTabPanels, VTabPanel };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VCard"], mergeProps({ class: "p-6" }, _attrs), {
    title: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center gap-2 mb-4"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "file-spreadsheet",
          class: "w-5 h-5 text-primary-600"
        }, null, _parent2, _scopeId));
        _push2(`<h2 class="text-lg font-semibold"${_scopeId}>\u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430</h2></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center gap-2 mb-4" }, [
            createVNode($setup["Icon"], {
              name: "file-spreadsheet",
              class: "w-5 h-5 text-primary-600"
            }),
            createVNode("h2", { class: "text-lg font-semibold" }, "\u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-6"${_scopeId}><div class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"${_scopeId}><div class="flex items-start gap-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "info",
          class: "w-5 h-5 text-blue-600 mt-0.5"
        }, null, _parent2, _scopeId));
        _push2(`<div${_scopeId}><h3 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1"${_scopeId}> \u0424\u043E\u0440\u043C\u0430\u0442\u044B \u0438\u043C\u043F\u043E\u0440\u0442\u0430 </h3><p class="text-sm text-blue-700 dark:text-blue-200"${_scopeId}> 1) \u041F\u0440\u043E\u0441\u0442\u043E\u0439: \u043E\u0434\u0438\u043D \u043B\u0438\u0441\u0442 &quot;CatalogItems&quot;. \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u2014 \u043A\u043E\u043B\u043E\u043D\u043A\u0438 \u0441 \u043F\u0440\u0435\u0444\u0438\u043A\u0441\u043E\u043C &quot;attr_&quot; (sku, brand, attr_inner_diameter, ...) <br${_scopeId}>2) \u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439: \u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E \u043B\u0438\u0441\u0442\u043E\u0432 \u043F\u043E \u043C\u043E\u0434\u0435\u043B\u044F\u043C (Brand, PartCategory, Part, CatalogItem, ...) </p></div></div></div>`);
        _push2(ssrRenderComponent($setup["VTabs"], {
          value: $setup.activeTabIndex,
          "onUpdate:value": ($event) => $setup.activeTabIndex = $event
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VTabList"], null, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["VTab"], { value: 0 }, {
                      default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0448\u0430\u0431\u043B\u043E\u043D`);
                        } else {
                          return [
                            createTextVNode("\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VTab"], { value: 1 }, {
                      default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`\u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D`);
                        } else {
                          return [
                            createTextVNode("\u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["VTab"], { value: 0 }, {
                        default: withCtx(() => [
                          createTextVNode("\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                        ]),
                        _: 1
                      }),
                      createVNode($setup["VTab"], { value: 1 }, {
                        default: withCtx(() => [
                          createTextVNode("\u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                        ]),
                        _: 1
                      })
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VTabPanels"], null, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["VTabPanel"], { value: 0 }, {
                      default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<div class="space-y-4"${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId4}>\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId4}><div${_scopeId4}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}> \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F </label>`);
                          _push5(ssrRenderComponent($setup["VMultiSelect"], {
                            modelValue: $setup.selectedTemplates,
                            "onUpdate:modelValue": ($event) => $setup.selectedTemplates = $event,
                            options: $setup.availableTemplates,
                            optionLabel: "label",
                            optionValue: "value",
                            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
                            filter: "",
                            loading: $setup.loadingTemplates,
                            class: "w-full"
                          }, null, _parent5, _scopeId4));
                          _push5(`</div><div${_scopeId4}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}> \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 </label><div class="space-y-2"${_scopeId4}><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.templateOptions.includeSampleData,
                            "onUpdate:modelValue": ($event) => $setup.templateOptions.includeSampleData = $event,
                            inputId: "includeSampleData",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="includeSampleData" class="text-sm"${_scopeId4}>\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u0440\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435</label></div>`);
                          _push5(ssrRenderComponent($setup["VInputText"], {
                            modelValue: $setup.templateOptions.categorySlug,
                            "onUpdate:modelValue": ($event) => $setup.templateOptions.categorySlug = $event,
                            placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                            class: "w-full"
                          }, null, _parent5, _scopeId4));
                          _push5(ssrRenderComponent($setup["VInputText"], {
                            modelValue: $setup.templateOptions.brandSlug,
                            "onUpdate:modelValue": ($event) => $setup.templateOptions.brandSlug = $event,
                            placeholder: "\u0411\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                            class: "w-full"
                          }, null, _parent5, _scopeId4));
                          _push5(`</div></div></div>`);
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(`</div>`);
                        } else {
                          return [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"),
                              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                                createVNode("div", null, [
                                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F "),
                                  createVNode($setup["VMultiSelect"], {
                                    modelValue: $setup.selectedTemplates,
                                    "onUpdate:modelValue": ($event) => $setup.selectedTemplates = $event,
                                    options: $setup.availableTemplates,
                                    optionLabel: "label",
                                    optionValue: "value",
                                    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
                                    filter: "",
                                    loading: $setup.loadingTemplates,
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "loading"])
                                ]),
                                createVNode("div", null, [
                                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 "),
                                  createVNode("div", { class: "space-y-2" }, [
                                    createVNode("div", { class: "flex items-center space-x-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.templateOptions.includeSampleData,
                                        "onUpdate:modelValue": ($event) => $setup.templateOptions.includeSampleData = $event,
                                        inputId: "includeSampleData",
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createVNode("label", {
                                        for: "includeSampleData",
                                        class: "text-sm"
                                      }, "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u0440\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435")
                                    ]),
                                    createVNode($setup["VInputText"], {
                                      modelValue: $setup.templateOptions.categorySlug,
                                      "onUpdate:modelValue": ($event) => $setup.templateOptions.categorySlug = $event,
                                      placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                      class: "w-full"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode($setup["VInputText"], {
                                      modelValue: $setup.templateOptions.brandSlug,
                                      "onUpdate:modelValue": ($event) => $setup.templateOptions.brandSlug = $event,
                                      placeholder: "\u0411\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                      class: "w-full"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                                  ])
                                ])
                              ]),
                              createVNode($setup["VButton"], {
                                onClick: $setup.onCreateTemplate,
                                disabled: $setup.loadingTemplate,
                                loading: $setup.loadingTemplate,
                                severity: "info",
                                outlined: "",
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"])
                            ])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VTabPanel"], { value: 1 }, {
                      default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<div class="space-y-4"${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId4}>\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D</h3><div class="grid grid-cols-1 gap-4"${_scopeId4}><div${_scopeId4}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}>\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043B\u0438\u0441\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0435\u0439</label><div class="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm"${_scopeId4}><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.Brand,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.Brand = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`Brand</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.PartCategory,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.PartCategory = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`PartCategory</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.AttributeGroup,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeGroup = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`AttributeGroup</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.AttributeTemplate,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeTemplate = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`AttributeTemplate</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.AttributeSynonymGroup,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonymGroup = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`AttributeSynonymGroup</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.AttributeSynonym,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonym = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`AttributeSynonym</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.Part,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.Part = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`Part</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.CatalogItem,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItem = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`CatalogItem</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.EquipmentModel,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModel = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`EquipmentModel</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.PartAttribute,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.PartAttribute = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`PartAttribute</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.CatalogItemAttribute,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItemAttribute = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`CatalogItemAttribute</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.EquipmentModelAttribute,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModelAttribute = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`EquipmentModelAttribute</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.PartApplicability,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.PartApplicability = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`PartApplicability</label><label class="flex items-center gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.universalInclude.EquipmentApplicability,
                            "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentApplicability = $event,
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`EquipmentApplicability</label></div></div></div>`);
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(`</div>`);
                        } else {
                          return [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"),
                              createVNode("div", { class: "grid grid-cols-1 gap-4" }, [
                                createVNode("div", null, [
                                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043B\u0438\u0441\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0435\u0439"),
                                  createVNode("div", { class: "grid grid-cols-2 md:grid-cols-3 gap-2 text-sm" }, [
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.Brand,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.Brand = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("Brand")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.PartCategory,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.PartCategory = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("PartCategory")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.AttributeGroup,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeGroup = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("AttributeGroup")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.AttributeTemplate,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeTemplate = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("AttributeTemplate")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.AttributeSynonymGroup,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonymGroup = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("AttributeSynonymGroup")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.AttributeSynonym,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonym = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("AttributeSynonym")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.Part,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.Part = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("Part")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.CatalogItem,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItem = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("CatalogItem")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.EquipmentModel,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModel = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("EquipmentModel")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.PartAttribute,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.PartAttribute = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("PartAttribute")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.CatalogItemAttribute,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItemAttribute = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("CatalogItemAttribute")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.EquipmentModelAttribute,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModelAttribute = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("EquipmentModelAttribute")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.PartApplicability,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.PartApplicability = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("PartApplicability")
                                    ]),
                                    createVNode("label", { class: "flex items-center gap-2" }, [
                                      createVNode($setup["VCheckbox"], {
                                        modelValue: $setup.universalInclude.EquipmentApplicability,
                                        "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentApplicability = $event,
                                        binary: true
                                      }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                      createTextVNode("EquipmentApplicability")
                                    ])
                                  ])
                                ])
                              ]),
                              createVNode($setup["VButton"], {
                                onClick: $setup.onCreateTemplate,
                                disabled: $setup.loadingTemplate,
                                loading: $setup.loadingTemplate,
                                severity: "info",
                                outlined: "",
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"])
                            ])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["VTabPanel"], { value: 0 }, {
                        default: withCtx(() => [
                          createVNode("div", { class: "space-y-4" }, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"),
                            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F "),
                                createVNode($setup["VMultiSelect"], {
                                  modelValue: $setup.selectedTemplates,
                                  "onUpdate:modelValue": ($event) => $setup.selectedTemplates = $event,
                                  options: $setup.availableTemplates,
                                  optionLabel: "label",
                                  optionValue: "value",
                                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
                                  filter: "",
                                  loading: $setup.loadingTemplates,
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "loading"])
                              ]),
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 "),
                                createVNode("div", { class: "space-y-2" }, [
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.templateOptions.includeSampleData,
                                      "onUpdate:modelValue": ($event) => $setup.templateOptions.includeSampleData = $event,
                                      inputId: "includeSampleData",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "includeSampleData",
                                      class: "text-sm"
                                    }, "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u0440\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435")
                                  ]),
                                  createVNode($setup["VInputText"], {
                                    modelValue: $setup.templateOptions.categorySlug,
                                    "onUpdate:modelValue": ($event) => $setup.templateOptions.categorySlug = $event,
                                    placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode($setup["VInputText"], {
                                    modelValue: $setup.templateOptions.brandSlug,
                                    "onUpdate:modelValue": ($event) => $setup.templateOptions.brandSlug = $event,
                                    placeholder: "\u0411\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                                ])
                              ])
                            ]),
                            createVNode($setup["VButton"], {
                              onClick: $setup.onCreateTemplate,
                              disabled: $setup.loadingTemplate,
                              loading: $setup.loadingTemplate,
                              severity: "info",
                              outlined: "",
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"])
                          ])
                        ]),
                        _: 1
                      }),
                      createVNode($setup["VTabPanel"], { value: 1 }, {
                        default: withCtx(() => [
                          createVNode("div", { class: "space-y-4" }, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"),
                            createVNode("div", { class: "grid grid-cols-1 gap-4" }, [
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043B\u0438\u0441\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0435\u0439"),
                                createVNode("div", { class: "grid grid-cols-2 md:grid-cols-3 gap-2 text-sm" }, [
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.Brand,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.Brand = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("Brand")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.PartCategory,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.PartCategory = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("PartCategory")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.AttributeGroup,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeGroup = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("AttributeGroup")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.AttributeTemplate,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeTemplate = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("AttributeTemplate")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.AttributeSynonymGroup,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonymGroup = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("AttributeSynonymGroup")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.AttributeSynonym,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonym = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("AttributeSynonym")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.Part,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.Part = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("Part")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.CatalogItem,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItem = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("CatalogItem")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.EquipmentModel,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModel = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("EquipmentModel")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.PartAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.PartAttribute = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("PartAttribute")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.CatalogItemAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItemAttribute = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("CatalogItemAttribute")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.EquipmentModelAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModelAttribute = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("EquipmentModelAttribute")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.PartApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.PartApplicability = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("PartApplicability")
                                  ]),
                                  createVNode("label", { class: "flex items-center gap-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.universalInclude.EquipmentApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentApplicability = $event,
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createTextVNode("EquipmentApplicability")
                                  ])
                                ])
                              ])
                            ]),
                            createVNode($setup["VButton"], {
                              onClick: $setup.onCreateTemplate,
                              disabled: $setup.loadingTemplate,
                              loading: $setup.loadingTemplate,
                              severity: "info",
                              outlined: "",
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"])
                          ])
                        ]),
                        _: 1
                      })
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VTabList"], null, {
                  default: withCtx(() => [
                    createVNode($setup["VTab"], { value: 0 }, {
                      default: withCtx(() => [
                        createTextVNode("\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VTab"], { value: 1 }, {
                      default: withCtx(() => [
                        createTextVNode("\u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }),
                createVNode($setup["VTabPanels"], null, {
                  default: withCtx(() => [
                    createVNode($setup["VTabPanel"], { value: 0 }, {
                      default: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"),
                          createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F "),
                              createVNode($setup["VMultiSelect"], {
                                modelValue: $setup.selectedTemplates,
                                "onUpdate:modelValue": ($event) => $setup.selectedTemplates = $event,
                                options: $setup.availableTemplates,
                                optionLabel: "label",
                                optionValue: "value",
                                placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
                                filter: "",
                                loading: $setup.loadingTemplates,
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "loading"])
                            ]),
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 "),
                              createVNode("div", { class: "space-y-2" }, [
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.templateOptions.includeSampleData,
                                    "onUpdate:modelValue": ($event) => $setup.templateOptions.includeSampleData = $event,
                                    inputId: "includeSampleData",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "includeSampleData",
                                    class: "text-sm"
                                  }, "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u0440\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435")
                                ]),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.templateOptions.categorySlug,
                                  "onUpdate:modelValue": ($event) => $setup.templateOptions.categorySlug = $event,
                                  placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.templateOptions.brandSlug,
                                  "onUpdate:modelValue": ($event) => $setup.templateOptions.brandSlug = $event,
                                  placeholder: "\u0411\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ])
                            ])
                          ]),
                          createVNode($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                            ]),
                            _: 1
                          }, 8, ["disabled", "loading"])
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VTabPanel"], { value: 1 }, {
                      default: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"),
                          createVNode("div", { class: "grid grid-cols-1 gap-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043B\u0438\u0441\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0435\u0439"),
                              createVNode("div", { class: "grid grid-cols-2 md:grid-cols-3 gap-2 text-sm" }, [
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.Brand,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.Brand = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("Brand")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartCategory,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartCategory = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartCategory")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeGroup,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeGroup = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeGroup")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeTemplate,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeTemplate = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeTemplate")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeSynonymGroup,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonymGroup = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeSynonymGroup")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeSynonym,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonym = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeSynonym")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.Part,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.Part = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("Part")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.CatalogItem,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItem = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("CatalogItem")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentModel,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModel = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentModel")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.CatalogItemAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItemAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("CatalogItemAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentModelAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModelAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentModelAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartApplicability = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartApplicability")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentApplicability = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentApplicability")
                                ])
                              ])
                            ])
                          ]),
                          createVNode($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                            ]),
                            _: 1
                          }, 8, ["disabled", "loading"])
                        ])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`<div class="space-y-4"${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId}>\u0418\u043C\u043F\u043E\u0440\u0442 \u0444\u0430\u0439\u043B\u0430</h3><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0424\u0430\u0439\u043B Excel (.xlsx) </label><div class="flex items-center gap-2"${_scopeId}><input type="file" accept=".xlsx" class="flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"${_scopeId}>`);
        if ($setup.file) {
          _push2(ssrRenderComponent($setup["DangerButton"], {
            onClick: $setup.clearFile,
            severity: "secondary",
            outlined: "",
            size: "small"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C`);
              } else {
                return [
                  createTextVNode("\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C")
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if ($setup.file) {
          _push2(`<div class="mt-2 text-sm text-surface-600"${_scopeId}> \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: ${ssrInterpolate($setup.file.name)} (${ssrInterpolate($setup.formatFileSize($setup.file.size))}) </div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 </label>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.importOptions.onConflict,
          "onUpdate:modelValue": ($event) => $setup.importOptions.onConflict = $event,
          options: $setup.conflictOptions,
          optionLabel: "label",
          optionValue: "value",
          placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="space-y-2 pt-6"${_scopeId}><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.importOptions.createMissingRefs,
          "onUpdate:modelValue": ($event) => $setup.importOptions.createMissingRefs = $event,
          inputId: "createMissingRefs",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="createMissingRefs" class="text-sm"${_scopeId}>\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.importOptions.createMissingTemplates,
          "onUpdate:modelValue": ($event) => $setup.importOptions.createMissingTemplates = $event,
          inputId: "createMissingTemplates",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="createMissingTemplates" class="text-sm"${_scopeId}>\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VCheckbox"], {
          modelValue: $setup.importOptions.autoLinkToParts,
          "onUpdate:modelValue": ($event) => $setup.importOptions.autoLinkToParts = $event,
          inputId: "autoLinkToParts",
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="autoLinkToParts" class="text-sm"${_scopeId}>\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u0441\u0432\u044F\u0437\u044B\u0432\u0430\u0442\u044C \u0441 \u0433\u0440\u0443\u043F\u043F\u0430\u043C\u0438</label></div></div></div><div class="flex flex-col gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onValidate,
          disabled: $setup.loading || !$setup.file || $setup.activeTabIndex !== 0,
          loading: $setup.loading && $setup.currentAction === "validate",
          severity: "info",
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loading && $setup.currentAction === "validate" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0440\u0435\u0436\u0438\u043C)")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "validate" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0440\u0435\u0436\u0438\u043C)"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.onExecute,
          disabled: $setup.loading || !$setup.file,
          loading: $setup.loading && $setup.currentAction === "execute",
          severity: "success",
          class: "w-full"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        if ($setup.activeTabIndex !== 0) {
          _push2(`<div class="text-xs text-surface-500 text-center"${_scopeId}>\u0412\u0430\u043B\u0438\u0434\u0430\u0446\u0438\u044F \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0430 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u043F\u0440\u043E\u0441\u0442\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if ($setup.result) {
          _push2(`<div class="space-y-3"${_scopeId}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId}>\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:</h3><div class="grid grid-cols-2 md:grid-cols-4 gap-3"${_scopeId}><div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800"${_scopeId}><div class="text-xs font-medium text-green-700 dark:text-green-300"${_scopeId}>\u0421\u043E\u0437\u0434\u0430\u043D\u043E \u043F\u043E\u0437\u0438\u0446\u0438\u0439</div><div class="text-lg font-bold text-green-800 dark:text-green-200"${_scopeId}>${ssrInterpolate($setup.result.counters.catalogItemsCreated)}</div></div><div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800"${_scopeId}><div class="text-xs font-medium text-blue-700 dark:text-blue-300"${_scopeId}>\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E \u043F\u043E\u0437\u0438\u0446\u0438\u0439</div><div class="text-lg font-bold text-blue-800 dark:text-blue-200"${_scopeId}>${ssrInterpolate($setup.result.counters.catalogItemsUpdated)}</div></div><div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800"${_scopeId}><div class="text-xs font-medium text-purple-700 dark:text-purple-300"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u043E</div><div class="text-lg font-bold text-purple-800 dark:text-purple-200"${_scopeId}>${ssrInterpolate($setup.result.counters.attributesCreated)}</div></div><div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-800"${_scopeId}><div class="text-xs font-medium text-orange-700 dark:text-orange-300"${_scopeId}>\u041D\u043E\u0432\u044B\u0445 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</div><div class="text-lg font-bold text-orange-800 dark:text-orange-200"${_scopeId}>${ssrInterpolate($setup.result.counters.templatesCreated)}</div></div></div>`);
          if ($setup.result.createdTemplates?.length) {
            _push2(`<div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800"${_scopeId}><div class="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1"${_scopeId}>\u0421\u043E\u0437\u0434\u0430\u043D\u043D\u044B\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432:</div><div class="text-sm text-yellow-700 dark:text-yellow-300"${_scopeId}>${ssrInterpolate($setup.result.createdTemplates.join(", "))}</div></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.result.errors?.length) {
            _push2(`<div class="space-y-2"${_scopeId}><h4 class="text-sm font-medium text-red-700 dark:text-red-400"${_scopeId}>\u041E\u0448\u0438\u0431\u043A\u0438:</h4><div class="max-h-32 overflow-y-auto space-y-1"${_scopeId}><!--[-->`);
            ssrRenderList($setup.result.errors.slice(0, 10), (error, idx) => {
              _push2(`<div class="text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"${_scopeId}>${ssrInterpolate(error.field ? `${error.field} (\u0441\u0442\u0440\u043E\u043A\u0430 ${error.rowIndex})` : `\u0421\u0442\u0440\u043E\u043A\u0430 ${error.rowIndex}`)}: ${ssrInterpolate(error.message)}</div>`);
            });
            _push2(`<!--]-->`);
            if ($setup.result.errors.length > 10) {
              _push2(`<div class="text-xs text-surface-500"${_scopeId}>... \u0438 \u0435\u0449\u0435 ${ssrInterpolate($setup.result.errors.length - 10)} \u043E\u0448\u0438\u0431\u043E\u043A</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.result.warnings?.length) {
            _push2(`<div class="space-y-2"${_scopeId}><h4 class="text-sm font-medium text-yellow-700 dark:text-yellow-400"${_scopeId}>\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u044F:</h4><div class="max-h-24 overflow-y-auto space-y-1"${_scopeId}><!--[-->`);
            ssrRenderList($setup.result.warnings.slice(0, 5), (warning, idx) => {
              _push2(`<div class="text-xs p-2 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 rounded"${_scopeId}>${ssrInterpolate(warning.field ? `${warning.field} (\u0441\u0442\u0440\u043E\u043A\u0430 ${warning.rowIndex})` : `\u0421\u0442\u0440\u043E\u043A\u0430 ${warning.rowIndex}`)}: ${ssrInterpolate(warning.message)}</div>`);
            });
            _push2(`<!--]-->`);
            if ($setup.result.warnings.length > 5) {
              _push2(`<div class="text-xs text-surface-500"${_scopeId}>... \u0438 \u0435\u0449\u0435 ${ssrInterpolate($setup.result.warnings.length - 5)} \u043F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0439</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.result.reportBase64) {
            _push2(`<div${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], {
              onClick: ($event) => $setup.downloadReport($setup.result.reportBase64),
              severity: "secondary",
              outlined: "",
              size: "small"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442`);
                } else {
                  return [
                    createTextVNode("\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-6" }, [
            createVNode("div", { class: "p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg" }, [
              createVNode("div", { class: "flex items-start gap-3" }, [
                createVNode($setup["Icon"], {
                  name: "info",
                  class: "w-5 h-5 text-blue-600 mt-0.5"
                }),
                createVNode("div", null, [
                  createVNode("h3", { class: "text-sm font-medium text-blue-900 dark:text-blue-100 mb-1" }, " \u0424\u043E\u0440\u043C\u0430\u0442\u044B \u0438\u043C\u043F\u043E\u0440\u0442\u0430 "),
                  createVNode("p", { class: "text-sm text-blue-700 dark:text-blue-200" }, [
                    createTextVNode(' 1) \u041F\u0440\u043E\u0441\u0442\u043E\u0439: \u043E\u0434\u0438\u043D \u043B\u0438\u0441\u0442 "CatalogItems". \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u2014 \u043A\u043E\u043B\u043E\u043D\u043A\u0438 \u0441 \u043F\u0440\u0435\u0444\u0438\u043A\u0441\u043E\u043C "attr_" (sku, brand, attr_inner_diameter, ...) '),
                    createVNode("br"),
                    createTextVNode("2) \u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439: \u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E \u043B\u0438\u0441\u0442\u043E\u0432 \u043F\u043E \u043C\u043E\u0434\u0435\u043B\u044F\u043C (Brand, PartCategory, Part, CatalogItem, ...) ")
                  ])
                ])
              ])
            ]),
            createVNode($setup["VTabs"], {
              value: $setup.activeTabIndex,
              "onUpdate:value": ($event) => $setup.activeTabIndex = $event
            }, {
              default: withCtx(() => [
                createVNode($setup["VTabList"], null, {
                  default: withCtx(() => [
                    createVNode($setup["VTab"], { value: 0 }, {
                      default: withCtx(() => [
                        createTextVNode("\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VTab"], { value: 1 }, {
                      default: withCtx(() => [
                        createTextVNode("\u0423\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D")
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }),
                createVNode($setup["VTabPanels"], null, {
                  default: withCtx(() => [
                    createVNode($setup["VTabPanel"], { value: 0 }, {
                      default: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"),
                          createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0434\u043B\u044F \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F "),
                              createVNode($setup["VMultiSelect"], {
                                modelValue: $setup.selectedTemplates,
                                "onUpdate:modelValue": ($event) => $setup.selectedTemplates = $event,
                                options: $setup.availableTemplates,
                                optionLabel: "label",
                                optionValue: "value",
                                placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
                                filter: "",
                                loading: $setup.loadingTemplates,
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "loading"])
                            ]),
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u0430 "),
                              createVNode("div", { class: "space-y-2" }, [
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.templateOptions.includeSampleData,
                                    "onUpdate:modelValue": ($event) => $setup.templateOptions.includeSampleData = $event,
                                    inputId: "includeSampleData",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "includeSampleData",
                                    class: "text-sm"
                                  }, "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0440\u0438\u043C\u0435\u0440\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435")
                                ]),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.templateOptions.categorySlug,
                                  "onUpdate:modelValue": ($event) => $setup.templateOptions.categorySlug = $event,
                                  placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.templateOptions.brandSlug,
                                  "onUpdate:modelValue": ($event) => $setup.templateOptions.brandSlug = $event,
                                  placeholder: "\u0411\u0440\u0435\u043D\u0434 \u0434\u043B\u044F \u043F\u0440\u0438\u043C\u0435\u0440\u043E\u0432 (slug)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ])
                            ])
                          ]),
                          createVNode($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                            ]),
                            _: 1
                          }, 8, ["disabled", "loading"])
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VTabPanel"], { value: 1 }, {
                      default: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"),
                          createVNode("div", { class: "grid grid-cols-1 gap-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043B\u0438\u0441\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0435\u0439"),
                              createVNode("div", { class: "grid grid-cols-2 md:grid-cols-3 gap-2 text-sm" }, [
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.Brand,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.Brand = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("Brand")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartCategory,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartCategory = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartCategory")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeGroup,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeGroup = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeGroup")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeTemplate,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeTemplate = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeTemplate")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeSynonymGroup,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonymGroup = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeSynonymGroup")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.AttributeSynonym,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.AttributeSynonym = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("AttributeSynonym")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.Part,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.Part = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("Part")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.CatalogItem,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItem = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("CatalogItem")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentModel,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModel = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentModel")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.CatalogItemAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.CatalogItemAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("CatalogItemAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentModelAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentModelAttribute = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentModelAttribute")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.PartApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.PartApplicability = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("PartApplicability")
                                ]),
                                createVNode("label", { class: "flex items-center gap-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.universalInclude.EquipmentApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.universalInclude.EquipmentApplicability = $event,
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createTextVNode("EquipmentApplicability")
                                ])
                              ])
                            ])
                          ]),
                          createVNode($setup["VButton"], {
                            onClick: $setup.onCreateTemplate,
                            disabled: $setup.loadingTemplate,
                            loading: $setup.loadingTemplate,
                            severity: "info",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0443\u043D\u0438\u0432\u0435\u0440\u0441\u0430\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                            ]),
                            _: 1
                          }, 8, ["disabled", "loading"])
                        ])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["value", "onUpdate:value"]),
            createVNode("div", { class: "space-y-4" }, [
              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u0444\u0430\u0439\u043B\u0430"),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
                createVNode("div", { class: "flex items-center gap-2" }, [
                  createVNode("input", {
                    type: "file",
                    onChange: $setup.onFileChange,
                    accept: ".xlsx",
                    class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                  }, null, 32),
                  $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                    key: 0,
                    onClick: $setup.clearFile,
                    severity: "secondary",
                    outlined: "",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createTextVNode("\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C")
                    ]),
                    _: 1
                  })) : createCommentVNode("", true)
                ]),
                $setup.file ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "mt-2 text-sm text-surface-600"
                }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
              ]),
              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                  createVNode($setup["VSelect"], {
                    modelValue: $setup.importOptions.onConflict,
                    "onUpdate:modelValue": ($event) => $setup.importOptions.onConflict = $event,
                    options: $setup.conflictOptions,
                    optionLabel: "label",
                    optionValue: "value",
                    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                    class: "w-full"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                createVNode("div", { class: "space-y-2 pt-6" }, [
                  createVNode("div", { class: "flex items-center space-x-2" }, [
                    createVNode($setup["VCheckbox"], {
                      modelValue: $setup.importOptions.createMissingRefs,
                      "onUpdate:modelValue": ($event) => $setup.importOptions.createMissingRefs = $event,
                      inputId: "createMissingRefs",
                      binary: true
                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                    createVNode("label", {
                      for: "createMissingRefs",
                      class: "text-sm"
                    }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
                  ]),
                  createVNode("div", { class: "flex items-center space-x-2" }, [
                    createVNode($setup["VCheckbox"], {
                      modelValue: $setup.importOptions.createMissingTemplates,
                      "onUpdate:modelValue": ($event) => $setup.importOptions.createMissingTemplates = $event,
                      inputId: "createMissingTemplates",
                      binary: true
                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                    createVNode("label", {
                      for: "createMissingTemplates",
                      class: "text-sm"
                    }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                  ]),
                  createVNode("div", { class: "flex items-center space-x-2" }, [
                    createVNode($setup["VCheckbox"], {
                      modelValue: $setup.importOptions.autoLinkToParts,
                      "onUpdate:modelValue": ($event) => $setup.importOptions.autoLinkToParts = $event,
                      inputId: "autoLinkToParts",
                      binary: true
                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                    createVNode("label", {
                      for: "autoLinkToParts",
                      class: "text-sm"
                    }, "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u0441\u0432\u044F\u0437\u044B\u0432\u0430\u0442\u044C \u0441 \u0433\u0440\u0443\u043F\u043F\u0430\u043C\u0438")
                  ])
                ])
              ]),
              createVNode("div", { class: "flex flex-col gap-2" }, [
                createVNode($setup["VButton"], {
                  onClick: $setup.onValidate,
                  disabled: $setup.loading || !$setup.file || $setup.activeTabIndex !== 0,
                  loading: $setup.loading && $setup.currentAction === "validate",
                  severity: "info",
                  class: "w-full"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "validate" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (\u041F\u0440\u043E\u0441\u0442\u043E\u0439 \u0440\u0435\u0436\u0438\u043C)"), 1)
                  ]),
                  _: 1
                }, 8, ["disabled", "loading"]),
                createVNode($setup["VButton"], {
                  onClick: $setup.onExecute,
                  disabled: $setup.loading || !$setup.file,
                  loading: $setup.loading && $setup.currentAction === "execute",
                  severity: "success",
                  class: "w-full"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0443\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                  ]),
                  _: 1
                }, 8, ["disabled", "loading"]),
                $setup.activeTabIndex !== 0 ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "text-xs text-surface-500 text-center"
                }, "\u0412\u0430\u043B\u0438\u0434\u0430\u0446\u0438\u044F \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0430 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u043F\u0440\u043E\u0441\u0442\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430")) : createCommentVNode("", true)
              ]),
              $setup.result ? (openBlock(), createBlock("div", {
                key: 0,
                class: "space-y-3"
              }, [
                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
                createVNode("div", { class: "grid grid-cols-2 md:grid-cols-4 gap-3" }, [
                  createVNode("div", { class: "p-3 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800" }, [
                    createVNode("div", { class: "text-xs font-medium text-green-700 dark:text-green-300" }, "\u0421\u043E\u0437\u0434\u0430\u043D\u043E \u043F\u043E\u0437\u0438\u0446\u0438\u0439"),
                    createVNode("div", { class: "text-lg font-bold text-green-800 dark:text-green-200" }, toDisplayString($setup.result.counters.catalogItemsCreated), 1)
                  ]),
                  createVNode("div", { class: "p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800" }, [
                    createVNode("div", { class: "text-xs font-medium text-blue-700 dark:text-blue-300" }, "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E \u043F\u043E\u0437\u0438\u0446\u0438\u0439"),
                    createVNode("div", { class: "text-lg font-bold text-blue-800 dark:text-blue-200" }, toDisplayString($setup.result.counters.catalogItemsUpdated), 1)
                  ]),
                  createVNode("div", { class: "p-3 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800" }, [
                    createVNode("div", { class: "text-xs font-medium text-purple-700 dark:text-purple-300" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u043E"),
                    createVNode("div", { class: "text-lg font-bold text-purple-800 dark:text-purple-200" }, toDisplayString($setup.result.counters.attributesCreated), 1)
                  ]),
                  createVNode("div", { class: "p-3 bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-800" }, [
                    createVNode("div", { class: "text-xs font-medium text-orange-700 dark:text-orange-300" }, "\u041D\u043E\u0432\u044B\u0445 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432"),
                    createVNode("div", { class: "text-lg font-bold text-orange-800 dark:text-orange-200" }, toDisplayString($setup.result.counters.templatesCreated), 1)
                  ])
                ]),
                $setup.result.createdTemplates?.length ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800"
                }, [
                  createVNode("div", { class: "text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1" }, "\u0421\u043E\u0437\u0434\u0430\u043D\u043D\u044B\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432:"),
                  createVNode("div", { class: "text-sm text-yellow-700 dark:text-yellow-300" }, toDisplayString($setup.result.createdTemplates.join(", ")), 1)
                ])) : createCommentVNode("", true),
                $setup.result.errors?.length ? (openBlock(), createBlock("div", {
                  key: 1,
                  class: "space-y-2"
                }, [
                  createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                  createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList($setup.result.errors.slice(0, 10), (error, idx) => {
                      return openBlock(), createBlock("div", {
                        key: idx,
                        class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                      }, toDisplayString(error.field ? `${error.field} (\u0441\u0442\u0440\u043E\u043A\u0430 ${error.rowIndex})` : `\u0421\u0442\u0440\u043E\u043A\u0430 ${error.rowIndex}`) + ": " + toDisplayString(error.message), 1);
                    }), 128)),
                    $setup.result.errors.length > 10 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "text-xs text-surface-500"
                    }, "... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.result.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A", 1)) : createCommentVNode("", true)
                  ])
                ])) : createCommentVNode("", true),
                $setup.result.warnings?.length ? (openBlock(), createBlock("div", {
                  key: 2,
                  class: "space-y-2"
                }, [
                  createVNode("h4", { class: "text-sm font-medium text-yellow-700 dark:text-yellow-400" }, "\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u044F:"),
                  createVNode("div", { class: "max-h-24 overflow-y-auto space-y-1" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList($setup.result.warnings.slice(0, 5), (warning, idx) => {
                      return openBlock(), createBlock("div", {
                        key: idx,
                        class: "text-xs p-2 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 rounded"
                      }, toDisplayString(warning.field ? `${warning.field} (\u0441\u0442\u0440\u043E\u043A\u0430 ${warning.rowIndex})` : `\u0421\u0442\u0440\u043E\u043A\u0430 ${warning.rowIndex}`) + ": " + toDisplayString(warning.message), 1);
                    }), 128)),
                    $setup.result.warnings.length > 5 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "text-xs text-surface-500"
                    }, "... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.result.warnings.length - 5) + " \u043F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0439", 1)) : createCommentVNode("", true)
                  ])
                ])) : createCommentVNode("", true),
                $setup.result.reportBase64 ? (openBlock(), createBlock("div", { key: 3 }, [
                  createVNode($setup["VButton"], {
                    onClick: ($event) => $setup.downloadReport($setup.result.reportBase64),
                    severity: "secondary",
                    outlined: "",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createTextVNode("\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442")
                    ]),
                    _: 1
                  }, 8, ["onClick"])
                ])) : createCommentVNode("", true)
              ])) : createCommentVNode("", true)
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/import/SimplifiedImportPanel.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const SimplifiedImportPanel = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ImportExportPanel",
  setup(__props, { expose: __expose }) {
    __expose();
    const { importExport, excelImport } = useTrpc();
    const activeTab = ref("simplified");
    const include = ref({
      Brand: true,
      PartCategory: true,
      AttributeGroup: false,
      AttributeTemplate: true,
      AttributeSynonymGroup: false,
      AttributeSynonym: false,
      Part: true,
      CatalogItem: true,
      EquipmentModel: true,
      PartAttribute: false,
      CatalogItemAttribute: false,
      EquipmentModelAttribute: false,
      PartApplicability: false,
      EquipmentApplicability: false
    });
    const brandSlugsInput = ref("");
    const onConflict = ref("upsert");
    const createMissingRefs = ref(false);
    const file = ref(null);
    const loading = ref(false);
    const loadingTemplate = ref(false);
    const currentAction = ref(null);
    const dryRunResult = ref(null);
    const conflictOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0438\u043B\u0438 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (upsert)", value: "upsert" },
      { label: "\u0422\u043E\u043B\u044C\u043A\u043E \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C (update_only)", value: "update_only" },
      { label: "\u041F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u044C (skip)", value: "skip" },
      { label: "\u041E\u0448\u0438\u0431\u043A\u0430 (error)", value: "error" }
    ];
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };
    const clearFile = () => {
      file.value = null;
      dryRunResult.value = null;
    };
    const onExport = async () => {
      loading.value = true;
      try {
        const brandSlugs = brandSlugsInput.value.split(",").map((s) => s.trim()).filter(Boolean);
        const res = await importExport.exportXlsx({
          include: include.value,
          filters: { brandSlugs },
          meta: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        if (!res) return;
        const blob = b64toBlob(res.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = res.fileName;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Export failed:", error);
      } finally {
        loading.value = false;
      }
    };
    const onExportTemplate = async () => {
      loadingTemplate.value = true;
      try {
        const res = await importExport.exportTemplate({
          include: include.value
        });
        if (!res) return;
        const blob = b64toBlob(res.base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = res.fileName;
        a.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Export template failed:", error);
      } finally {
        loadingTemplate.value = false;
      }
    };
    const onFileChange = (e) => {
      const target = e.target;
      file.value = target.files?.[0] ?? null;
      dryRunResult.value = null;
    };
    function fileToBase64(file2) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(String(reader.result).split(",")[1] || "");
        reader.onerror = reject;
        reader.readAsDataURL(file2);
      });
    }
    const onDryRun = async () => {
      if (!file.value) return;
      currentAction.value = "dryRun";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await useTrpc().excelImport.dryRun({
          base64,
          overrides: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        dryRunResult.value = res;
      } catch (error) {
        console.error("Dry run failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    const onExecute = async () => {
      if (!file.value) return;
      currentAction.value = "execute";
      loading.value = true;
      try {
        const base64 = await fileToBase64(file.value);
        const res = await useTrpc().excelImport.execute({
          base64,
          overrides: {
            createMissingRefs: createMissingRefs.value,
            onConflict: onConflict.value
          }
        });
        if (!res) return;
        dryRunResult.value = res;
        if (res.reportBase64) downloadReport(res.reportBase64);
      } catch (error) {
        console.error("Execute failed:", error);
      } finally {
        loading.value = false;
        currentAction.value = null;
      }
    };
    function downloadReport(base64) {
      const blob = b64toBlob(base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `import-report-${Date.now()}.xlsx`;
      a.click();
      URL.revokeObjectURL(url);
    }
    function b64toBlob(base64, contentType = "", sliceSize = 512) {
      const byteCharacters = atob(base64);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
      const blob = new Blob(byteArrays, { type: contentType });
      return blob;
    }
    const __returned__ = { importExport, excelImport, activeTab, include, brandSlugsInput, onConflict, createMissingRefs, file, loading, loadingTemplate, currentAction, dryRunResult, conflictOptions, formatFileSize, clearFile, onExport, onExportTemplate, onFileChange, fileToBase64, onDryRun, onExecute, downloadReport, b64toBlob, VCard, VCheckbox, VInputText: InputText, VButton, VSelect: Select, VTabs, VTabList, VTab, VTabPanels, VTabPanel, DangerButton, Icon, SimplifiedImportPanel };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-7xl" }, _attrs))}><div class="mb-6">`);
  _push(ssrRenderComponent($setup["VTabs"], {
    modelValue: $setup.activeTab,
    "onUpdate:modelValue": ($event) => $setup.activeTab = $event,
    class: "w-full"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VTabList"], { class: "grid grid-cols-2 w-full" }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VTab"], {
                value: "simplified",
                class: "flex items-center gap-2"
              }, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["Icon"], {
                      name: "file-spreadsheet",
                      class: "w-4 h-4"
                    }, null, _parent4, _scopeId3));
                    _push4(` \u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 `);
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "file-spreadsheet",
                        class: "w-4 h-4"
                      }),
                      createTextVNode(" \u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 ")
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VTab"], {
                value: "advanced",
                class: "flex items-center gap-2"
              }, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["Icon"], {
                      name: "settings",
                      class: "w-4 h-4"
                    }, null, _parent4, _scopeId3));
                    _push4(` \u0420\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442/\u044D\u043A\u0441\u043F\u043E\u0440\u0442 `);
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "settings",
                        class: "w-4 h-4"
                      }),
                      createTextVNode(" \u0420\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442/\u044D\u043A\u0441\u043F\u043E\u0440\u0442 ")
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VTab"], {
                  value: "simplified",
                  class: "flex items-center gap-2"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["Icon"], {
                      name: "file-spreadsheet",
                      class: "w-4 h-4"
                    }),
                    createTextVNode(" \u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 ")
                  ]),
                  _: 1
                }),
                createVNode($setup["VTab"], {
                  value: "advanced",
                  class: "flex items-center gap-2"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["Icon"], {
                      name: "settings",
                      class: "w-4 h-4"
                    }),
                    createTextVNode(" \u0420\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442/\u044D\u043A\u0441\u043F\u043E\u0440\u0442 ")
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VTabPanels"], { class: "mt-6" }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VTabPanel"], { value: "simplified" }, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["SimplifiedImportPanel"], null, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["SimplifiedImportPanel"])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VTabPanel"], { value: "advanced" }, {
                default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="grid grid-cols-1 xl:grid-cols-3 gap-6"${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
                      title: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<h2 class="text-lg font-semibold mb-4"${_scopeId4}>\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430</h2>`);
                        } else {
                          return [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                          ];
                        }
                      }),
                      content: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<div class="space-y-4"${_scopeId4}><div${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}>\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438</h3><div class="grid grid-cols-1 gap-2"${_scopeId4}><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.Brand,
                            "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                            inputId: "brand",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="brand" class="text-sm"${_scopeId4}>\u0411\u0440\u0435\u043D\u0434\u044B</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.PartCategory,
                            "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                            inputId: "partCategory",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="partCategory" class="text-sm"${_scopeId4}>\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.AttributeGroup,
                            "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                            inputId: "attributeGroup",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="attributeGroup" class="text-sm"${_scopeId4}>\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.AttributeTemplate,
                            "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                            inputId: "attributeTemplate",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="attributeTemplate" class="text-sm"${_scopeId4}>\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.AttributeSynonymGroup,
                            "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                            inputId: "synonymGroup",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="synonymGroup" class="text-sm"${_scopeId4}>\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.AttributeSynonym,
                            "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                            inputId: "synonym",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="synonym" class="text-sm"${_scopeId4}>\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.Part,
                            "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                            inputId: "part",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="part" class="text-sm"${_scopeId4}>\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.CatalogItem,
                            "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                            inputId: "catalogItem",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="catalogItem" class="text-sm"${_scopeId4}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.EquipmentModel,
                            "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                            inputId: "equipmentModel",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="equipmentModel" class="text-sm"${_scopeId4}>\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438</label></div></div></div><div${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438</h3><div class="grid grid-cols-1 gap-2"${_scopeId4}><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.PartAttribute,
                            "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                            inputId: "partAttribute",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="partAttribute" class="text-sm"${_scopeId4}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.CatalogItemAttribute,
                            "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                            inputId: "catalogItemAttribute",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="catalogItemAttribute" class="text-sm"${_scopeId4}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.EquipmentModelAttribute,
                            "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                            inputId: "equipmentModelAttribute",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="equipmentModelAttribute" class="text-sm"${_scopeId4}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.PartApplicability,
                            "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                            inputId: "partApplicability",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="partApplicability" class="text-sm"${_scopeId4}>\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</label></div><div class="flex items-center space-x-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.include.EquipmentApplicability,
                            "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                            inputId: "equipmentApplicability",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="equipmentApplicability" class="text-sm"${_scopeId4}>\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435</label></div></div></div><div${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}>\u0424\u0438\u043B\u044C\u0442\u0440\u044B</h3>`);
                          _push5(ssrRenderComponent($setup["VInputText"], {
                            modelValue: $setup.brandSlugsInput,
                            "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                            placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                            class: "w-full"
                          }, null, _parent5, _scopeId4));
                          _push5(`</div><div class="flex flex-col gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onExport,
                            disabled: $setup.loading,
                            loading: $setup.loading,
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onExportTemplate,
                            disabled: $setup.loading,
                            loading: $setup.loadingTemplate,
                            severity: "secondary",
                            outlined: "",
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(`</div></div>`);
                        } else {
                          return [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438"),
                                createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.Brand,
                                      "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                                      inputId: "brand",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "brand",
                                      class: "text-sm"
                                    }, "\u0411\u0440\u0435\u043D\u0434\u044B")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartCategory,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                                      inputId: "partCategory",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partCategory",
                                      class: "text-sm"
                                    }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeGroup,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                                      inputId: "attributeGroup",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "attributeGroup",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeTemplate,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                                      inputId: "attributeTemplate",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "attributeTemplate",
                                      class: "text-sm"
                                    }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeSynonymGroup,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                                      inputId: "synonymGroup",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "synonymGroup",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeSynonym,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                                      inputId: "synonym",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "synonym",
                                      class: "text-sm"
                                    }, "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.Part,
                                      "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                                      inputId: "part",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "part",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.CatalogItem,
                                      "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                                      inputId: "catalogItem",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "catalogItem",
                                      class: "text-sm"
                                    }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentModel,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                                      inputId: "equipmentModel",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentModel",
                                      class: "text-sm"
                                    }, "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                  ])
                                ])
                              ]),
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438"),
                                createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                                      inputId: "partAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.CatalogItemAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                                      inputId: "catalogItemAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "catalogItemAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentModelAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                                      inputId: "equipmentModelAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentModelAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                                      inputId: "partApplicability",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partApplicability",
                                      class: "text-sm"
                                    }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                                      inputId: "equipmentApplicability",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentApplicability",
                                      class: "text-sm"
                                    }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435")
                                  ])
                                ])
                              ]),
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0424\u0438\u043B\u044C\u0442\u0440\u044B"),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.brandSlugsInput,
                                  "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                                  placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ]),
                              createVNode("div", { class: "flex flex-col gap-2" }, [
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExport,
                                  disabled: $setup.loading,
                                  loading: $setup.loading,
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"]),
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExportTemplate,
                                  disabled: $setup.loading,
                                  loading: $setup.loadingTemplate,
                                  severity: "secondary",
                                  outlined: "",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"])
                              ])
                            ])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
                      title: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<h2 class="text-lg font-semibold mb-4"${_scopeId4}>\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430</h2>`);
                        } else {
                          return [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                          ];
                        }
                      }),
                      content: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<div class="space-y-4"${_scopeId4}><div${_scopeId4}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}> \u0424\u0430\u0439\u043B Excel (.xlsx) </label><div class="flex items-center gap-2"${_scopeId4}><input type="file" accept=".xlsx" class="flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"${_scopeId4}>`);
                          if ($setup.file) {
                            _push5(ssrRenderComponent($setup["DangerButton"], {
                              onClick: $setup.clearFile,
                              severity: "secondary",
                              outlined: "",
                              size: "small"
                            }, {
                              default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                                if (_push6) {
                                  _push6(` \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C `);
                                } else {
                                  return [
                                    createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                                  ];
                                }
                              }),
                              _: 1
                            }, _parent5, _scopeId4));
                          } else {
                            _push5(`<!---->`);
                          }
                          _push5(`</div>`);
                          if ($setup.file) {
                            _push5(`<div class="mt-2 text-sm text-surface-600"${_scopeId4}> \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: ${ssrInterpolate($setup.file.name)} (${ssrInterpolate($setup.formatFileSize($setup.file.size))}) </div>`);
                          } else {
                            _push5(`<!---->`);
                          }
                          _push5(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId4}><div${_scopeId4}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId4}> \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 </label>`);
                          _push5(ssrRenderComponent($setup["VSelect"], {
                            modelValue: $setup.onConflict,
                            "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                            options: $setup.conflictOptions,
                            optionLabel: "label",
                            optionValue: "value",
                            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                            class: "w-full"
                          }, null, _parent5, _scopeId4));
                          _push5(`</div><div class="flex items-center space-x-2 pt-6"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VCheckbox"], {
                            modelValue: $setup.createMissingRefs,
                            "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                            inputId: "createMissingRefs",
                            binary: true
                          }, null, _parent5, _scopeId4));
                          _push5(`<label for="createMissingRefs" class="text-sm"${_scopeId4}>\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438</label></div></div><div class="flex flex-col gap-2"${_scopeId4}>`);
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onDryRun,
                            disabled: $setup.loading || !$setup.file,
                            loading: $setup.loading && $setup.currentAction === "dryRun",
                            severity: "info",
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(ssrRenderComponent($setup["VButton"], {
                            onClick: $setup.onExecute,
                            disabled: $setup.loading || !$setup.file,
                            loading: $setup.loading && $setup.currentAction === "execute",
                            severity: "success",
                            class: "w-full"
                          }, {
                            default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                              if (_push6) {
                                _push6(`${ssrInterpolate($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442")}`);
                              } else {
                                return [
                                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                                ];
                              }
                            }),
                            _: 1
                          }, _parent5, _scopeId4));
                          _push5(`</div>`);
                          if ($setup.dryRunResult) {
                            _push5(`<div class="space-y-3"${_scopeId4}><h3 class="text-sm font-medium text-surface-700 dark:text-surface-300"${_scopeId4}>\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:</h3>`);
                            if ($setup.dryRunResult.perSheet) {
                              _push5(`<div class="grid grid-cols-2 gap-2"${_scopeId4}><!--[-->`);
                              ssrRenderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                                _push5(`<div class="p-3 bg-surface-50 dark:bg-surface-900 rounded border"${_scopeId4}><div class="text-xs font-medium text-surface-700 dark:text-surface-300"${_scopeId4}>${ssrInterpolate(sheetName)}</div><div class="text-xs text-surface-600 dark:text-surface-400"${_scopeId4}> \u0421\u0442\u0440\u043E\u043A: ${ssrInterpolate(counters.rowsSeen)} | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: ${ssrInterpolate(counters.rowsValid)} `);
                                if (counters.created) {
                                  _push5(`<span${_scopeId4}>| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: ${ssrInterpolate(counters.created)}</span>`);
                                } else {
                                  _push5(`<!---->`);
                                }
                                if (counters.updated) {
                                  _push5(`<span${_scopeId4}>| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: ${ssrInterpolate(counters.updated)}</span>`);
                                } else {
                                  _push5(`<!---->`);
                                }
                                if (counters.errorsCount) {
                                  _push5(`<span${_scopeId4}>| \u041E\u0448\u0438\u0431\u043E\u043A: ${ssrInterpolate(counters.errorsCount)}</span>`);
                                } else {
                                  _push5(`<!---->`);
                                }
                                _push5(`</div></div>`);
                              });
                              _push5(`<!--]--></div>`);
                            } else {
                              _push5(`<!---->`);
                            }
                            if ($setup.dryRunResult.errors?.length) {
                              _push5(`<div class="space-y-2"${_scopeId4}><h4 class="text-sm font-medium text-red-700 dark:text-red-400"${_scopeId4}>\u041E\u0448\u0438\u0431\u043A\u0438:</h4><div class="max-h-32 overflow-y-auto space-y-1"${_scopeId4}><!--[-->`);
                              ssrRenderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                                _push5(`<div class="text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"${_scopeId4}>${ssrInterpolate(error.sheet)}:${ssrInterpolate(error.rowIndex)} - ${ssrInterpolate(error.message)}</div>`);
                              });
                              _push5(`<!--]-->`);
                              if ($setup.dryRunResult.errors.length > 10) {
                                _push5(`<div class="text-xs text-surface-500"${_scopeId4}> ... \u0438 \u0435\u0449\u0435 ${ssrInterpolate($setup.dryRunResult.errors.length - 10)} \u043E\u0448\u0438\u0431\u043E\u043A </div>`);
                              } else {
                                _push5(`<!---->`);
                              }
                              _push5(`</div></div>`);
                            } else {
                              _push5(`<!---->`);
                            }
                            if ($setup.dryRunResult.reportBase64) {
                              _push5(`<div${_scopeId4}>`);
                              _push5(ssrRenderComponent($setup["VButton"], {
                                onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                                severity: "secondary",
                                outlined: "",
                                size: "small"
                              }, {
                                default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                                  if (_push6) {
                                    _push6(` \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 `);
                                  } else {
                                    return [
                                      createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                                    ];
                                  }
                                }),
                                _: 1
                              }, _parent5, _scopeId4));
                              _push5(`</div>`);
                            } else {
                              _push5(`<!---->`);
                            }
                            _push5(`</div>`);
                          } else {
                            _push5(`<!---->`);
                          }
                          _push5(`</div>`);
                        } else {
                          return [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode("input", {
                                    type: "file",
                                    onChange: $setup.onFileChange,
                                    accept: ".xlsx",
                                    class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                                  }, null, 32),
                                  $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                                    key: 0,
                                    onClick: $setup.clearFile,
                                    severity: "secondary",
                                    outlined: "",
                                    size: "small"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                                    ]),
                                    _: 1
                                  })) : createCommentVNode("", true)
                                ]),
                                $setup.file ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "mt-2 text-sm text-surface-600"
                                }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
                              ]),
                              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                                createVNode("div", null, [
                                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                                  createVNode($setup["VSelect"], {
                                    modelValue: $setup.onConflict,
                                    "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                                    options: $setup.conflictOptions,
                                    optionLabel: "label",
                                    optionValue: "value",
                                    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2 pt-6" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.createMissingRefs,
                                    "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                                    inputId: "createMissingRefs",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "createMissingRefs",
                                    class: "text-sm"
                                  }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
                                ])
                              ]),
                              createVNode("div", { class: "flex flex-col gap-2" }, [
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onDryRun,
                                  disabled: $setup.loading || !$setup.file,
                                  loading: $setup.loading && $setup.currentAction === "dryRun",
                                  severity: "info",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"]),
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExecute,
                                  disabled: $setup.loading || !$setup.file,
                                  loading: $setup.loading && $setup.currentAction === "execute",
                                  severity: "success",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"])
                              ]),
                              $setup.dryRunResult ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "space-y-3"
                              }, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
                                $setup.dryRunResult.perSheet ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "grid grid-cols-2 gap-2"
                                }, [
                                  (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                                    return openBlock(), createBlock("div", {
                                      key: sheetName,
                                      class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                                    }, [
                                      createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300" }, toDisplayString(sheetName), 1),
                                      createVNode("div", { class: "text-xs text-surface-600 dark:text-surface-400" }, [
                                        createTextVNode(" \u0421\u0442\u0440\u043E\u043A: " + toDisplayString(counters.rowsSeen) + " | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: " + toDisplayString(counters.rowsValid) + " ", 1),
                                        counters.created ? (openBlock(), createBlock("span", { key: 0 }, "| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: " + toDisplayString(counters.created), 1)) : createCommentVNode("", true),
                                        counters.updated ? (openBlock(), createBlock("span", { key: 1 }, "| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: " + toDisplayString(counters.updated), 1)) : createCommentVNode("", true),
                                        counters.errorsCount ? (openBlock(), createBlock("span", { key: 2 }, "| \u041E\u0448\u0438\u0431\u043E\u043A: " + toDisplayString(counters.errorsCount), 1)) : createCommentVNode("", true)
                                      ])
                                    ]);
                                  }), 128))
                                ])) : createCommentVNode("", true),
                                $setup.dryRunResult.errors?.length ? (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "space-y-2"
                                }, [
                                  createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                                  createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                                    (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                                      return openBlock(), createBlock("div", {
                                        key: idx,
                                        class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                                      }, toDisplayString(error.sheet) + ":" + toDisplayString(error.rowIndex) + " - " + toDisplayString(error.message), 1);
                                    }), 128)),
                                    $setup.dryRunResult.errors.length > 10 ? (openBlock(), createBlock("div", {
                                      key: 0,
                                      class: "text-xs text-surface-500"
                                    }, " ... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.dryRunResult.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A ", 1)) : createCommentVNode("", true)
                                  ])
                                ])) : createCommentVNode("", true),
                                $setup.dryRunResult.reportBase64 ? (openBlock(), createBlock("div", { key: 2 }, [
                                  createVNode($setup["VButton"], {
                                    onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                                    severity: "secondary",
                                    outlined: "",
                                    size: "small"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                                    ]),
                                    _: 1
                                  }, 8, ["onClick"])
                                ])) : createCommentVNode("", true)
                              ])) : createCommentVNode("", true)
                            ])
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VCard"], { class: "p-6" }, {
                      title: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<h2 class="text-lg font-semibold mb-4"${_scopeId4}>\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432</h2>`);
                        } else {
                          return [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432")
                          ];
                        }
                      }),
                      content: withCtx((_4, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`<div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId4}> \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 </div>`);
                        } else {
                          return [
                            createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 ")
                          ];
                        }
                      }),
                      _: 1
                    }, _parent4, _scopeId3));
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "grid grid-cols-1 xl:grid-cols-3 gap-6" }, [
                        createVNode($setup["VCard"], { class: "p-6" }, {
                          title: withCtx(() => [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                          ]),
                          content: withCtx(() => [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438"),
                                createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.Brand,
                                      "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                                      inputId: "brand",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "brand",
                                      class: "text-sm"
                                    }, "\u0411\u0440\u0435\u043D\u0434\u044B")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartCategory,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                                      inputId: "partCategory",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partCategory",
                                      class: "text-sm"
                                    }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeGroup,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                                      inputId: "attributeGroup",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "attributeGroup",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeTemplate,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                                      inputId: "attributeTemplate",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "attributeTemplate",
                                      class: "text-sm"
                                    }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeSynonymGroup,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                                      inputId: "synonymGroup",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "synonymGroup",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.AttributeSynonym,
                                      "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                                      inputId: "synonym",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "synonym",
                                      class: "text-sm"
                                    }, "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.Part,
                                      "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                                      inputId: "part",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "part",
                                      class: "text-sm"
                                    }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.CatalogItem,
                                      "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                                      inputId: "catalogItem",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "catalogItem",
                                      class: "text-sm"
                                    }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentModel,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                                      inputId: "equipmentModel",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentModel",
                                      class: "text-sm"
                                    }, "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                  ])
                                ])
                              ]),
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438"),
                                createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                                      inputId: "partAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.CatalogItemAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                                      inputId: "catalogItemAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "catalogItemAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentModelAttribute,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                                      inputId: "equipmentModelAttribute",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentModelAttribute",
                                      class: "text-sm"
                                    }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.PartApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                                      inputId: "partApplicability",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "partApplicability",
                                      class: "text-sm"
                                    }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                  ]),
                                  createVNode("div", { class: "flex items-center space-x-2" }, [
                                    createVNode($setup["VCheckbox"], {
                                      modelValue: $setup.include.EquipmentApplicability,
                                      "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                                      inputId: "equipmentApplicability",
                                      binary: true
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                    createVNode("label", {
                                      for: "equipmentApplicability",
                                      class: "text-sm"
                                    }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435")
                                  ])
                                ])
                              ]),
                              createVNode("div", null, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0424\u0438\u043B\u044C\u0442\u0440\u044B"),
                                createVNode($setup["VInputText"], {
                                  modelValue: $setup.brandSlugsInput,
                                  "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                                  placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ]),
                              createVNode("div", { class: "flex flex-col gap-2" }, [
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExport,
                                  disabled: $setup.loading,
                                  loading: $setup.loading,
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"]),
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExportTemplate,
                                  disabled: $setup.loading,
                                  loading: $setup.loadingTemplate,
                                  severity: "secondary",
                                  outlined: "",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"])
                              ])
                            ])
                          ]),
                          _: 1
                        }),
                        createVNode($setup["VCard"], { class: "p-6" }, {
                          title: withCtx(() => [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                          ]),
                          content: withCtx(() => [
                            createVNode("div", { class: "space-y-4" }, [
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
                                createVNode("div", { class: "flex items-center gap-2" }, [
                                  createVNode("input", {
                                    type: "file",
                                    onChange: $setup.onFileChange,
                                    accept: ".xlsx",
                                    class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                                  }, null, 32),
                                  $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                                    key: 0,
                                    onClick: $setup.clearFile,
                                    severity: "secondary",
                                    outlined: "",
                                    size: "small"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                                    ]),
                                    _: 1
                                  })) : createCommentVNode("", true)
                                ]),
                                $setup.file ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "mt-2 text-sm text-surface-600"
                                }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
                              ]),
                              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                                createVNode("div", null, [
                                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                                  createVNode($setup["VSelect"], {
                                    modelValue: $setup.onConflict,
                                    "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                                    options: $setup.conflictOptions,
                                    optionLabel: "label",
                                    optionValue: "value",
                                    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                                    class: "w-full"
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2 pt-6" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.createMissingRefs,
                                    "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                                    inputId: "createMissingRefs",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "createMissingRefs",
                                    class: "text-sm"
                                  }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
                                ])
                              ]),
                              createVNode("div", { class: "flex flex-col gap-2" }, [
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onDryRun,
                                  disabled: $setup.loading || !$setup.file,
                                  loading: $setup.loading && $setup.currentAction === "dryRun",
                                  severity: "info",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"]),
                                createVNode($setup["VButton"], {
                                  onClick: $setup.onExecute,
                                  disabled: $setup.loading || !$setup.file,
                                  loading: $setup.loading && $setup.currentAction === "execute",
                                  severity: "success",
                                  class: "w-full"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                                  ]),
                                  _: 1
                                }, 8, ["disabled", "loading"])
                              ]),
                              $setup.dryRunResult ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "space-y-3"
                              }, [
                                createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
                                $setup.dryRunResult.perSheet ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "grid grid-cols-2 gap-2"
                                }, [
                                  (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                                    return openBlock(), createBlock("div", {
                                      key: sheetName,
                                      class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                                    }, [
                                      createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300" }, toDisplayString(sheetName), 1),
                                      createVNode("div", { class: "text-xs text-surface-600 dark:text-surface-400" }, [
                                        createTextVNode(" \u0421\u0442\u0440\u043E\u043A: " + toDisplayString(counters.rowsSeen) + " | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: " + toDisplayString(counters.rowsValid) + " ", 1),
                                        counters.created ? (openBlock(), createBlock("span", { key: 0 }, "| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: " + toDisplayString(counters.created), 1)) : createCommentVNode("", true),
                                        counters.updated ? (openBlock(), createBlock("span", { key: 1 }, "| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: " + toDisplayString(counters.updated), 1)) : createCommentVNode("", true),
                                        counters.errorsCount ? (openBlock(), createBlock("span", { key: 2 }, "| \u041E\u0448\u0438\u0431\u043E\u043A: " + toDisplayString(counters.errorsCount), 1)) : createCommentVNode("", true)
                                      ])
                                    ]);
                                  }), 128))
                                ])) : createCommentVNode("", true),
                                $setup.dryRunResult.errors?.length ? (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "space-y-2"
                                }, [
                                  createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                                  createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                                    (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                                      return openBlock(), createBlock("div", {
                                        key: idx,
                                        class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                                      }, toDisplayString(error.sheet) + ":" + toDisplayString(error.rowIndex) + " - " + toDisplayString(error.message), 1);
                                    }), 128)),
                                    $setup.dryRunResult.errors.length > 10 ? (openBlock(), createBlock("div", {
                                      key: 0,
                                      class: "text-xs text-surface-500"
                                    }, " ... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.dryRunResult.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A ", 1)) : createCommentVNode("", true)
                                  ])
                                ])) : createCommentVNode("", true),
                                $setup.dryRunResult.reportBase64 ? (openBlock(), createBlock("div", { key: 2 }, [
                                  createVNode($setup["VButton"], {
                                    onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                                    severity: "secondary",
                                    outlined: "",
                                    size: "small"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                                    ]),
                                    _: 1
                                  }, 8, ["onClick"])
                                ])) : createCommentVNode("", true)
                              ])) : createCommentVNode("", true)
                            ])
                          ]),
                          _: 1
                        }),
                        createVNode($setup["VCard"], { class: "p-6" }, {
                          title: withCtx(() => [
                            createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432")
                          ]),
                          content: withCtx(() => [
                            createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 ")
                          ]),
                          _: 1
                        })
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VTabPanel"], { value: "simplified" }, {
                  default: withCtx(() => [
                    createVNode($setup["SimplifiedImportPanel"])
                  ]),
                  _: 1
                }),
                createVNode($setup["VTabPanel"], { value: "advanced" }, {
                  default: withCtx(() => [
                    createVNode("div", { class: "grid grid-cols-1 xl:grid-cols-3 gap-6" }, [
                      createVNode($setup["VCard"], { class: "p-6" }, {
                        title: withCtx(() => [
                          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                        ]),
                        content: withCtx(() => [
                          createVNode("div", { class: "space-y-4" }, [
                            createVNode("div", null, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438"),
                              createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.Brand,
                                    "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                                    inputId: "brand",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "brand",
                                    class: "text-sm"
                                  }, "\u0411\u0440\u0435\u043D\u0434\u044B")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.PartCategory,
                                    "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                                    inputId: "partCategory",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "partCategory",
                                    class: "text-sm"
                                  }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.AttributeGroup,
                                    "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                                    inputId: "attributeGroup",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "attributeGroup",
                                    class: "text-sm"
                                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.AttributeTemplate,
                                    "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                                    inputId: "attributeTemplate",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "attributeTemplate",
                                    class: "text-sm"
                                  }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.AttributeSynonymGroup,
                                    "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                                    inputId: "synonymGroup",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "synonymGroup",
                                    class: "text-sm"
                                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.AttributeSynonym,
                                    "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                                    inputId: "synonym",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "synonym",
                                    class: "text-sm"
                                  }, "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.Part,
                                    "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                                    inputId: "part",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "part",
                                    class: "text-sm"
                                  }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.CatalogItem,
                                    "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                                    inputId: "catalogItem",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "catalogItem",
                                    class: "text-sm"
                                  }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.EquipmentModel,
                                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                                    inputId: "equipmentModel",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "equipmentModel",
                                    class: "text-sm"
                                  }, "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                ])
                              ])
                            ]),
                            createVNode("div", null, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438"),
                              createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.PartAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                                    inputId: "partAttribute",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "partAttribute",
                                    class: "text-sm"
                                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.CatalogItemAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                                    inputId: "catalogItemAttribute",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "catalogItemAttribute",
                                    class: "text-sm"
                                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.EquipmentModelAttribute,
                                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                                    inputId: "equipmentModelAttribute",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "equipmentModelAttribute",
                                    class: "text-sm"
                                  }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.PartApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                                    inputId: "partApplicability",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "partApplicability",
                                    class: "text-sm"
                                  }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                                ]),
                                createVNode("div", { class: "flex items-center space-x-2" }, [
                                  createVNode($setup["VCheckbox"], {
                                    modelValue: $setup.include.EquipmentApplicability,
                                    "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                                    inputId: "equipmentApplicability",
                                    binary: true
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                  createVNode("label", {
                                    for: "equipmentApplicability",
                                    class: "text-sm"
                                  }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435")
                                ])
                              ])
                            ]),
                            createVNode("div", null, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0424\u0438\u043B\u044C\u0442\u0440\u044B"),
                              createVNode($setup["VInputText"], {
                                modelValue: $setup.brandSlugsInput,
                                "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                                placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue"])
                            ]),
                            createVNode("div", { class: "flex flex-col gap-2" }, [
                              createVNode($setup["VButton"], {
                                onClick: $setup.onExport,
                                disabled: $setup.loading,
                                loading: $setup.loading,
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"]),
                              createVNode($setup["VButton"], {
                                onClick: $setup.onExportTemplate,
                                disabled: $setup.loading,
                                loading: $setup.loadingTemplate,
                                severity: "secondary",
                                outlined: "",
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"])
                            ])
                          ])
                        ]),
                        _: 1
                      }),
                      createVNode($setup["VCard"], { class: "p-6" }, {
                        title: withCtx(() => [
                          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                        ]),
                        content: withCtx(() => [
                          createVNode("div", { class: "space-y-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
                              createVNode("div", { class: "flex items-center gap-2" }, [
                                createVNode("input", {
                                  type: "file",
                                  onChange: $setup.onFileChange,
                                  accept: ".xlsx",
                                  class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                                }, null, 32),
                                $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                                  key: 0,
                                  onClick: $setup.clearFile,
                                  severity: "secondary",
                                  outlined: "",
                                  size: "small"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                                  ]),
                                  _: 1
                                })) : createCommentVNode("", true)
                              ]),
                              $setup.file ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "mt-2 text-sm text-surface-600"
                              }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
                            ]),
                            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                              createVNode("div", null, [
                                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                                createVNode($setup["VSelect"], {
                                  modelValue: $setup.onConflict,
                                  "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                                  options: $setup.conflictOptions,
                                  optionLabel: "label",
                                  optionValue: "value",
                                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                                  class: "w-full"
                                }, null, 8, ["modelValue", "onUpdate:modelValue"])
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2 pt-6" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.createMissingRefs,
                                  "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                                  inputId: "createMissingRefs",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "createMissingRefs",
                                  class: "text-sm"
                                }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
                              ])
                            ]),
                            createVNode("div", { class: "flex flex-col gap-2" }, [
                              createVNode($setup["VButton"], {
                                onClick: $setup.onDryRun,
                                disabled: $setup.loading || !$setup.file,
                                loading: $setup.loading && $setup.currentAction === "dryRun",
                                severity: "info",
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"]),
                              createVNode($setup["VButton"], {
                                onClick: $setup.onExecute,
                                disabled: $setup.loading || !$setup.file,
                                loading: $setup.loading && $setup.currentAction === "execute",
                                severity: "success",
                                class: "w-full"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                                ]),
                                _: 1
                              }, 8, ["disabled", "loading"])
                            ]),
                            $setup.dryRunResult ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "space-y-3"
                            }, [
                              createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
                              $setup.dryRunResult.perSheet ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "grid grid-cols-2 gap-2"
                              }, [
                                (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                                  return openBlock(), createBlock("div", {
                                    key: sheetName,
                                    class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                                  }, [
                                    createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300" }, toDisplayString(sheetName), 1),
                                    createVNode("div", { class: "text-xs text-surface-600 dark:text-surface-400" }, [
                                      createTextVNode(" \u0421\u0442\u0440\u043E\u043A: " + toDisplayString(counters.rowsSeen) + " | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: " + toDisplayString(counters.rowsValid) + " ", 1),
                                      counters.created ? (openBlock(), createBlock("span", { key: 0 }, "| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: " + toDisplayString(counters.created), 1)) : createCommentVNode("", true),
                                      counters.updated ? (openBlock(), createBlock("span", { key: 1 }, "| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: " + toDisplayString(counters.updated), 1)) : createCommentVNode("", true),
                                      counters.errorsCount ? (openBlock(), createBlock("span", { key: 2 }, "| \u041E\u0448\u0438\u0431\u043E\u043A: " + toDisplayString(counters.errorsCount), 1)) : createCommentVNode("", true)
                                    ])
                                  ]);
                                }), 128))
                              ])) : createCommentVNode("", true),
                              $setup.dryRunResult.errors?.length ? (openBlock(), createBlock("div", {
                                key: 1,
                                class: "space-y-2"
                              }, [
                                createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                                createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                                  (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                                    return openBlock(), createBlock("div", {
                                      key: idx,
                                      class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                                    }, toDisplayString(error.sheet) + ":" + toDisplayString(error.rowIndex) + " - " + toDisplayString(error.message), 1);
                                  }), 128)),
                                  $setup.dryRunResult.errors.length > 10 ? (openBlock(), createBlock("div", {
                                    key: 0,
                                    class: "text-xs text-surface-500"
                                  }, " ... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.dryRunResult.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A ", 1)) : createCommentVNode("", true)
                                ])
                              ])) : createCommentVNode("", true),
                              $setup.dryRunResult.reportBase64 ? (openBlock(), createBlock("div", { key: 2 }, [
                                createVNode($setup["VButton"], {
                                  onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                                  severity: "secondary",
                                  outlined: "",
                                  size: "small"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                                  ]),
                                  _: 1
                                }, 8, ["onClick"])
                              ])) : createCommentVNode("", true)
                            ])) : createCommentVNode("", true)
                          ])
                        ]),
                        _: 1
                      }),
                      createVNode($setup["VCard"], { class: "p-6" }, {
                        title: withCtx(() => [
                          createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432")
                        ]),
                        content: withCtx(() => [
                          createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 ")
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VTabList"], { class: "grid grid-cols-2 w-full" }, {
            default: withCtx(() => [
              createVNode($setup["VTab"], {
                value: "simplified",
                class: "flex items-center gap-2"
              }, {
                default: withCtx(() => [
                  createVNode($setup["Icon"], {
                    name: "file-spreadsheet",
                    class: "w-4 h-4"
                  }),
                  createTextVNode(" \u0423\u043F\u0440\u043E\u0449\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442 ")
                ]),
                _: 1
              }),
              createVNode($setup["VTab"], {
                value: "advanced",
                class: "flex items-center gap-2"
              }, {
                default: withCtx(() => [
                  createVNode($setup["Icon"], {
                    name: "settings",
                    class: "w-4 h-4"
                  }),
                  createTextVNode(" \u0420\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u043D\u044B\u0439 \u0438\u043C\u043F\u043E\u0440\u0442/\u044D\u043A\u0441\u043F\u043E\u0440\u0442 ")
                ]),
                _: 1
              })
            ]),
            _: 1
          }),
          createVNode($setup["VTabPanels"], { class: "mt-6" }, {
            default: withCtx(() => [
              createVNode($setup["VTabPanel"], { value: "simplified" }, {
                default: withCtx(() => [
                  createVNode($setup["SimplifiedImportPanel"])
                ]),
                _: 1
              }),
              createVNode($setup["VTabPanel"], { value: "advanced" }, {
                default: withCtx(() => [
                  createVNode("div", { class: "grid grid-cols-1 xl:grid-cols-3 gap-6" }, [
                    createVNode($setup["VCard"], { class: "p-6" }, {
                      title: withCtx(() => [
                        createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                      ]),
                      content: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("div", null, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0441\u0443\u0449\u043D\u043E\u0441\u0442\u0438"),
                            createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.Brand,
                                  "onUpdate:modelValue": ($event) => $setup.include.Brand = $event,
                                  inputId: "brand",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "brand",
                                  class: "text-sm"
                                }, "\u0411\u0440\u0435\u043D\u0434\u044B")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.PartCategory,
                                  "onUpdate:modelValue": ($event) => $setup.include.PartCategory = $event,
                                  inputId: "partCategory",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "partCategory",
                                  class: "text-sm"
                                }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.AttributeGroup,
                                  "onUpdate:modelValue": ($event) => $setup.include.AttributeGroup = $event,
                                  inputId: "attributeGroup",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "attributeGroup",
                                  class: "text-sm"
                                }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.AttributeTemplate,
                                  "onUpdate:modelValue": ($event) => $setup.include.AttributeTemplate = $event,
                                  inputId: "attributeTemplate",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "attributeTemplate",
                                  class: "text-sm"
                                }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.AttributeSynonymGroup,
                                  "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonymGroup = $event,
                                  inputId: "synonymGroup",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "synonymGroup",
                                  class: "text-sm"
                                }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.AttributeSynonym,
                                  "onUpdate:modelValue": ($event) => $setup.include.AttributeSynonym = $event,
                                  inputId: "synonym",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "synonym",
                                  class: "text-sm"
                                }, "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.Part,
                                  "onUpdate:modelValue": ($event) => $setup.include.Part = $event,
                                  inputId: "part",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "part",
                                  class: "text-sm"
                                }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.CatalogItem,
                                  "onUpdate:modelValue": ($event) => $setup.include.CatalogItem = $event,
                                  inputId: "catalogItem",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "catalogItem",
                                  class: "text-sm"
                                }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.EquipmentModel,
                                  "onUpdate:modelValue": ($event) => $setup.include.EquipmentModel = $event,
                                  inputId: "equipmentModel",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "equipmentModel",
                                  class: "text-sm"
                                }, "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                              ])
                            ])
                          ]),
                          createVNode("div", null, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0438 \u0441\u0432\u044F\u0437\u0438"),
                            createVNode("div", { class: "grid grid-cols-1 gap-2" }, [
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.PartAttribute,
                                  "onUpdate:modelValue": ($event) => $setup.include.PartAttribute = $event,
                                  inputId: "partAttribute",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "partAttribute",
                                  class: "text-sm"
                                }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.CatalogItemAttribute,
                                  "onUpdate:modelValue": ($event) => $setup.include.CatalogItemAttribute = $event,
                                  inputId: "catalogItemAttribute",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "catalogItemAttribute",
                                  class: "text-sm"
                                }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043F\u043E\u0437\u0438\u0446\u0438\u0439")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.EquipmentModelAttribute,
                                  "onUpdate:modelValue": ($event) => $setup.include.EquipmentModelAttribute = $event,
                                  inputId: "equipmentModelAttribute",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "equipmentModelAttribute",
                                  class: "text-sm"
                                }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0442\u0435\u0445\u043D\u0438\u043A\u0438")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.PartApplicability,
                                  "onUpdate:modelValue": ($event) => $setup.include.PartApplicability = $event,
                                  inputId: "partApplicability",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "partApplicability",
                                  class: "text-sm"
                                }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439")
                              ]),
                              createVNode("div", { class: "flex items-center space-x-2" }, [
                                createVNode($setup["VCheckbox"], {
                                  modelValue: $setup.include.EquipmentApplicability,
                                  "onUpdate:modelValue": ($event) => $setup.include.EquipmentApplicability = $event,
                                  inputId: "equipmentApplicability",
                                  binary: true
                                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                                createVNode("label", {
                                  for: "equipmentApplicability",
                                  class: "text-sm"
                                }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435")
                              ])
                            ])
                          ]),
                          createVNode("div", null, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0424\u0438\u043B\u044C\u0442\u0440\u044B"),
                            createVNode($setup["VInputText"], {
                              modelValue: $setup.brandSlugsInput,
                              "onUpdate:modelValue": ($event) => $setup.brandSlugsInput = $event,
                              placeholder: "\u0411\u0440\u0435\u043D\u0434\u044B \u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: cat,komatsu)",
                              class: "w-full"
                            }, null, 8, ["modelValue", "onUpdate:modelValue"])
                          ]),
                          createVNode("div", { class: "flex flex-col gap-2" }, [
                            createVNode($setup["VButton"], {
                              onClick: $setup.onExport,
                              disabled: $setup.loading,
                              loading: $setup.loading,
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loading ? "\u042D\u043A\u0441\u043F\u043E\u0440\u0442..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"]),
                            createVNode($setup["VButton"], {
                              onClick: $setup.onExportTemplate,
                              disabled: $setup.loading,
                              loading: $setup.loadingTemplate,
                              severity: "secondary",
                              outlined: "",
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loadingTemplate ? "\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435..." : "\u0421\u043A\u0430\u0447\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"])
                          ])
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VCard"], { class: "p-6" }, {
                      title: withCtx(() => [
                        createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u043C\u043F\u043E\u0440\u0442 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0430")
                      ]),
                      content: withCtx(() => [
                        createVNode("div", { class: "space-y-4" }, [
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0424\u0430\u0439\u043B Excel (.xlsx) "),
                            createVNode("div", { class: "flex items-center gap-2" }, [
                              createVNode("input", {
                                type: "file",
                                onChange: $setup.onFileChange,
                                accept: ".xlsx",
                                class: "flex-1 text-sm text-surface-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                              }, null, 32),
                              $setup.file ? (openBlock(), createBlock($setup["DangerButton"], {
                                key: 0,
                                onClick: $setup.clearFile,
                                severity: "secondary",
                                outlined: "",
                                size: "small"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C ")
                                ]),
                                _: 1
                              })) : createCommentVNode("", true)
                            ]),
                            $setup.file ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "mt-2 text-sm text-surface-600"
                            }, " \u0412\u044B\u0431\u0440\u0430\u043D \u0444\u0430\u0439\u043B: " + toDisplayString($setup.file.name) + " (" + toDisplayString($setup.formatFileSize($setup.file.size)) + ") ", 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0420\u0435\u0436\u0438\u043C \u043A\u043E\u043D\u0444\u043B\u0438\u043A\u0442\u043E\u0432 "),
                              createVNode($setup["VSelect"], {
                                modelValue: $setup.onConflict,
                                "onUpdate:modelValue": ($event) => $setup.onConflict = $event,
                                options: $setup.conflictOptions,
                                optionLabel: "label",
                                optionValue: "value",
                                placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C",
                                class: "w-full"
                              }, null, 8, ["modelValue", "onUpdate:modelValue"])
                            ]),
                            createVNode("div", { class: "flex items-center space-x-2 pt-6" }, [
                              createVNode($setup["VCheckbox"], {
                                modelValue: $setup.createMissingRefs,
                                "onUpdate:modelValue": ($event) => $setup.createMissingRefs = $event,
                                inputId: "createMissingRefs",
                                binary: true
                              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                              createVNode("label", {
                                for: "createMissingRefs",
                                class: "text-sm"
                              }, "\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438")
                            ])
                          ]),
                          createVNode("div", { class: "flex flex-col gap-2" }, [
                            createVNode($setup["VButton"], {
                              onClick: $setup.onDryRun,
                              disabled: $setup.loading || !$setup.file,
                              loading: $setup.loading && $setup.currentAction === "dryRun",
                              severity: "info",
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "dryRun" ? "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430..." : "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C \u0444\u0430\u0439\u043B (Dry Run)"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"]),
                            createVNode($setup["VButton"], {
                              onClick: $setup.onExecute,
                              disabled: $setup.loading || !$setup.file,
                              loading: $setup.loading && $setup.currentAction === "execute",
                              severity: "success",
                              class: "w-full"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString($setup.loading && $setup.currentAction === "execute" ? "\u0412\u044B\u043F\u043E\u043B\u043D\u044F\u044E..." : "\u0412\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u043E\u0440\u0442"), 1)
                              ]),
                              _: 1
                            }, 8, ["disabled", "loading"])
                          ]),
                          $setup.dryRunResult ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "space-y-3"
                          }, [
                            createVNode("h3", { class: "text-sm font-medium text-surface-700 dark:text-surface-300" }, "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442:"),
                            $setup.dryRunResult.perSheet ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "grid grid-cols-2 gap-2"
                            }, [
                              (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.perSheet, (counters, sheetName) => {
                                return openBlock(), createBlock("div", {
                                  key: sheetName,
                                  class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                                }, [
                                  createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300" }, toDisplayString(sheetName), 1),
                                  createVNode("div", { class: "text-xs text-surface-600 dark:text-surface-400" }, [
                                    createTextVNode(" \u0421\u0442\u0440\u043E\u043A: " + toDisplayString(counters.rowsSeen) + " | \u0412\u0430\u043B\u0438\u0434\u043D\u044B\u0445: " + toDisplayString(counters.rowsValid) + " ", 1),
                                    counters.created ? (openBlock(), createBlock("span", { key: 0 }, "| \u0421\u043E\u0437\u0434\u0430\u043D\u043E: " + toDisplayString(counters.created), 1)) : createCommentVNode("", true),
                                    counters.updated ? (openBlock(), createBlock("span", { key: 1 }, "| \u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E: " + toDisplayString(counters.updated), 1)) : createCommentVNode("", true),
                                    counters.errorsCount ? (openBlock(), createBlock("span", { key: 2 }, "| \u041E\u0448\u0438\u0431\u043E\u043A: " + toDisplayString(counters.errorsCount), 1)) : createCommentVNode("", true)
                                  ])
                                ]);
                              }), 128))
                            ])) : createCommentVNode("", true),
                            $setup.dryRunResult.errors?.length ? (openBlock(), createBlock("div", {
                              key: 1,
                              class: "space-y-2"
                            }, [
                              createVNode("h4", { class: "text-sm font-medium text-red-700 dark:text-red-400" }, "\u041E\u0448\u0438\u0431\u043A\u0438:"),
                              createVNode("div", { class: "max-h-32 overflow-y-auto space-y-1" }, [
                                (openBlock(true), createBlock(Fragment, null, renderList($setup.dryRunResult.errors.slice(0, 10), (error, idx) => {
                                  return openBlock(), createBlock("div", {
                                    key: idx,
                                    class: "text-xs p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded"
                                  }, toDisplayString(error.sheet) + ":" + toDisplayString(error.rowIndex) + " - " + toDisplayString(error.message), 1);
                                }), 128)),
                                $setup.dryRunResult.errors.length > 10 ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "text-xs text-surface-500"
                                }, " ... \u0438 \u0435\u0449\u0435 " + toDisplayString($setup.dryRunResult.errors.length - 10) + " \u043E\u0448\u0438\u0431\u043E\u043A ", 1)) : createCommentVNode("", true)
                              ])
                            ])) : createCommentVNode("", true),
                            $setup.dryRunResult.reportBase64 ? (openBlock(), createBlock("div", { key: 2 }, [
                              createVNode($setup["VButton"], {
                                onClick: ($event) => $setup.downloadReport($setup.dryRunResult.reportBase64),
                                severity: "secondary",
                                outlined: "",
                                size: "small"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(" \u0421\u043A\u0430\u0447\u0430\u0442\u044C \u043E\u0442\u0447\u0451\u0442 ")
                                ]),
                                _: 1
                              }, 8, ["onClick"])
                            ])) : createCommentVNode("", true)
                          ])) : createCommentVNode("", true)
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["VCard"], { class: "p-6" }, {
                      title: withCtx(() => [
                        createVNode("h2", { class: "text-lg font-semibold mb-4" }, "\u0418\u0441\u0442\u043E\u0440\u0438\u044F \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432")
                      ]),
                      content: withCtx(() => [
                        createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u0424\u0443\u043D\u043A\u0446\u0438\u043E\u043D\u0430\u043B \u0438\u0441\u0442\u043E\u0440\u0438\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u043E\u0432 \u0431\u0443\u0434\u0435\u0442 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439 \u0438\u0442\u0435\u0440\u0430\u0446\u0438\u0438 ")
                      ]),
                      _: 1
                    })
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/catalog/ImportExportPanel.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ImportExportPanel = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$ImportExport = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0418\u043C\u043F\u043E\u0440\u0442 / \u042D\u043A\u0441\u043F\u043E\u0440\u0442 - PartTec Admin", "showSidebar": true }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "ImportExportPanel", ImportExportPanel, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/widgets/catalog/ImportExportPanel.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/import-export.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/import-export.astro";
const $$url = "/admin/import-export";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$ImportExport,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
