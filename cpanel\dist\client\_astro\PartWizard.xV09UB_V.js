import{V as P4,a as M4,b as q4}from"./Tab.DSDMNzbv.js";import{u as gu}from"./useTrpc.CcBnDuWb.js";import{u as Ku,C as Hu,T as Wu}from"./ConfirmDialog.Cn649_Oc.js";import Gu from"./Card.DllwvXut.js";import fu from"./Button.CplYapw1.js";import{I as bu}from"./InputText.CPqCR4in.js";import{V as vu}from"./AutoComplete.WqgqstcD.js";import{V as Qu}from"./Message.DXe4eGzY.js";import{D as Xu}from"./Dialog.DjvB895c.js";import{V as Ju}from"./Textarea.C8hcWg9_.js";import{_ as au,p as L4}from"./utils.BWEB-mtU.js";import{d as nu,g as I,o as l,w as C,a as n,b as B,e as p,c as m,f as _,h as J,l as Yu,B as Zu,x as Ru,p as Iu,q as Y,j as pu,m as $,u as Fu,v as mu,$ as U4,F as O,r as z,i as $u,k as O4,n as z4}from"./index.CpC-7sc3.js";import{n as u4,t as y,r as D,b as j4}from"./reactivity.esm-bundler.Bx7uHohy.js";import{A as e4,Q as t4,F as R4}from"./QuickCreateBrand.ZBe-Tlwy.js";import{R as a4,f as n4}from"./index.CMLtULFQ.js";import{a as l4}from"./index.DqILEIKx.js";import{P as o4}from"./trash.D7SMYTt1.js";import{T as N4}from"./Tag.BtN2Bjhy.js";import{S as K4}from"./Select.B5f7pqRM.js";import{I as H4}from"./Icon.DGPcirKX.js";import{D as r4}from"./DangerButton.DyBZF5lv.js";import{c as W4}from"./createLucideIcon.CxvjiKko.js";import{f as Nu,r as G4}from"./utils.D8YPi1lO.js";import"./index.B3SmZZpj.js";import"./index.DUcQAuYR.js";import"./trpc.CMxyjkwB.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";import"./index.enLFHxHc.js";import"./index.DV5zenSI.js";import"./SecondaryButton.ImrBLtmY.js";import"./index.Cl2VmfYg.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.CwY1vywt.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./index.C2Xch34u.js";import"./AttributeValueInput.CrEMZkDz.js";import"./InputNumber.8Wyucp72.js";import"./index.hiPlcmdl.js";import"./Checkbox.Czip7_Ii.js";import"./index.CyH7ziOX.js";/* empty css                            *//**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s4=W4("unlink",[["path",{d:"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71",key:"yqzxt4"}],["path",{d:"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71",key:"4qinb0"}],["line",{x1:"8",x2:"8",y1:"2",y2:"5",key:"1041cp"}],["line",{x1:"2",x2:"5",y1:"8",y2:"8",key:"14m1p5"}],["line",{x1:"16",x2:"16",y1:"19",y2:"22",key:"rzdirn"}],["line",{x1:"19",x2:"22",y1:"16",y2:"16",key:"ox905f"}]]),Q4=nu({__name:"QuickCreateCategory",props:{visible:{type:Boolean}},emits:["update:visible","created"],setup(a,{expose:e,emit:o}){e();const u=a,E=o,{loading:f,error:b,clearError:t,partCategories:c}=gu(),s=J({get:()=>u.visible,set:k=>E("update:visible",k)}),x=D({name:"",description:"",parent:null}),T=D({name:""}),U=D([]),j=J(()=>x.value.name.trim().length>0&&!f.value),q=async k=>{const M=k.query.toLowerCase(),R=await c.findMany({where:{name:{contains:M,mode:"insensitive"}},take:10});R&&(U.value=R)},P=async()=>{if(t(),T.value={name:""},!x.value.name.trim()){T.value.name="Название обязательно";return}try{const k=x.value.name.toLowerCase().replace(/[^a-zа-я0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),M={name:x.value.name.trim(),slug:k,description:x.value.description?.trim()||void 0,level:x.value.parent?x.value.parent.level+1:0,path:x.value.parent?`${x.value.parent.path}/${k}`:`/${k}`,parentId:x.value.parent?.id||void 0},R=await c.create({data:M});R&&(E("created",R),V(),s.value=!1)}catch(k){console.error("Ошибка создания категории:",k)}},V=()=>{x.value={name:"",description:"",parent:null},T.value={name:""},t()};Yu(s,k=>{k||V()});const w={props:u,emit:E,loading:f,error:b,clearError:t,partCategories:c,visible:s,formData:x,errors:T,parentSuggestions:U,canCreate:j,searchParents:q,createCategory:P,resetForm:V,VDialog:Xu,VButton:fu,VInputText:bu,VTextarea:Ju,VAutoComplete:vu,VMessage:Qu};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}}),X4={class:"space-y-4"},J4={key:0,class:"text-red-500"},Y4={class:"flex justify-end gap-3"};function Z4(a,e,o,u,E,f){return l(),I(u.VDialog,{visible:u.visible,"onUpdate:visible":e[4]||(e[4]=b=>u.visible=b),modal:"",header:"Создать новую категорию",class:"w-96"},{footer:C(()=>[n("div",Y4,[p(u.VButton,{onClick:e[3]||(e[3]=b=>u.visible=!1),severity:"secondary",outlined:"",disabled:u.loading},{default:C(()=>e[8]||(e[8]=[_(" Отмена ")])),_:1,__:[8]},8,["disabled"]),p(u.VButton,{onClick:u.createCategory,loading:u.loading,disabled:!u.canCreate},{default:C(()=>e[9]||(e[9]=[_(" Создать ")])),_:1,__:[9]},8,["loading","disabled"])])]),default:C(()=>[n("div",X4,[n("div",null,[e[5]||(e[5]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название категории * ",-1)),p(u.VInputText,{modelValue:u.formData.name,"onUpdate:modelValue":e[0]||(e[0]=b=>u.formData.name=b),placeholder:"Например: Фильтры масляные",class:u4(["w-full",{"p-invalid":u.errors.name}])},null,8,["modelValue","class"]),u.errors.name?(l(),m("small",J4,y(u.errors.name),1)):B("",!0)]),n("div",null,[e[6]||(e[6]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),p(u.VTextarea,{modelValue:u.formData.description,"onUpdate:modelValue":e[1]||(e[1]=b=>u.formData.description=b),placeholder:"Описание категории",rows:"3",class:"w-full"},null,8,["modelValue"])]),n("div",null,[e[7]||(e[7]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Родительская категория ",-1)),p(u.VAutoComplete,{modelValue:u.formData.parent,"onUpdate:modelValue":e[2]||(e[2]=b=>u.formData.parent=b),suggestions:u.parentSuggestions,onComplete:u.searchParents,"option-label":"name",placeholder:"Поиск родительской категории",class:"w-full"},null,8,["modelValue","suggestions"])])]),u.error?(l(),I(u.VMessage,{key:0,severity:"error",class:"mt-4"},{default:C(()=>[_(y(u.error),1)]),_:1})):B("",!0)]),_:1},8,["visible"])}const $4=au(Q4,[["render",Z4]]);var u0=`
    .p-togglebutton {
        display: inline-flex;
        cursor: pointer;
        user-select: none;
        overflow: hidden;
        position: relative;
        color: dt('togglebutton.color');
        background: dt('togglebutton.background');
        border: 1px solid dt('togglebutton.border.color');
        padding: dt('togglebutton.padding');
        font-size: 1rem;
        font-family: inherit;
        font-feature-settings: inherit;
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
        border-radius: dt('togglebutton.border.radius');
        outline-color: transparent;
        font-weight: dt('togglebutton.font.weight');
    }

    .p-togglebutton-content {
        display: inline-flex;
        flex: 1 1 auto;
        align-items: center;
        justify-content: center;
        gap: dt('togglebutton.gap');
        padding: dt('togglebutton.content.padding');
        background: transparent;
        border-radius: dt('togglebutton.content.border.radius');
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
        background: dt('togglebutton.hover.background');
        color: dt('togglebutton.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked {
        background: dt('togglebutton.checked.background');
        border-color: dt('togglebutton.checked.border.color');
        color: dt('togglebutton.checked.color');
    }

    .p-togglebutton-checked .p-togglebutton-content {
        background: dt('togglebutton.content.checked.background');
        box-shadow: dt('togglebutton.content.checked.shadow');
    }

    .p-togglebutton:focus-visible {
        box-shadow: dt('togglebutton.focus.ring.shadow');
        outline: dt('togglebutton.focus.ring.width') dt('togglebutton.focus.ring.style') dt('togglebutton.focus.ring.color');
        outline-offset: dt('togglebutton.focus.ring.offset');
    }

    .p-togglebutton.p-invalid {
        border-color: dt('togglebutton.invalid.border.color');
    }

    .p-togglebutton:disabled {
        opacity: 1;
        cursor: default;
        background: dt('togglebutton.disabled.background');
        border-color: dt('togglebutton.disabled.border.color');
        color: dt('togglebutton.disabled.color');
    }

    .p-togglebutton-label,
    .p-togglebutton-icon {
        position: relative;
        transition: none;
    }

    .p-togglebutton-icon {
        color: dt('togglebutton.icon.color');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
        color: dt('togglebutton.icon.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
        color: dt('togglebutton.icon.checked.color');
    }

    .p-togglebutton:disabled .p-togglebutton-icon {
        color: dt('togglebutton.icon.disabled.color');
    }

    .p-togglebutton-sm {
        padding: dt('togglebutton.sm.padding');
        font-size: dt('togglebutton.sm.font.size');
    }

    .p-togglebutton-sm .p-togglebutton-content {
        padding: dt('togglebutton.content.sm.padding');
    }

    .p-togglebutton-lg {
        padding: dt('togglebutton.lg.padding');
        font-size: dt('togglebutton.lg.font.size');
    }

    .p-togglebutton-lg .p-togglebutton-content {
        padding: dt('togglebutton.content.lg.padding');
    }

    .p-togglebutton-fluid {
        width: 100%;
    }
`,e0={root:function(e){var o=e.instance,u=e.props;return["p-togglebutton p-component",{"p-togglebutton-checked":o.active,"p-invalid":o.$invalid,"p-togglebutton-fluid":u.fluid,"p-togglebutton-sm p-inputfield-sm":u.size==="small","p-togglebutton-lg p-inputfield-lg":u.size==="large"}]},content:"p-togglebutton-content",icon:"p-togglebutton-icon",label:"p-togglebutton-label"},t0=Zu.extend({name:"togglebutton",style:u0,classes:e0}),a0={name:"BaseToggleButton",extends:l4,props:{onIcon:String,offIcon:String,onLabel:{type:String,default:"Yes"},offLabel:{type:String,default:"No"},iconPos:{type:String,default:"left"},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:t0,provide:function(){return{$pcToggleButton:this,$parentInstance:this}}};function tu(a){"@babel/helpers - typeof";return tu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tu(a)}function n0(a,e,o){return(e=l0(e))in a?Object.defineProperty(a,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[e]=o,a}function l0(a){var e=o0(a,"string");return tu(e)=="symbol"?e:e+""}function o0(a,e){if(tu(a)!="object"||!a)return a;var o=a[Symbol.toPrimitive];if(o!==void 0){var u=o.call(a,e);if(tu(u)!="object")return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(a)}var i4={name:"ToggleButton",extends:a0,inheritAttrs:!1,emits:["change"],methods:{getPTOptions:function(e){var o=e==="root"?this.ptmi:this.ptm;return o(e,{context:{active:this.active,disabled:this.disabled}})},onChange:function(e){!this.disabled&&!this.readonly&&(this.writeValue(!this.d_value,e),this.$emit("change",e))},onBlur:function(e){var o,u;(o=(u=this.formField).onBlur)===null||o===void 0||o.call(u,e)}},computed:{active:function(){return this.d_value===!0},hasLabel:function(){return Ru(this.onLabel)&&Ru(this.offLabel)},label:function(){return this.hasLabel?this.d_value?this.onLabel:this.offLabel:" "},dataP:function(){return n4(n0({checked:this.active,invalid:this.$invalid},this.size,this.size))}},directives:{ripple:a4}},r0=["tabindex","disabled","aria-pressed","aria-label","aria-labelledby","data-p-checked","data-p-disabled","data-p"],s0=["data-p"];function i0(a,e,o,u,E,f){var b=Iu("ripple");return Y((l(),m("button",$({type:"button",class:a.cx("root"),tabindex:a.tabindex,disabled:a.disabled,"aria-pressed":a.d_value,onClick:e[0]||(e[0]=function(){return f.onChange&&f.onChange.apply(f,arguments)}),onBlur:e[1]||(e[1]=function(){return f.onBlur&&f.onBlur.apply(f,arguments)})},f.getPTOptions("root"),{"aria-label":a.ariaLabel,"aria-labelledby":a.ariaLabelledby,"data-p-checked":f.active,"data-p-disabled":a.disabled,"data-p":f.dataP}),[n("span",$({class:a.cx("content")},f.getPTOptions("content"),{"data-p":f.dataP}),[pu(a.$slots,"default",{},function(){return[pu(a.$slots,"icon",{value:a.d_value,class:u4(a.cx("icon"))},function(){return[a.onIcon||a.offIcon?(l(),m("span",$({key:0,class:[a.cx("icon"),a.d_value?a.onIcon:a.offIcon]},f.getPTOptions("icon")),null,16)):B("",!0)]}),n("span",$({class:a.cx("label")},f.getPTOptions("label")),y(f.label),17)]})],16,s0)],16,r0)),[[b]])}i4.render=i0;var d0=`
    .p-selectbutton {
        display: inline-flex;
        user-select: none;
        vertical-align: bottom;
        outline-color: transparent;
        border-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton {
        border-radius: 0;
        border-width: 1px 1px 1px 0;
    }

    .p-selectbutton .p-togglebutton:focus-visible {
        position: relative;
        z-index: 1;
    }

    .p-selectbutton .p-togglebutton:first-child {
        border-inline-start-width: 1px;
        border-start-start-radius: dt('selectbutton.border.radius');
        border-end-start-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton:last-child {
        border-start-end-radius: dt('selectbutton.border.radius');
        border-end-end-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton.p-invalid {
        outline: 1px solid dt('selectbutton.invalid.border.color');
        outline-offset: 0;
    }

    .p-selectbutton-fluid {
        width: 100%;
    }
    
    .p-selectbutton-fluid .p-togglebutton {
        flex: 1 1 0;
    }
`,c0={root:function(e){var o=e.props,u=e.instance;return["p-selectbutton p-component",{"p-invalid":u.$invalid,"p-selectbutton-fluid":o.fluid}]}},m0=Zu.extend({name:"selectbutton",style:d0,classes:c0}),p0={name:"BaseSelectButton",extends:l4,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,multiple:Boolean,allowEmpty:{type:Boolean,default:!0},dataKey:null,ariaLabelledby:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:m0,provide:function(){return{$pcSelectButton:this,$parentInstance:this}}};function g0(a,e){var o=typeof Symbol<"u"&&a[Symbol.iterator]||a["@@iterator"];if(!o){if(Array.isArray(a)||(o=d4(a))||e){o&&(a=o);var u=0,E=function(){};return{s:E,n:function(){return u>=a.length?{done:!0}:{done:!1,value:a[u++]}},e:function(s){throw s},f:E}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var f,b=!0,t=!1;return{s:function(){o=o.call(a)},n:function(){var s=o.next();return b=s.done,s},e:function(s){t=!0,f=s},f:function(){try{b||o.return==null||o.return()}finally{if(t)throw f}}}}function f0(a){return E0(a)||v0(a)||d4(a)||b0()}function b0(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function d4(a,e){if(a){if(typeof a=="string")return Au(a,e);var o={}.toString.call(a).slice(8,-1);return o==="Object"&&a.constructor&&(o=a.constructor.name),o==="Map"||o==="Set"?Array.from(a):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Au(a,e):void 0}}function v0(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function E0(a){if(Array.isArray(a))return Au(a)}function Au(a,e){(e==null||e>a.length)&&(e=a.length);for(var o=0,u=Array(e);o<e;o++)u[o]=a[o];return u}var c4={name:"SelectButton",extends:p0,inheritAttrs:!1,emits:["change"],methods:{getOptionLabel:function(e){return this.optionLabel?mu(e,this.optionLabel):e},getOptionValue:function(e){return this.optionValue?mu(e,this.optionValue):e},getOptionRenderKey:function(e){return this.dataKey?mu(e,this.dataKey):this.getOptionLabel(e)},isOptionDisabled:function(e){return this.optionDisabled?mu(e,this.optionDisabled):!1},isOptionReadonly:function(e){if(this.allowEmpty)return!1;var o=this.isSelected(e);return this.multiple?o&&this.d_value.length===1:o},onOptionSelect:function(e,o,u){var E=this;if(!(this.disabled||this.isOptionDisabled(o)||this.isOptionReadonly(o))){var f=this.isSelected(o),b=this.getOptionValue(o),t;if(this.multiple)if(f){if(t=this.d_value.filter(function(c){return!Fu(c,b,E.equalityKey)}),!this.allowEmpty&&t.length===0)return}else t=this.d_value?[].concat(f0(this.d_value),[b]):[b];else{if(f&&!this.allowEmpty)return;t=f?null:b}this.writeValue(t,e),this.$emit("change",{event:e,value:t})}},isSelected:function(e){var o=!1,u=this.getOptionValue(e);if(this.multiple){if(this.d_value){var E=g0(this.d_value),f;try{for(E.s();!(f=E.n()).done;){var b=f.value;if(Fu(b,u,this.equalityKey)){o=!0;break}}}catch(t){E.e(t)}finally{E.f()}}}else o=Fu(this.d_value,u,this.equalityKey);return o}},computed:{equalityKey:function(){return this.optionValue?null:this.dataKey},dataP:function(){return n4({invalid:this.$invalid})}},directives:{ripple:a4},components:{ToggleButton:i4}},y0=["aria-labelledby","data-p"];function C0(a,e,o,u,E,f){var b=U4("ToggleButton");return l(),m("div",$({class:a.cx("root"),role:"group","aria-labelledby":a.ariaLabelledby},a.ptmi("root"),{"data-p":f.dataP}),[(l(!0),m(O,null,z(a.options,function(t,c){return l(),I(b,{key:f.getOptionRenderKey(t),modelValue:f.isSelected(t),onLabel:f.getOptionLabel(t),offLabel:f.getOptionLabel(t),disabled:a.disabled||f.isOptionDisabled(t),unstyled:a.unstyled,size:a.size,readonly:f.isOptionReadonly(t),onChange:function(x){return f.onOptionSelect(x,t,c)},pt:a.ptm("pcToggleButton")},$u({_:2},[a.$slots.option?{name:"default",fn:C(function(){return[pu(a.$slots,"option",{option:t,index:c},function(){return[n("span",$({ref_for:!0},a.ptm("pcToggleButton").label),y(f.getOptionLabel(t)),17)]})]}),key:"0"}:void 0]),1032,["modelValue","onLabel","offLabel","disabled","unstyled","size","readonly","onChange","pt"])}),128))],16,y0)}c4.render=C0;const x0=nu({__name:"SelectButton",setup(a,{expose:e}){e();const u={theme:D({root:`inline-flex select-none rounded-md
        p-invalid:outline p-invalid:outline-offset-0 p-invalid:outline-red-400 dark:p-invalid:outline-red-300`,pcToggleButton:{root:`inline-flex items-center justify-center overflow-hidden relative cursor-pointer select-none grow
            border border-surface-100 dark:border-surface-950
            rounded-none first:rounded-s-md last:rounded-e-md
            bg-surface-100 dark:bg-surface-950
            text-surface-500 dark:text-surface-400
            p-checked:text-surface-700 dark:p-checked:text-surface-0
            text-base font-medium
            focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary focus-visible:relative focus-visible:z-10
            disabled:cursor-default
            disabled:bg-surface-200 disabled:border-surface-200 disabled:text-surface-500
            disabled:dark:bg-surface-700 disabled:dark:border-surface-700 disabled:dark:text-surface-400
            p-invalid:border-red-400 dark:p-invalid:border-red-300
            transition-colors duration-200
            p-1 p-small:text-sm p-large:text-lg
        `,content:`relative flex-auto inline-flex items-center justify-center gap-2 py-1 px-3
            rounded-md transition-colors duration-200
            p-checked:bg-surface-0 dark:p-checked:bg-surface-800 p-checked:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.02),0px_1px_2px_0px_rgba(0,0,0,0.04)]`,icon:"",label:""}}),get SelectButton(){return c4},get ptViewMerge(){return L4}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}});function h0(a,e,o,u,E,f){return l(),I(u.SelectButton,{unstyled:"",pt:u.theme,ptOptions:{mergeProps:u.ptViewMerge}},$u({_:2},[z(a.$slots,(b,t)=>({name:t,fn:C(c=>[pu(a.$slots,t,j4(O4(c??{})))])}))]),1032,["pt","ptOptions"])}const m4=au(x0,[["render",h0]]),B0=nu({__name:"EquipmentSelector",props:{modelValue:{}},emits:["update:modelValue"],setup(a,{expose:e,emit:o}){e();const u=a,E=o,{equipmentModels:f,brands:b}=gu(),t=[{label:"Создать новую модель",value:!1},{label:"Найти существующую",value:!0}],c=D([]),s=D([]),x=J({get:()=>u.modelValue,set:V=>E("update:modelValue",V)}),P={props:u,emit:E,equipmentModels:f,brands:b,equipmentTypeOptions:t,equipmentSuggestions:c,brandSuggestions:s,modelValue:x,searchEquipmentModels:async(V,w)=>{const k=V.query.toLowerCase(),M=await f.findMany({where:{name:{contains:k,mode:"insensitive"}},include:{brand:!0},take:10});M&&Array.isArray(M)&&(c.value=M)},searchBrands:async V=>{const w=V.query.toLowerCase(),k=await b.findMany({where:{name:{contains:w,mode:"insensitive"}},take:10});k&&Array.isArray(k)&&(s.value=k)},addEquipment:()=>{const V={name:"",selectedBrand:null,isExisting:!1,existingEquipmentModel:null,notes:""};x.value=[...x.value,V]},removeEquipment:V=>{const w=[...x.value];w.splice(V,1),x.value=w},VButton:fu,VInputText:bu,VAutoComplete:vu,VSelectButton:m4,get PlusIcon(){return o4}};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}}),D0={class:"equipment-selector"},k0={class:"flex items-center justify-between mb-4"},_0={key:0,class:"text-center py-8 text-surface-500"},F0={class:"flex items-center justify-between mb-4"},A0={class:"font-medium text-surface-900 dark:text-surface-0"},I0={class:"mb-4"},V0={key:0,class:"mb-4"},w0={class:"flex items-center gap-2"},S0={class:"flex-1"},T0={class:"font-medium"},P0={key:0,class:"text-sm text-surface-600"},M0={key:1,class:"space-y-4"},q0={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},L0={class:"mt-4"};function U0(a,e,o,u,E,f){return l(),m("div",D0,[n("div",k0,[e[1]||(e[1]=n("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Применимость к технике ",-1)),p(u.VButton,{onClick:u.addEquipment,outlined:"",size:"small"},{default:C(()=>[e[0]||(e[0]=_(" Добавить технику ")),p(u.PlusIcon,{class:"h-4 w-4"})]),_:1,__:[0]})]),u.modelValue.length===0?(l(),m("div",_0," Добавьте модели техники, к которым применима эта запчасть ")):B("",!0),(l(!0),m(O,null,z(u.modelValue,(b,t)=>(l(),m("div",{key:t,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4 mb-4"},[n("div",F0,[n("h4",A0," Техника "+y(t+1),1),p(u.VButton,{onClick:c=>u.removeEquipment(t),severity:"danger",size:"small",text:""},{default:C(()=>e[2]||(e[2]=[_(" Удалить ")])),_:2,__:[2]},1032,["onClick"])]),n("div",I0,[e[3]||(e[3]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип добавления ",-1)),p(u.VSelectButton,{modelValue:b.isExisting,"onUpdate:modelValue":c=>b.isExisting=c,options:u.equipmentTypeOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),b.isExisting?(l(),m("div",V0,[e[4]||(e[4]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск модели техники * ",-1)),p(u.VAutoComplete,{modelValue:b.existingEquipmentModel,"onUpdate:modelValue":c=>b.existingEquipmentModel=c,suggestions:u.equipmentSuggestions,onComplete:c=>u.searchEquipmentModels(c,t),"option-label":"name",placeholder:"Введите название модели для поиска...",class:"w-full",dropdown:""},{option:C(({option:c})=>[n("div",w0,[n("div",S0,[n("div",T0,y(c.name),1),c.brand?(l(),m("div",P0,y(c.brand.name),1)):B("",!0)])])]),_:2},1032,["modelValue","onUpdate:modelValue","suggestions","onComplete"])])):(l(),m("div",M0,[n("div",q0,[n("div",null,[e[5]||(e[5]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название модели * ",-1)),p(u.VInputText,{modelValue:b.name,"onUpdate:modelValue":c=>b.name=c,placeholder:"Например: CAT 320D",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])]),n("div",null,[e[6]||(e[6]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд ",-1)),p(u.VAutoComplete,{modelValue:b.selectedBrand,"onUpdate:modelValue":c=>b.selectedBrand=c,suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:"w-full",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"])])])])),n("div",L0,[e[7]||(e[7]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),p(u.VInputText,{modelValue:b.notes,"onUpdate:modelValue":c=>b.notes=c,placeholder:"Дополнительная информация о применимости к данной технике",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])}const O0=au(B0,[["render",U0]]),z0=nu({__name:"CatalogItemWizardEditor",props:{modelValue:{},mode:{default:"create"},partId:{default:void 0}},emits:["update:modelValue"],setup(a,{expose:e,emit:o}){e();const u=a,E=o,{catalogItems:f,brands:b,partApplicability:t,client:c}=gu(),s=Ku(),x=D([]),T=D([]),U=D(!1),j=D(!1),q=D(-1),P=D([]),su={props:u,emit:E,catalogItems:f,brands:b,partApplicability:t,client:c,confirm:s,catalogItemSuggestions:x,brandSuggestions:T,showCreateBrand:U,showAttributeManager:j,currentItemIndex:q,currentItemAttributes:P,accuracyOptions:[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требует доработки",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],itemTypeOptions:[{label:"Создать новую позицию",value:!1},{label:"Выбрать существующую",value:!0}],addCatalogItem:()=>{const g={sku:"",brandId:"",selectedBrand:null,description:"",attributes:[],isExisting:!1,existingCatalogItem:null,accuracy:"EXACT_MATCH",notes:"",applicabilityId:void 0},h=[...u.modelValue,g];E("update:modelValue",h)},removeCatalogItem:g=>{const h=u.modelValue.filter((F,S)=>S!==g);E("update:modelValue",h)},getDisplayLabel:g=>g?typeof g=="object"?`${g.sku} (${g.brand?.name||"Без бренда"})`:g:"",onItemSelect:(g,h)=>{const F=[...u.modelValue];typeof h=="object"&&(F[g].existingCatalogItem=h,F[g].applicabilityId=void 0),E("update:modelValue",F)},searchCatalogItems:async g=>{try{const h=g.query.toLowerCase(),F=await f.findMany({where:{OR:[{sku:{contains:h,mode:"insensitive"}},{brand:{name:{contains:h,mode:"insensitive"}}}]},include:{brand:!0},take:10});F&&(x.value=F.map(S=>({...S,displayLabel:`${S.sku} (${S.brand?.name||"Без бренда"})`})))}catch(h){console.error("Ошибка поиска каталожных позиций:",h)}},searchBrands:async g=>{try{const h=g.query.toLowerCase(),F=await b.findMany({where:{name:{contains:h,mode:"insensitive"}},take:10});F&&(T.value=F)}catch(h){console.error("Ошибка поиска брендов:",h)}},onBrandCreated:g=>{T.value=[g,...T.value]},openAttributeManager:g=>{q.value=g,P.value=[...u.modelValue[g]?.attributes||[]],j.value=!0},closeAttributeManager:()=>{if(q.value>=0){const g=[...u.modelValue];g[q.value].attributes=[...P.value],E("update:modelValue",g)}j.value=!1,q.value=-1,P.value=[]},removeAttribute:(g,h)=>{const F=[...u.modelValue];F[g].attributes&&(F[g].attributes.splice(h,1),E("update:modelValue",F))},getUnitLabel:g=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[g]||g,unlinkCatalogItem:async(g,h)=>{if(!g.applicabilityId){console.warn("Нет ID применимости для отвязки");return}const F=g.isExisting?`${g.existingCatalogItem?.sku} (${g.existingCatalogItem?.brand?.name})`:`${g.sku} (${g.selectedBrand?.name})`;s.show({header:"Отвязать каталожную позицию?",message:`Вы уверены, что хотите отвязать позицию "${F}" от этой запчасти? Сама позиция останется в системе.`,icon:"pi pi-link-slash",acceptLabel:"Отвязать",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:async()=>{try{await t.delete({where:{id:g.applicabilityId}});const S=u.modelValue.filter((K,H)=>H!==h);E("update:modelValue",S),console.log("Каталожная позиция успешно отвязана")}catch(S){console.error("Ошибка отвязки каталожной позиции:",S)}}})},deleteCatalogItem:async(g,h)=>{const F=g.isExisting?`${g.existingCatalogItem?.sku} (${g.existingCatalogItem?.brand?.name})`:`${g.sku} (${g.selectedBrand?.name})`,S=g.isExisting?g.existingCatalogItem?.id:null;if(!S){console.warn("Нет ID каталожной позиции для удаления");return}s.show({header:"Удалить каталожную позицию?",message:`Вы уверены, что хотите ПОЛНОСТЬЮ УДАЛИТЬ позицию "${F}" из системы? Это действие необратимо и может повлиять на другие запчасти, которые связаны с этой позицией.`,icon:"pi pi-trash",acceptLabel:"Удалить",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:async()=>{try{g.applicabilityId&&await t.delete({where:{id:g.applicabilityId}}),await f.delete({where:{id:S}});const K=u.modelValue.filter((H,yu)=>yu!==h);E("update:modelValue",K),console.log("Каталожная позиция успешно удалена")}catch(K){console.error("Ошибка удаления каталожной позиции:",K),(K?.message?.includes("Foreign key constraint")||K?.message?.includes("violates foreign key")||K?.code==="P2003")&&console.error(`Невозможно удалить каталожную позицию "${F}". Она используется в других запчастях или заказах.`)}}})},VCard:Gu,VButton:fu,VTag:N4,VSelectButton:m4,VAutoComplete:vu,VInputText:bu,VTextarea:Ju,VSelect:K4,VDialog:Xu,VConfirmDialog:Hu,QuickCreateBrand:t4,AttributeManager:e4,Icon:H4,get PlusIcon(){return o4},get UnlinkIcon(){return s4},get Trash2Icon(){return Wu},DangerButton:r4};return Object.defineProperty(su,"__isScriptSetup",{enumerable:!1,value:!0}),su}}),j0={class:"catalog-item-wizard-editor"},R0={class:"flex items-center justify-between mb-4"},N0={key:0,class:"text-center py-10"},K0={key:1,class:"space-y-4"},H0={class:"p-4"},W0={class:"flex items-center justify-between mb-4"},G0={class:"flex items-center gap-3"},Q0={class:"flex gap-2"},X0={class:"mb-4"},J0={key:0,class:"mb-4"},Y0={class:"flex items-center gap-2"},Z0={class:"font-mono font-medium"},$0={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},ue={class:"flex gap-2"},ee={class:"md:col-span-2"},te={key:2,class:"mb-4"},ae={class:"flex items-center justify-between mb-3"},ne={key:0,class:"space-y-2"},le={class:"flex-1"},oe={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},re={key:0,class:"text-red-500 ml-1"},se={class:"text-sm text-surface-600 dark:text-surface-400"},ie={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},de={key:1,class:"text-center py-4 text-surface-500 text-sm border border-dashed rounded"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function me(a,e,o,u,E,f){const b=Iu("tooltip");return l(),m("div",j0,[n("div",R0,[e[5]||(e[5]=n("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Каталожные позиции ",-1)),p(u.VButton,{onClick:u.addCatalogItem,outlined:"",size:"small"},{default:C(()=>[e[4]||(e[4]=_(" Добавить ")),p(u.PlusIcon)]),_:1,__:[4]})]),o.modelValue.length===0?(l(),m("div",N0,[e[7]||(e[7]=n("div",{class:"text-surface-600 dark:text-surface-400 mb-4"}," Вы можете добавить каталожные позиции сейчас или пропустить этот шаг. ",-1)),p(u.VButton,{size:"small",onClick:u.addCatalogItem,outlined:""},{default:C(()=>[e[6]||(e[6]=_(" Добавить ")),p(u.PlusIcon)]),_:1,__:[6]})])):(l(),m("div",K0,[(l(!0),m(O,null,z(o.modelValue,(t,c)=>(l(),I(u.VCard,{key:c,class:"border border-surface-200 dark:border-surface-700"},{content:C(()=>[n("div",H0,[n("div",W0,[n("div",G0,[p(u.VTag,{value:`Позиция ${c+1}`,severity:"secondary",size:"small"},null,8,["value"]),t.isExisting?(l(),I(u.VTag,{key:0,value:"Существующая",severity:"info",size:"small"})):(l(),I(u.VTag,{key:1,value:"Новая",severity:"success",size:"small"}))]),n("div",Q0,[o.mode==="edit"&&t.isExisting&&t.applicabilityId?(l(),m(O,{key:0},[Y((l(),I(u.DangerButton,{onClick:s=>u.unlinkCatalogItem(t,c),severity:"secondary",outlined:"",size:"small"},{default:C(()=>[e[8]||(e[8]=_(" Отвязать ")),p(u.UnlinkIcon,{class:"w-4 h-4"})]),_:2,__:[8]},1032,["onClick"])),[[b,"Отвязать от запчасти"]]),Y((l(),I(u.DangerButton,{onClick:s=>u.deleteCatalogItem(t,c),severity:"danger",outlined:"",size:"small"},{default:C(()=>[e[9]||(e[9]=_(" Удалить ")),p(u.Trash2Icon,{class:"w-4 h-4"})]),_:2,__:[9]},1032,["onClick"])),[[b,"Удалить каталожную позицию"]])],64)):B("",!0),Y((l(),I(u.VButton,{onClick:s=>u.removeCatalogItem(c),severity:"danger",size:"small",text:""},{default:C(()=>[p(u.Icon,{name:"pi pi-times",class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[b,o.mode==="edit"&&t.isExisting?"Убрать из списка":"Удалить"]])])]),n("div",X0,[p(u.VSelectButton,{modelValue:t.isExisting,"onUpdate:modelValue":s=>t.isExisting=s,options:u.itemTypeOptions,optionLabel:"label",optionValue:"value",class:"w-full md:w-auto"},null,8,["modelValue","onUpdate:modelValue"])]),t.isExisting?(l(),m("div",J0,[e[10]||(e[10]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск каталожной позиции * ",-1)),p(u.VAutoComplete,{"model-value":u.getDisplayLabel(t.existingCatalogItem),"onUpdate:modelValue":s=>u.onItemSelect(c,s),suggestions:u.catalogItemSuggestions,onComplete:u.searchCatalogItems,field:"displayLabel",placeholder:"Поиск по артикулу или бренду...",class:"w-full",dropdown:""},{option:C(({option:s})=>[n("div",Y0,[n("span",Z0,y(s.sku),1),p(u.VTag,{value:s.brand?.name,severity:"secondary",size:"small"},null,8,["value"])])]),_:2},1032,["model-value","onUpdate:modelValue","suggestions"])])):(l(),m("div",$0,[n("div",null,[e[11]||(e[11]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),p(u.VInputText,{modelValue:t.sku,"onUpdate:modelValue":s=>t.sku=s,placeholder:"Например: 12345-ABC",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),n("div",null,[e[12]||(e[12]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),n("div",ue,[p(u.VAutoComplete,{modelValue:t.selectedBrand,"onUpdate:modelValue":s=>t.selectedBrand=s,suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name","force-selection":!0,placeholder:"Поиск бренда...",class:"flex-1",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"]),Y((l(),I(u.VButton,{onClick:e[0]||(e[0]=s=>u.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:C(()=>[p(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})),[[b,"Создать новый бренд"]])])]),n("div",ee,[e[13]||(e[13]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),p(u.VInputText,{modelValue:t.description,"onUpdate:modelValue":s=>t.description=s,placeholder:"Описание каталожной позиции...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])),t.isExisting?B("",!0):(l(),m("div",te,[n("div",ae,[e[14]||(e[14]=n("h4",{class:"text-md font-medium text-surface-900 dark:text-surface-0"}," Атрибуты каталожной позиции ",-1)),p(u.VButton,{onClick:s=>u.openAttributeManager(c),severity:"secondary",outlined:"",size:"small",label:"Добавить атрибут"},{icon:C(()=>[p(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:2},1032,["onClick"])]),t.attributes&&t.attributes.length>0?(l(),m("div",ne,[(l(!0),m(O,null,z(t.attributes,(s,x)=>(l(),m("div",{key:x,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[n("div",le,[n("div",oe,[_(y(s.template?.title||s.templateTitle)+" ",1),s.template?.isRequired?(l(),m("span",re,"*")):B("",!0)]),n("div",se,[_(y(s.value)+" "+y(s.template?.unit?u.getUnitLabel(s.template.unit):"")+" ",1),s.template?.group?.name?(l(),m("span",ie,y(s.template.group.name),1)):B("",!0)])]),p(u.VButton,{onClick:T=>u.removeAttribute(c,x),severity:"danger",size:"small",text:""},{default:C(()=>[p(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]))),128))])):(l(),m("div",de," Атрибуты не добавлены "))])),n("div",ce,[n("div",null,[e[15]||(e[15]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность применимости * ",-1)),p(u.VSelect,{modelValue:t.accuracy,"onUpdate:modelValue":s=>t.accuracy=s,options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),n("div",null,[e[16]||(e[16]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),p(u.VTextarea,{modelValue:t.notes,"onUpdate:modelValue":s=>t.notes=s,"auto-resize":!0,rows:"2",placeholder:"Дополнительные примечания...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])])]),_:2},1024))),128))])),p(u.QuickCreateBrand,{visible:u.showCreateBrand,"onUpdate:visible":e[1]||(e[1]=t=>u.showCreateBrand=t),onCreated:u.onBrandCreated},null,8,["visible"]),p(u.VDialog,{visible:u.showAttributeManager,"onUpdate:visible":e[3]||(e[3]=t=>u.showAttributeManager=t),modal:"",header:"Управление атрибутами каталожной позиции",class:"w-auto flex"},{default:C(()=>[p(u.AttributeManager,{modelValue:u.currentItemAttributes,"onUpdate:modelValue":e[2]||(e[2]=t=>u.currentItemAttributes=t),title:"Атрибуты каталожной позиции",onClose:u.closeAttributeManager},null,8,["modelValue"])]),_:1},8,["visible"]),p(u.VConfirmDialog)])}const pe=au(z0,[["render",me]]),ge=nu({__name:"PartWizard",props:{part:{default:null},mode:{default:"create"}},emits:["created","updated"],setup(a,{expose:e,emit:o}){e();const u=a,E=o,f=[{title:"Основная информация",key:"basic"},{title:"Атрибуты",key:"attributes"},{title:"Каталожные позиции",key:"catalog"},{title:"Применимость к технике",key:"equipment"},{title:"Подтверждение",key:"confirm"}],b=f.length,t=D(1),c=J({get:()=>f[t.value-1]?.key,set:d=>{const i=f.findIndex(v=>v.key===d);i>=0&&(t.value=i+1)}}),s=D({name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]}),{loading:x,error:T,clearError:U,partCategories:j,parts:q,catalogItems:P,equipmentModels:V,partAttributes:w,client:k,partApplicability:M,equipmentApplicability:R,media:Z}=gu(),Eu=k.crud.catalogItemAttribute,lu=Ku(),N=D(null),uu=D([]),Vu=D(!1),wu=D(!1),ou=D(!1),Q=D(null),ru=D(u.part||null),su=J(()=>ru.value?.image?.url||null),g=D(null),h=D(!1),F=d=>{const i=d.target;i.files&&i.files[0]&&(g.value=i.files[0])},S=async()=>{if(!(!u.part?.id||!g.value))try{h.value=!0;const d=await Nu(g.value);await Z.uploadPartImage({partId:u.part.id,fileName:g.value.name,fileData:d,mimeType:g.value.type||"image/png"}),await eu(),g.value=null}finally{h.value=!1}},K=async()=>{if(u.part?.id)try{h.value=!0,await Z.deletePartImage({partId:u.part.id}),await eu()}finally{h.value=!1}},H=D(null),yu=d=>{const i=d.target;H.value=i.files||null},Cu=D(!1),p4=async()=>{if(!(!u.part?.id||!H.value||H.value.length===0))try{Cu.value=!0;for(const d of Array.from(H.value)){const i=await Nu(d);await Z.uploadPartMedia({partId:u.part.id,fileName:d.name,fileData:i,mimeType:d.type||"application/octet-stream"})}await eu(),H.value=null}finally{Cu.value=!1}},g4=async d=>{u.part?.id&&(await Z.removePartMedia({partId:u.part.id,mediaId:d}),await eu())},eu=async()=>{if(!u.part?.id)return;const d=await k.crud.part.findUnique.query({where:{id:u.part.id},include:{image:!0,mediaAssets:!0,partCategory:!0}});ru.value=d},f4=d=>{N.value=d,uu.value=[d,...uu.value]},b4=async d=>{const i=d.query.toLowerCase(),v=await j.findMany({where:{name:{contains:i,mode:"insensitive"}},take:10});v&&Array.isArray(v)&&(uu.value=v)},xu=d=>{switch(d){case 1:return!!(s.value.name.trim()&&N.value);case 2:return s.value.attributes.every(i=>{if(!!!(i.template?.isRequired||i.isRequired))return!0;const W=i.template?.dataType||i.templateDataType,L=i.value;return W==="BOOLEAN"?L!=null:String(L??"").trim().length>0});case 3:return s.value.catalogItems.every(i=>{if(i.isExisting)return!!(i.existingCatalogItem&&i.accuracy);const v=typeof i.selectedBrand=="object"&&!!i.selectedBrand?.id||typeof i.selectedBrand=="number"||typeof i.brandId=="number";return!!(String(i.sku||"").trim()&&v&&i.accuracy)});case 4:return s.value.equipmentApplicabilities.every(i=>i.isExisting?!!i.existingEquipmentModel:!!String(i.name||"").trim());case 5:return!0;default:return!1}},hu=J(()=>xu(t.value)),Su=J(()=>t.value===b&&[1,2,3,4].every(xu)),v4=()=>{t.value<b&&hu.value&&(t.value++,U())},E4=()=>{t.value>1&&(t.value--,U())},y4=d=>{if(typeof d!="string")return;const i=f.findIndex(v=>v.key===d)+1;i!==t.value&&(i>t.value&&!hu.value||(t.value=i,U()))},C4=async()=>{if(Su.value)try{ou.value=!0,Q.value=null,U();const d={name:s.value.name,partCategoryId:typeof N.value=="object"?N.value.id:N.value,level:u.part?.level||0,path:u.part?.path||"/"};let i=null;if(u.mode==="edit"&&u.part?i=await q.update({where:{id:u.part.id},data:d}):i=await q.create({data:d}),!i||!i.id)throw new Error(`Не удалось ${u.mode==="edit"?"обновить":"создать"} запчасть`);const v=i.id,W=new Map;for(const r of s.value.attributes)r.templateId&&W.set(r.templateId,r);const L=[],Du=[];W.forEach(r=>{const A=r.value;A==null||String(A).trim()===""||(r.id?Du.push({id:r.id,value:String(A).trim()}):L.push({templateId:r.templateId,value:String(A).trim()}))});const iu=[];L.length>0&&iu.push(w.bulkCreate({partId:v,attributes:L})),Du.length>0&&iu.push(Promise.all(Du.map(r=>w.update({id:r.id,value:r.value})))),iu.length>0&&await Promise.all(iu);const du=[],ku=[];for(const r of s.value.catalogItems)r.isExisting&&r.existingCatalogItem&&du.push({id:r.existingCatalogItem.id,accuracy:r.accuracy,notes:r.notes||void 0});const _4=s.value.catalogItems.filter(r=>!r.isExisting).map(async r=>{let A=null;if(typeof r.selectedBrand=="object"&&r.selectedBrand?.id?A=r.selectedBrand.id:typeof r.selectedBrand=="number"?A=r.selectedBrand:typeof r.brandId=="number"&&(A=r.brandId),!A)return ku.push(`Не выбран бренд для каталожной позиции ${r.sku}`),null;const G=await P.create({data:{sku:r.sku.toUpperCase().trim(),brandId:A,description:r.description||void 0,isPublic:!0}});if(!G||!G.id)return ku.push(`Не удалось создать каталожную позицию ${r.sku}`),null;if(r.attributes&&r.attributes.length>0)try{const _u=r.attributes.filter(X=>{const S4=X.value&&String(X.value).trim()!=="",T4=X.templateId||X.template?.id;return S4&&T4});for(const X of _u)await Eu.create.mutate({data:{value:String(X.value),catalogItem:{connect:{id:G.id}},template:{connect:{id:X.templateId||X.template?.id}}}})}catch(_u){console.error(`Ошибка создания атрибутов для каталожной позиции ${r.sku}:`,_u)}return{id:G.id,accuracy:r.accuracy,notes:r.notes||void 0}}),F4=await Promise.all(_4);for(const r of F4)r&&du.push(r);const Uu=[],A4=du.map(r=>M.upsert({where:{partId_catalogItemId:{partId:v,catalogItemId:r.id}},update:{accuracy:r.accuracy,notes:r.notes},create:{partId:v,catalogItemId:r.id,accuracy:r.accuracy,notes:r.notes}}));(await Promise.all(A4)).forEach((r,A)=>{r||Uu.push(`Связь с каталожной позицией #${du[A].id} не сохранена`)});const cu=[],Ou=[];for(const r of s.value.equipmentApplicabilities)r.isExisting&&r.existingEquipmentModel&&cu.push({id:r.existingEquipmentModel.id,notes:r.notes||void 0});const I4=s.value.equipmentApplicabilities.filter(r=>!r.isExisting).map(async r=>{const A={name:r.name.trim()};r.selectedBrand&&(A.brandId=typeof r.selectedBrand=="object"?r.selectedBrand.id:r.selectedBrand);const G=await V.create({data:A});return!G||!G.id?(Ou.push(`Не удалось создать модель техники ${r.name}`),null):{id:G.id,notes:r.notes||void 0}}),V4=await Promise.all(I4);for(const r of V4)r&&cu.push(r);const zu=[],w4=cu.map(r=>R.upsert({where:{partId_equipmentModelId:{partId:v,equipmentModelId:r.id}},update:{notes:r.notes},create:{partId:v,equipmentModelId:r.id,notes:r.notes}}));(await Promise.all(w4)).forEach((r,A)=>{r||zu.push(`Связь с техникой #${cu[A].id} не сохранена`)});const ju=[...ku,...Uu,...Ou,...zu].filter(Boolean);ju.length>0&&(Q.value=ju.join("; ")),u.mode==="edit"?E("updated",i):E("created",i),u.mode==="create"&&Tu()}catch(d){console.error(`Ошибка ${u.mode==="edit"?"обновления":"создания"} запчасти:`,d),Q.value=d?.message||"Произошла ошибка при сохранении запчасти"}finally{ou.value=!1}},Tu=()=>{s.value={name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]},N.value=null,t.value=1},x4=d=>d?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[d]||d:"",h4=d=>d?{MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[d]||d:"",B4=d=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[d]||d,Pu=async d=>{try{const i=await w.findByPartId({partId:d});i&&Array.isArray(i)&&(s.value.attributes=i.map(v=>({id:v.id,templateId:v.templateId,value:v.value,template:v.template,templateTitle:v.template?.title,templateDataType:v.template?.dataType,templateUnit:v.template?.unit,templateGroup:v.template?.group?.name,templateDescription:v.template?.description})))}catch(i){console.warn("Не удалось загрузить атрибуты запчасти:",i)}},Mu=async d=>{try{const i=await k.crud.partApplicability.findMany.query({where:{partId:d},include:{catalogItem:{include:{brand:!0}}}});i&&(s.value.catalogItems=i.map(v=>({isExisting:!0,existingCatalogItem:v.catalogItem,accuracy:v.accuracy,notes:v.notes||"",applicabilityId:v.id,sku:"",brandId:"",selectedBrand:null,description:""})))}catch(i){console.warn("Не удалось загрузить каталожные позиции:",i)}},qu=async d=>{try{const i=await k.crud.equipmentApplicability.findMany.query({where:{partId:d},include:{equipmentModel:{include:{brand:!0}}}});i&&(s.value.equipmentApplicabilities=i.map(v=>({isExisting:!0,existingEquipmentModel:v.equipmentModel,notes:v.notes||"",name:"",selectedBrand:null})))}catch(i){console.warn("Не удалось загрузить применимость к технике:",i)}},Bu=async()=>{u.part&&u.mode==="edit"&&(s.value.name=u.part.name||"",u.part.partCategory&&(N.value=u.part.partCategory),u.part.id&&await Promise.all([Pu(u.part.id),Mu(u.part.id),qu(u.part.id)]))};Yu(()=>u.part,async()=>{u.mode==="edit"&&await Bu()},{immediate:!0});const D4=async(d,i)=>{if(!d.applicabilityId){console.warn("Нет ID применимости для отвязки");return}const v=d.isExisting?`${d.existingCatalogItem?.sku} (${d.existingCatalogItem?.brand?.name})`:`${d.sku} (${d.selectedBrand?.name})`;lu.show({header:"Отвязать каталожную позицию?",message:`Вы уверены, что хотите отвязать позицию "${v}" от этой запчасти? Сама позиция останется в системе.`,icon:"pi pi-link-slash",acceptLabel:"Отвязать",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:async()=>{try{await M.delete({where:{id:d.applicabilityId}}),s.value.catalogItems.splice(i,1),console.log("Каталожная позиция успешно отвязана")}catch(W){console.error("Ошибка отвязки каталожной позиции:",W),Q.value="Не удалось отвязать каталожную позицию"}}})},k4=async(d,i)=>{const v=d.isExisting?`${d.existingCatalogItem?.sku} (${d.existingCatalogItem?.brand?.name})`:`${d.sku} (${d.selectedBrand?.name})`,W=d.isExisting?d.existingCatalogItem?.id:null;if(!W){console.warn("Нет ID каталожной позиции для удаления");return}lu.show({header:"Удалить каталожную позицию?",message:`Вы уверены, что хотите ПОЛНОСТЬЮ УДАЛИТЬ позицию "${v}" из системы? Это действие необратимо и может повлиять на другие запчасти, которые связаны с этой позицией.`,icon:"pi pi-trash",acceptLabel:"Удалить",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:async()=>{try{d.applicabilityId&&await M.delete({where:{id:d.applicabilityId}}),await P.delete({where:{id:W}}),s.value.catalogItems.splice(i,1),console.log("Каталожная позиция успешно удалена")}catch(L){console.error("Ошибка удаления каталожной позиции:",L),L?.message?.includes("Foreign key constraint")||L?.message?.includes("violates foreign key")||L?.code==="P2003"?Q.value=`Невозможно удалить каталожную позицию "${v}". Она используется в других запчастях или заказах. Попробуйте сначала отвязать позицию.`:Q.value="Не удалось удалить каталожную позицию"}}})};z4(async()=>{u.mode==="edit"&&u.part&&await Bu()});const Lu={props:u,emit:E,steps:f,totalSteps:b,currentStep:t,activeStepKey:c,formData:s,loading:x,error:T,clearError:U,partCategories:j,parts:q,catalogItems:P,equipmentModels:V,partAttributes:w,client:k,partApplicability:M,equipmentApplicability:R,media:Z,catalogItemAttributesApi:Eu,confirm:lu,selectedCategory:N,categorySuggestions:uu,showCreateCategory:Vu,showCreateBrand:wu,saving:ou,saveError:Q,currentPart:ru,partImageUrl:su,selectedFile:g,uploading:h,onSelectPartImage:F,uploadPartImage:S,removePartImage:K,selectedMediaFiles:H,onSelectPartMedia:yu,uploadingMedia:Cu,uploadPartMedia:p4,removePartMedia:g4,refreshCurrentPart:eu,onCategoryCreated:f4,searchCategories:b4,validateStep:xu,canProceed:hu,canFinish:Su,nextStep:v4,previousStep:E4,onTabChange:y4,savePart:C4,resetForm:Tu,getDataTypeLabel:x4,getUnitLabel:h4,getAccuracyLabel:B4,loadExistingAttributes:Pu,loadExistingCatalogItems:Mu,loadExistingEquipmentApplicabilities:qu,loadPartData:Bu,unlinkCatalogItem:D4,deleteCatalogItem:k4,VTabs:q4,VTabList:M4,VTab:P4,VCard:Gu,VButton:fu,VInputText:bu,VAutoComplete:vu,VMessage:Qu,VConfirmDialog:Hu,QuickCreateCategory:$4,QuickCreateBrand:t4,AttributeManager:e4,EquipmentSelector:O0,CatalogItemWizardEditor:pe,get resolveMediaUrl(){return G4},get Trash2Icon(){return Wu},get UnlinkIcon(){return s4},get FileTextIcon(){return R4},DangerButton:r4};return Object.defineProperty(Lu,"__isScriptSetup",{enumerable:!1,value:!0}),Lu}}),fe={class:"part-wizard"},be={class:"flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"},ve={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},Ee={class:"p-6"},ye={class:"mb-4"},Ce={class:"text-sm"},xe={class:"min-h-96"},he={key:0,class:"space-y-6"},Be={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},De={class:"flex gap-2"},ke={key:0,class:"mt-4 space-y-4"},_e={class:"flex items-center gap-4"},Fe={class:"w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},Ae=["src"],Ie={key:1,class:"text-surface-500 text-sm"},Ve={class:"flex flex-col gap-2"},we={class:"flex gap-2"},Se={key:0,class:"text-surface-500 text-xs"},Te={class:"flex items-center gap-4"},Pe={class:"flex-1 grid grid-cols-2 gap-3"},Me=["src","alt"],qe=["href"],Le={class:"text-sm truncate"},Ue={class:"p-2 border-t flex justify-end"},Oe={class:"flex flex-col gap-2 w-64"},ze={key:0,class:"text-surface-500 text-xs"},je={key:1,class:"space-y-6"},Re={key:2,class:"space-y-6"},Ne={key:3,class:"space-y-6"},Ke={key:4,class:"space-y-6"},He={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},We={class:"space-y-2"},Ge={class:"flex"},Qe={class:"text-surface-900 dark:text-surface-0"},Xe={class:"flex"},Je={class:"text-surface-900 dark:text-surface-0"},Ye={key:0,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},Ze={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},$e={class:"space-y-3"},ut={class:"flex-1"},et={class:"font-medium text-surface-900 dark:text-surface-0"},tt={key:0,class:"text-red-500 ml-1"},at={class:"text-sm text-surface-600 dark:text-surface-400"},nt={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},lt={class:"text-xs text-surface-500 dark:text-surface-400"},ot={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},rt={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},st={class:"space-y-3"},it={class:"flex-1"},dt={class:"flex items-center gap-2"},ct={class:"font-medium"},mt={class:"text-surface-600 dark:text-surface-400"},pt={key:0,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},gt={key:1,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},ft={class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},bt={key:0,class:"ml-2"},vt={key:0,class:"flex gap-2"},Et={key:1,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},yt={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},Ct={class:"space-y-3"},xt={class:"flex-1"},ht={class:"flex items-center gap-2"},Bt={class:"font-medium"},Dt={key:0,class:"text-surface-600 dark:text-surface-400"},kt={key:1,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},_t={key:2,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},Ft={key:0,class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},At={class:"flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"},It={key:1},Vt={class:"flex gap-3"};function wt(a,e,o,u,E,f){const b=Iu("tooltip");return l(),m("div",fe,[p(u.VCard,null,{header:C(()=>[n("div",be,[e[8]||(e[8]=n("h2",{class:"text-xl font-semibold text-surface-900 dark:text-surface-0"}," Мастер создания запчасти ",-1)),n("div",ve," Шаг "+y(u.currentStep)+" из "+y(u.totalSteps),1)])]),content:C(()=>[n("div",Ee,[n("div",ye,[p(u.VTabs,{value:u.activeStepKey,"onUpdate:value":u.onTabChange},{default:C(()=>[p(u.VTabList,null,{default:C(()=>[(l(),m(O,null,z(u.steps,(t,c)=>p(u.VTab,{key:t.key,value:t.key},{default:C(()=>[n("span",Ce,y(c+1)+". "+y(t.title),1)]),_:2},1032,["value"])),64))]),_:1})]),_:1},8,["value"])]),n("div",xe,[u.currentStep===1?(l(),m("div",he,[e[18]||(e[18]=n("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация о запчасти ",-1)),n("div",Be,[n("div",null,[e[9]||(e[9]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название запчасти * ",-1)),p(u.VInputText,{modelValue:u.formData.name,"onUpdate:modelValue":e[0]||(e[0]=t=>u.formData.name=t),placeholder:"Например: Сальник коленвала передний",class:"w-full p-3"},null,8,["modelValue"])]),n("div",null,[e[11]||(e[11]=n("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Категория * ",-1)),n("div",De,[p(u.VAutoComplete,{modelValue:u.selectedCategory,"onUpdate:modelValue":e[1]||(e[1]=t=>u.selectedCategory=t),suggestions:u.categorySuggestions,onComplete:u.searchCategories,"option-label":"name",placeholder:"Поиск категории...",class:"flex-1",dropdown:""},null,8,["modelValue","suggestions"]),p(u.VButton,{onClick:e[2]||(e[2]=t=>u.showCreateCategory=!0),severity:"secondary",outlined:"",size:"small",label:"Создать новую категорию"},{default:C(()=>e[10]||(e[10]=[_(" + ")])),_:1,__:[10]})])])]),o.mode==="edit"&&u.props.part?(l(),m("div",ke,[n("div",null,[e[14]||(e[14]=n("h4",{class:"text-surface-900 dark:text-surface-0 mb-2 font-medium"},"Основное изображение",-1)),n("div",_e,[n("div",Fe,[u.partImageUrl?(l(),m("img",{key:0,src:u.resolveMediaUrl(u.partImageUrl),alt:"Изображение запчасти",class:"object-cover w-full h-full"},null,8,Ae)):(l(),m("span",Ie,"Нет изображения"))]),n("div",Ve,[n("input",{type:"file",accept:"image/*",onChange:u.onSelectPartImage},null,32),n("div",we,[p(u.VButton,{size:"small",disabled:!u.selectedFile||u.uploading,onClick:u.uploadPartImage},{default:C(()=>e[12]||(e[12]=[_("Загрузить")])),_:1,__:[12]},8,["disabled"]),p(u.VButton,{size:"small",severity:"danger",outlined:"",disabled:!u.partImageUrl||u.uploading,onClick:u.removePartImage},{default:C(()=>e[13]||(e[13]=[_("Удалить")])),_:1,__:[13]},8,["disabled"])]),u.uploading?(l(),m("div",Se,"Загрузка...")):B("",!0)])])]),n("div",null,[e[17]||(e[17]=n("h4",{class:"text-surface-900 dark:text-surface-0 mb-2 font-medium"},"Дополнительные медиа (изображения, PDF)",-1)),n("div",Te,[n("div",Pe,[(l(!0),m(O,null,z(u.currentPart?.mediaAssets||[],t=>(l(),m("div",{key:t.id,class:"border rounded overflow-hidden"},[t.mimeType?.startsWith("image/")?(l(),m("img",{key:0,src:u.resolveMediaUrl(t.url),alt:t.fileName,class:"w-full h-32 object-cover"},null,8,Me)):(l(),m("a",{key:1,href:u.resolveMediaUrl(t.url),target:"_blank",class:"flex items-center gap-2 p-2 hover:bg-surface-50 dark:hover:bg-surface-900"},[p(u.FileTextIcon,{class:"text-red-600 w-4 h-4"}),n("span",Le,y(t.fileName),1)],8,qe)),n("div",Ue,[p(u.VButton,{size:"small",severity:"danger",text:"",onClick:c=>u.removePartMedia(t.id)},{default:C(()=>e[15]||(e[15]=[_("Удалить")])),_:2,__:[15]},1032,["onClick"])])]))),128))]),n("div",Oe,[n("input",{type:"file",multiple:"",accept:"image/*,application/pdf",onChange:u.onSelectPartMedia},null,32),p(u.VButton,{size:"small",disabled:!u.selectedMediaFiles||u.uploadingMedia,onClick:u.uploadPartMedia},{default:C(()=>e[16]||(e[16]=[_("Загрузить выбранные")])),_:1,__:[16]},8,["disabled"]),u.uploadingMedia?(l(),m("div",ze,"Загрузка...")):B("",!0)])])])])):B("",!0)])):B("",!0),u.currentStep===2?(l(),m("div",je,[p(u.AttributeManager,{modelValue:u.formData.attributes,"onUpdate:modelValue":e[3]||(e[3]=t=>u.formData.attributes=t)},null,8,["modelValue"])])):B("",!0),u.currentStep===3?(l(),m("div",Re,[p(u.CatalogItemWizardEditor,{modelValue:u.formData.catalogItems,"onUpdate:modelValue":e[4]||(e[4]=t=>u.formData.catalogItems=t),mode:o.mode,"part-id":u.props.part?.id},null,8,["modelValue","mode","part-id"])])):B("",!0),u.currentStep===4?(l(),m("div",Ne,[p(u.EquipmentSelector,{modelValue:u.formData.equipmentApplicabilities,"onUpdate:modelValue":e[5]||(e[5]=t=>u.formData.equipmentApplicabilities=t)},null,8,["modelValue"])])):B("",!0),u.currentStep===5?(l(),m("div",Ke,[e[24]||(e[24]=n("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Подтверждение создания ",-1)),n("div",He,[e[21]||(e[21]=n("h4",{class:"font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация: ",-1)),n("dl",We,[n("div",Ge,[e[19]||(e[19]=n("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Название: ",-1)),n("dd",Qe,y(u.formData.name),1)]),n("div",Xe,[e[20]||(e[20]=n("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Категория: ",-1)),n("dd",Je,y(u.selectedCategory?.name),1)])])]),u.formData.attributes.length>0?(l(),m("div",Ye,[n("h4",Ze," Атрибуты ("+y(u.formData.attributes.length)+"): ",1),n("div",$e,[(l(!0),m(O,null,z(u.formData.attributes,(t,c)=>(l(),m("div",{key:c,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"},[n("div",ut,[n("div",et,[_(y(t.template?.title||t.templateTitle)+" ",1),t.template?.isRequired?(l(),m("span",tt,"*")):B("",!0)]),n("div",at,[_(y(t.value)+y(t.template?.unit||t.templateUnit?`
                        ${u.getUnitLabel(t.template?.unit||t.templateUnit)}`:"")+" ",1),t.template?.group?.name||t.templateGroup?(l(),m("span",nt,y(t.template?.group?.name||t.templateGroup),1)):B("",!0)])]),n("div",lt,y(u.getDataTypeLabel(t.template?.dataType||t.templateDataType)),1)]))),128))])])):B("",!0),n("div",ot,[n("h4",rt," Каталожные позиции ("+y(u.formData.catalogItems.length)+"): ",1),n("div",st,[(l(!0),m(O,null,z(u.formData.catalogItems,(t,c)=>(l(),m("div",{key:c,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[n("div",it,[n("div",dt,[n("span",ct,y(t.isExisting?t.existingCatalogItem?.sku:t.sku),1),n("span",mt," ("+y(t.isExisting?t.existingCatalogItem?.brand?.name:t.selectedBrand?.name)+") ",1),t.isExisting?(l(),m("span",pt," Существующая ")):(l(),m("span",gt," Новая "))]),n("div",ft,[_(" Точность: "+y(u.getAccuracyLabel(t.accuracy))+" ",1),t.notes?(l(),m("span",bt,"• "+y(t.notes),1)):B("",!0)])]),o.mode==="edit"&&u.props.part?(l(),m("div",vt,[Y((l(),I(u.DangerButton,{onClick:s=>u.unlinkCatalogItem(t,c),severity:"secondary",outlined:"",size:"small"},{default:C(()=>[e[22]||(e[22]=_(" Отвязать ")),p(u.UnlinkIcon,{class:"w-4 h-4"})]),_:2,__:[22]},1032,["onClick"])),[[b,"Отвязать от запчасти"]]),Y((l(),I(u.DangerButton,{onClick:s=>u.deleteCatalogItem(t,c),severity:"danger",outlined:"",size:"small"},{default:C(()=>[e[23]||(e[23]=_(" Удалить ")),p(u.Trash2Icon,{class:"w-4 h-4"})]),_:2,__:[23]},1032,["onClick"])),[[b,"Удалить каталожную позицию"]])])):B("",!0)]))),128))])]),u.formData.equipmentApplicabilities.length>0?(l(),m("div",Et,[n("h4",yt," Применимость к технике ("+y(u.formData.equipmentApplicabilities.length)+"): ",1),n("div",Ct,[(l(!0),m(O,null,z(u.formData.equipmentApplicabilities,(t,c)=>(l(),m("div",{key:c,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[n("div",xt,[n("div",ht,[n("span",Bt,y(t.isExisting?t.existingEquipmentModel?.name:t.name),1),t.selectedBrand||t.existingEquipmentModel?.brand?(l(),m("span",Dt," ("+y(t.selectedBrand?.name||t.existingEquipmentModel?.brand?.name)+") ",1)):B("",!0),t.isExisting?(l(),m("span",kt," Существующая ")):(l(),m("span",_t," Новая "))]),t.notes?(l(),m("div",Ft,y(t.notes),1)):B("",!0)])]))),128))])])):B("",!0)])):B("",!0)]),n("div",At,[u.currentStep>1?(l(),I(u.VButton,{key:0,onClick:u.previousStep,severity:"secondary",outlined:""},{default:C(()=>e[25]||(e[25]=[_(" Назад ")])),_:1,__:[25]})):(l(),m("div",It)),n("div",Vt,[u.currentStep<u.totalSteps?(l(),I(u.VButton,{key:0,onClick:u.nextStep,disabled:!u.canProceed,label:"Далее",outlined:""},null,8,["disabled"])):(l(),I(u.VButton,{key:1,label:o.mode==="edit"?"Сохранить изменения":"Создать запчасть",onClick:u.savePart,loading:u.loading||u.saving,disabled:!u.canFinish||u.saving},null,8,["label","loading","disabled"]))])])])]),_:1}),u.error?(l(),I(u.VMessage,{key:0,severity:"error",class:"mt-4"},{default:C(()=>[_(y(u.error),1)]),_:1})):B("",!0),u.saveError?(l(),I(u.VMessage,{key:1,severity:"error",class:"mt-4"},{default:C(()=>[_(y(u.saveError),1)]),_:1})):B("",!0),p(u.QuickCreateCategory,{visible:u.showCreateCategory,"onUpdate:visible":e[6]||(e[6]=t=>u.showCreateCategory=t),onCreated:u.onCategoryCreated},null,8,["visible"]),p(u.QuickCreateBrand,{visible:u.showCreateBrand,"onUpdate:visible":e[7]||(e[7]=t=>u.showCreateBrand=t)},null,8,["visible"]),p(u.VConfirmDialog)])}const Va=au(ge,[["render",wt]]);export{Va as default};
