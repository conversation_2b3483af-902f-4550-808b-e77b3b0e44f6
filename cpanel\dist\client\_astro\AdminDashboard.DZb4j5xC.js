import F from"./Card.DllwvXut.js";import{u as w}from"./useTrpc.CcBnDuWb.js";import{n as I}from"./router.DKcY2uv6.js";import{_ as S}from"./utils.BWEB-mtU.js";import{c}from"./createLucideIcon.CxvjiKko.js";import{L}from"./link.DoOSpg3_.js";import{L as A}from"./list.Ce343wC8.js";import{T}from"./tags.HHlfcBcj.js";import{d as j,c as n,a as t,F as f,e as o,w as l,n as H,o as i,r as x}from"./index.CpC-7sc3.js";import{t as a,r as y,y as V}from"./reactivity.esm-bundler.Bx7uHohy.js";import"./trpc.CMxyjkwB.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=c("bolt",[["path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",key:"yt0hxn"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=c("boxes",[["path",{d:"M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z",key:"lc1i9w"}],["path",{d:"m7 16.5-4.74-2.85",key:"1o9zyk"}],["path",{d:"m7 16.5 5-3",key:"va8pkn"}],["path",{d:"M7 16.5v5.17",key:"jnp8gn"}],["path",{d:"M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z",key:"8zsnat"}],["path",{d:"m17 16.5-5-3",key:"8arw3v"}],["path",{d:"m17 16.5 4.74-2.85",key:"8rfmw"}],["path",{d:"M17 16.5v5.17",key:"k6z78m"}],["path",{d:"M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z",key:"1xygjf"}],["path",{d:"M12 8 7.26 5.15",key:"1vbdud"}],["path",{d:"m12 8 4.74-2.85",key:"3rx089"}],["path",{d:"M12 13.5V8",key:"1io7kd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=c("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=c("folder-tree",[["path",{d:"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"hod4my"}],["path",{d:"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"w4yl2u"}],["path",{d:"M3 5a2 2 0 0 0 2 2h3",key:"f2jnh7"}],["path",{d:"M3 3v13a2 2 0 0 0 2 2h3",key:"k8epm1"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=c("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=c("inbox",[["polyline",{points:"22 12 16 12 14 15 10 15 8 12 2 12",key:"o97t9d"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=c("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),p=30,G=j({__name:"AdminDashboard",setup(_,{expose:s}){s();const{client:g}=w(),e=y(!0),v=y(null),h=y(null);H(async()=>{try{const r=await g.admin.getDashboard.query({rangeDays:p});h.value=r}catch(r){v.value=r?.message??"Не удалось загрузить дашборд"}finally{e.value=!1}});function u(r){return r.reduce((d,m)=>d+m.count,0)}function D(r){return r.slice(-7)}function b(r){return r.reduce((d,m)=>Math.max(d,m.count),0)||1}function B(r,d){const m=b(d);return Math.round(r/m*100)}function E(r){const d=new Date(r);return`${d.getDate().toString().padStart(2,"0")}.${(d.getMonth()+1).toString().padStart(2,"0")}`}function C(r){return new Date(r).toLocaleString()}function M(r){I(r)}const k={client:g,loading:e,error:v,stats:h,rangeDays:p,sumSeries:u,last7:D,maxInSeries:b,barHeight:B,shortDate:E,formatDate:C,navigateTo:M,Card:F,get UsersIcon(){return O},get Building2Icon(){return q},get BoxesIcon(){return Z},get BoltIcon(){return z},get TagsIcon(){return T},get ListIcon(){return A},get InboxIcon(){return N},get HistoryIcon(){return U},get LinkIcon(){return L},get FolderTreeIcon(){return P}};return Object.defineProperty(k,"__isScriptSetup",{enumerable:!1,value:!0}),k}}),J={class:"space-y-8"},K={key:0,class:"text-sm text-surface-500"},Q={key:1,class:"text-sm text-red-600"},R={class:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"},W={class:"flex-shrink-0"},X={class:"ml-5 w-0 flex-1"},Y={class:"text-lg font-medium text-surface-900"},$={class:"flex-shrink-0"},t0={class:"ml-5 w-0 flex-1"},e0={class:"text-lg font-medium text-surface-900"},s0={class:"flex-shrink-0"},u0={class:"ml-5 w-0 flex-1"},a0={class:"text-lg font-medium text-surface-900"},o0={class:"flex-shrink-0"},r0={class:"ml-5 w-0 flex-1"},n0={class:"text-lg font-medium text-surface-900"},l0={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5"},i0={class:"flex items-center"},d0={class:"text-base font-semibold"},c0={class:"flex items-center"},m0={class:"text-base font-semibold"},f0={class:"flex items-center"},x0={class:"text-base font-semibold"},g0={class:"flex items-center"},v0={class:"text-base font-semibold"},h0={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},y0={class:"grid grid-cols-1 sm:grid-cols-3 gap-4"},b0={class:"p-3 rounded-lg bg-surface-50 dark:bg-surface-800"},k0={class:"text-xl font-semibold"},p0={class:"p-3 rounded-lg bg-surface-50 dark:bg-surface-800"},_0={class:"text-xl font-semibold"},D0={class:"p-3 rounded-lg bg-surface-50 dark:bg-surface-800"},B0={class:"text-xl font-semibold"},E0={class:"mt-4 grid grid-cols-7 gap-2 text-xs text-surface-500"},C0={class:"h-10 w-8 flex items-end"},M0={class:"mt-1"},F0={class:"space-y-3"},w0={class:"text-sm font-medium text-surface-900"},I0={class:"text-sm text-surface-500"},S0={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},L0={key:0,class:"text-sm text-surface-500"},A0={key:1,class:"space-y-3"},T0={class:"flex-1"},j0={class:"text-sm text-surface-900"},H0={class:"text-xs text-surface-500"},V0={key:0,class:"text-sm text-surface-500"},z0={key:1,class:"space-y-3"},Z0={class:"flex-1"},q0={class:"text-sm text-surface-900"},P0={class:"text-xs text-surface-500"};function U0(_,s,g,e,v,h){return i(),n("div",J,[s[22]||(s[22]=t("div",null,[t("h1",{class:"text-2xl font-bold text-surface-900"},"Дашборд"),t("p",{class:"mt-1 text-sm text-surface-600"}," Обзор системы управления каталогом запчастей ")],-1)),e.loading?(i(),n("div",K,"Загрузка...")):e.error?(i(),n("div",Q,a(e.error),1)):(i(),n(f,{key:2},[t("div",R,[o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Пользователи",onClick:s[0]||(s[0]=u=>e.navigateTo("/admin/users")),class:"flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",W,[o(e.UsersIcon,{class:"text-2xl text-primary-600"})]),t("div",X,[t("dl",null,[s[8]||(s[8]=t("dt",{class:"text-sm font-medium text-surface-500 truncate"},"Пользователи",-1)),t("dd",Y,a(e.stats?.totals.users.toLocaleString()),1)])])])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Магазины",onClick:s[1]||(s[1]=u=>e.navigateTo("/admin/users")),class:"flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",$,[o(e.Building2Icon,{class:"text-2xl text-primary-600"})]),t("div",t0,[t("dl",null,[s[9]||(s[9]=t("dt",{class:"text-sm font-medium text-surface-500 truncate"},"Магазины",-1)),t("dd",e0,a(e.stats?.totals.shops.toLocaleString()),1)])])])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Запчасти",onClick:s[2]||(s[2]=u=>e.navigateTo("/admin/parts")),class:"flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",s0,[o(e.BoxesIcon,{class:"text-2xl text-primary-600"})]),t("div",u0,[t("dl",null,[s[10]||(s[10]=t("dt",{class:"text-sm font-medium text-surface-500 truncate"},"Запчасти",-1)),t("dd",a0,a(e.stats?.totals.parts.toLocaleString()),1)])])])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Активные сессии",onClick:s[3]||(s[3]=u=>e.navigateTo("/admin/access-control")),class:"flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",o0,[o(e.BoltIcon,{class:"text-2xl text-primary-600"})]),t("div",r0,[t("dl",null,[s[11]||(s[11]=t("dt",{class:"text-sm font-medium text-surface-500 truncate"},"Активные сессии",-1)),t("dd",n0,a(e.stats?.totals.sessionsActive.toLocaleString()),1)])])])]),_:1})]),t("div",l0,[o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Бренды",onClick:s[4]||(s[4]=u=>e.navigateTo("/admin/brands")),class:"flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",i0,[o(e.TagsIcon,{class:"text-xl text-primary-600"}),s[12]||(s[12]=t("span",{class:"ml-3 text-sm text-surface-600"},"Бренды",-1))]),t("span",d0,a(e.stats?.totals.brands.toLocaleString()),1)])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Категории",onClick:s[5]||(s[5]=u=>e.navigateTo("/admin/categories")),class:"flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",c0,[o(e.FolderTreeIcon,{class:"text-xl text-primary-600"}),s[13]||(s[13]=t("span",{class:"ml-3 text-sm text-surface-600"},"Категории",-1))]),t("span",m0,a(e.stats?.totals.partCategories.toLocaleString()),1)])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Каталог",onClick:s[6]||(s[6]=u=>e.navigateTo("/admin/catalogitems")),class:"flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",f0,[o(e.ListIcon,{class:"text-xl text-primary-600"}),s[14]||(s[14]=t("span",{class:"ml-3 text-sm text-surface-600"},"Каталог",-1))]),t("span",x0,a(e.stats?.totals.catalogItems.toLocaleString()),1)])]),_:1}),o(e.Card,null,{content:l(()=>[t("button",{type:"button","aria-label":"Предложения",onClick:s[7]||(s[7]=u=>e.navigateTo("/admin/proposals")),class:"flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"},[t("div",g0,[o(e.InboxIcon,{class:"text-xl text-primary-600"}),s[15]||(s[15]=t("span",{class:"ml-3 text-sm text-surface-600"},"Предложения (ожидают)",-1))]),t("span",v0,a(e.stats?.totals.proposals.pending.toLocaleString()),1)])]),_:1})]),t("div",h0,[o(e.Card,{class:"lg:col-span-2"},{header:l(()=>[t("h3",{class:"text-lg leading-6 font-medium text-surface-900"},"Активность за "+a(e.rangeDays)+" дней")]),content:l(()=>[t("div",y0,[t("div",b0,[s[16]||(s[16]=t("div",{class:"text-xs text-surface-500 mb-1"},"Новые пользователи",-1)),t("div",k0,a(e.sumSeries(e.stats.trends.usersDaily).toLocaleString()),1)]),t("div",p0,[s[17]||(s[17]=t("div",{class:"text-xs text-surface-500 mb-1"},"Новые запчасти",-1)),t("div",_0,a(e.sumSeries(e.stats.trends.partsDaily).toLocaleString()),1)]),t("div",D0,[s[18]||(s[18]=t("div",{class:"text-xs text-surface-500 mb-1"},"Новые предложения",-1)),t("div",B0,a(e.sumSeries(e.stats.trends.proposalsDaily).toLocaleString()),1)])]),t("div",E0,[(i(!0),n(f,null,x(e.last7(e.stats.trends.usersDaily),u=>(i(),n("div",{key:u.date,class:"flex flex-col items-center"},[t("div",C0,[t("div",{class:"w-full bg-primary-500/20",style:V({height:`${e.barHeight(u.count,e.stats.trends.usersDaily)}%`})},null,4)]),t("div",M0,a(e.shortDate(u.date)),1)]))),128))])]),_:1}),o(e.Card,null,{header:l(()=>s[19]||(s[19]=[t("h3",{class:"text-lg leading-6 font-medium text-surface-900"},"Популярные категории",-1)])),content:l(()=>[t("div",F0,[(i(!0),n(f,null,x(e.stats?.top.categories,u=>(i(),n("div",{key:u.id,class:"flex justify-between items-center"},[t("span",w0,a(u.name),1),t("span",I0,a(u.count.toLocaleString()),1)]))),128))])]),_:1})]),t("div",S0,[o(e.Card,null,{header:l(()=>s[20]||(s[20]=[t("h3",{class:"text-lg leading-6 font-medium text-surface-900"},"Последние действия (аудит)",-1)])),content:l(()=>[(e.stats?.recent.audit?.length||0)===0?(i(),n("div",L0,"Нет записей")):(i(),n("div",A0,[(i(!0),n(f,null,x(e.stats.recent.audit,u=>(i(),n("div",{key:u.id,class:"flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"},[o(e.HistoryIcon,{class:"text-surface-500 mr-3"}),t("div",T0,[t("div",j0,a(u.action),1),t("div",H0,a(e.formatDate(u.createdAt))+" • "+a(u.adminEmail),1)])]))),128))]))]),_:1}),o(e.Card,null,{header:l(()=>s[21]||(s[21]=[t("h3",{class:"text-lg leading-6 font-medium text-surface-900"},"Новые предложения эквивалентов",-1)])),content:l(()=>[(e.stats?.recent.pendingProposals?.length||0)===0?(i(),n("div",V0,"Нет новых предложений")):(i(),n("div",z0,[(i(!0),n(f,null,x(e.stats.recent.pendingProposals,u=>(i(),n("div",{key:u.id,class:"flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"},[o(e.LinkIcon,{class:"text-primary mr-3"}),t("div",Z0,[t("div",q0,a(u.catalogItem?.brand?.name||"Бренд")+" • "+a(u.catalogItem?.sku)+" → "+a(u.part?.name||`Part #${u.part?.id}`),1),t("div",P0,a(u.accuracySuggestion)+" • "+a(e.formatDate(u.createdAt)),1)])]))),128))]))]),_:1})])],64))])}const st=S(G,[["render",U0]]);export{st as default};
