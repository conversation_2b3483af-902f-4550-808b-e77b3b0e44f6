import { defineComponent, useSSRContext, ref, mergeProps, createSlots, withCtx, createVNode, renderList, renderSlot } from 'vue';
import AngleDownIcon from '@primevue/icons/angledown';
import AngleUpIcon from '@primevue/icons/angleup';
import { s as script } from './index_BtI-Bxqq.mjs';
import { p as ptViewMerge, _ as _export_sfc } from './ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "InputNumber",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex relative 
        p-vertical:flex-col p-fluid:w-full`,
      pcInputText: {
        root: `appearance-none rounded-md outline-hidden flex-auto
        bg-surface-0 dark:bg-surface-950
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        text-surface-700 dark:text-surface-0
        placeholder:text-surface-500 dark:placeholder:text-surface-400
        border border-surface-300 dark:border-surface-700 
        enabled:hover:border-surface-400 dark:enabled:hover:border-surface-600
        enabled:focus:border-primary
        disabled:bg-surface-200 disabled:text-surface-500 
        dark:disabled:bg-surface-700 dark:disabled:text-surface-400
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-invalid:placeholder:text-red-600 dark:p-invalid:placeholder:text-red-400
        px-3 py-2 p-fluid:w-full
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-lg p-large:px-[0.875rem] p-large:py-[0.625rem]
        transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        p-horizontal:order-2 p-horizontal:rounded-none
        p-vertical:order-2 p-vertical:rounded-none p-vertical:text-center
        p-fluid:w-[1%] p-fluid:p-vertical:w-full`
      },
      buttonGroup: `p-stacked:flex p-stacked:flex-col p-stacked:absolute p-stacked:top-px p-stacked:end-px p-stacked:h-[calc(100%-2px)] p-stacked:z-10`,
      incrementButton: `flex items-center justify-center grow-0 shrink-0 basis-auto cursor-pointer w-10
        bg-transparent enabled:hover:bg-surface-100 enabled:active:bg-surface-200
        border border-surface-300 enabled:hover:border-surface-300 enabled:active:border-surface-300
        text-surface-400 enabled:hover:text-surface-500 enabled:active:text-surface-600
        dark:bg-transparent dark:enabled:hover:bg-surface-800 dark:enabled:active:bg-surface-700
        dark:border-surface-700 dark:enabled:hover:border-surface-700 dark:enabled:active:border-surface-700
        dark:text-surface-400 dark:enabled:hover:text-surface-300 dark:enabled:active:text-surface-200
        transition-colors duration-200 disabled:pointer-events-none
        p-stacked:relative p-stacked:flex-auto p-stacked:border-none
        p-stacked:p-0 p-stacked:rounded-tr-[5px]
        p-horizontal:order-3 p-horizontal:rounded-e-md p-horizontal:border-s-0
        p-vertical:py-2 p-vertical:order-1 p-vertical:rounded-ss-md p-vertical:rounded-se-md p-vertical:w-full p-vertical:border-b-0`,
      incrementIcon: ``,
      decrementButton: `flex items-center justify-center grow-0 shrink-0 basis-auto cursor-pointer w-10
        bg-transparent enabled:hover:bg-surface-100 enabled:active:bg-surface-200
        border border-surface-300 enabled:hover:border-surface-300 enabled:active:border-surface-300
        text-surface-400 enabled:hover:text-surface-500 enabled:active:text-surface-600
        dark:bg-transparent dark:enabled:hover:bg-surface-800 dark:enabled:active:bg-surface-700
        dark:border-surface-700 dark:enabled:hover:border-surface-700 dark:enabled:active:border-surface-700
        dark:text-surface-400 dark:enabled:hover:text-surface-300 dark:enabled:active:text-surface-200
        transition-colors duration-200 disabled:pointer-events-none
        p-stacked:relative p-stacked:flex-auto p-stacked:border-none
        p-stacked:p-0 p-stacked:rounded-br-[5px]
        p-horizontal:order-1 p-horizontal:rounded-s-md p-horizontal:border-e-0
        p-vertical:py-2 p-vertical:order-3 p-vertical:rounded-ee-md p-vertical:rounded-es-md p-vertical:w-full p-vertical:border-t-0`,
      decrementIcon: ``
    });
    const __returned__ = { theme, get AngleDownIcon() {
      return AngleDownIcon;
    }, get AngleUpIcon() {
      return AngleUpIcon;
    }, get InputNumber() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["InputNumber"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({
    incrementicon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["AngleUpIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["AngleUpIcon"])
        ];
      }
    }),
    decrementicon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["AngleDownIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["AngleDownIcon"])
        ];
      }
    }),
    _: 2
  }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/InputNumber.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const VInputNumber = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { VInputNumber as V };
