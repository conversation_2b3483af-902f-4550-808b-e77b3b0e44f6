<template>
  <div class="w-full max-w-4xl">
    <VCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">{{ isEdit ? 'Редактировать' : 'Создать' }} шаблон страницы</h2>
          <VButton 
            label="Назад к списку" 
            icon="ArrowLeft" 
            @click="navigate('/admin/templates')" 
            variant="outline"
          />
        </div>
      </template>
      <template #content>
        <form @submit.prevent="onSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="text-sm font-medium">Название</label>
              <VInputText 
                v-model="form.name" 
                placeholder="Введите название шаблона"
                :class="{ 'border-red-500': errors.name }"
              />
              <span v-if="errors.name" class="text-sm text-red-500">{{ errors.name }}</span>
            </div>
            
            <div class="space-y-2">
              <label class="text-sm font-medium">Тип</label>
              <VSelect
                v-model="form.kind"
                :options="kindOptions"
                placeholder="Выберите тип"
                optionLabel="label"
                optionValue="value"
                :class="{ 'border-red-500': errors.kind }"
              />
              <span v-if="errors.kind" class="text-sm text-red-500">{{ errors.kind }}</span>
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">Описание</label>
            <VTextarea 
              v-model="form.description" 
              placeholder="Введите описание шаблона"
              rows="3"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="text-sm font-medium">Категория запчасти</label>
              <VAutoComplete
                v-model="categoryQuery"
                :suggestions="categorySuggestions"
                optionLabel="name"
                placeholder="Выберите категорию"
                @complete="onCompleteCategory"
                @item-select="onSelectCategory"
              />
            </div>
            
            <div class="space-y-2">
              <label class="text-sm font-medium">H1 заголовок</label>
              <VTextarea 
                v-model="form.categoryConfig.h1" 
                placeholder="Введите H1 заголовок (например: {{category.name}})"
                rows="2"
                :class="{ 'border-red-500': errors.h1 }"
              />
              <span v-if="errors.h1" class="text-sm text-red-500">{{ errors.h1 }}</span>
            </div>
            
            <div class="space-y-2">
              <label class="text-sm font-medium">H2 заголовок</label>
              <VTextarea 
                v-model="form.categoryConfig.h2" 
                placeholder="Введите H2 заголовок (например: {{category.description}})"
                rows="2"
                :class="{ 'border-red-500': errors.h2 }"
              />
              <span v-if="errors.h2" class="text-sm text-red-500">{{ errors.h2 }}</span>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <VCheckbox 
              v-model="form.isDefault" 
              :binary="true"
              :label="'Установить как шаблон по умолчанию'"
            />
            <VCheckbox 
              v-model="form.isActive" 
              :binary="true"
              :label="'Активен'"
            />
          </div>

          <div class="flex justify-end space-x-3">
            <VButton 
              type="button"
              label="Отмена" 
              variant="outline"
              @click="navigate('/admin/templates')" 
            />
            <VButton 
              type="submit" 
              label="Сохранить" 
              :loading="loading"
            />
          </div>
        </form>
      </template>
    </VCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VInputText from "@/volt/InputText.vue";
import VTextarea from "@/volt/Textarea.vue";
import VSelect from "@/volt/Select.vue";
import VCheckbox from "@/volt/Checkbox.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import { navigate } from "astro:transitions/client";
import { useTrpc } from "@/composables/useTrpc";

const trpc = useTrpc();

const isEdit = ref(false);
const loading = ref(false);
const categoryQuery = ref("");
const categorySuggestions = ref<any[]>([]);

const form = ref({
  name: "",
  description: "",
  kind: "CATEGORY" as "CATEGORY" | "PART" | "CATALOG_ITEM",
  categoryConfig: {
    h1: "",
    h2: "",
    description: "",
    footer: "",
    filters: { attributeNames: [], sortOrder: [], withUnits: false },
    productAttrs: { attributeNames: [], sortOrder: [], withUnits: true }
  },
  partConfig: {
    h1: "",
    h2: "",
    attributes: { attributeNames: [], sortOrder: [], withUnits: true }
  },
  catalogItemConfig: {
    h1: "",
    h2: "",
    attributes: { attributeNames: [], sortOrder: [], withUnits: true }
  },
  isDefault: false,
  isActive: true,
  partCategoryId: undefined as number | undefined,
});

const errors = ref<Record<string, string>>({});

const kindOptions = [
  { label: "Категория", value: "CATEGORY" },
  { label: "Запчасть", value: "PART" },
  { label: "Каталожная позиция", value: "CATALOG_ITEM" },
];

async function onSubmit() {
  try {
    loading.value = true;
    errors.value = {};

    // Валидация
    if (!form.value.name.trim()) {
      errors.value.name = "Название обязательно";
      return;
    }
    
    // Валидация в зависимости от типа
    if (form.value.kind === "CATEGORY") {
      if (!form.value.categoryConfig.h1.trim()) {
        errors.value.h1 = "H1 заголовок обязателен";
        return;
      }
    }

    const result = await (trpc.client.pageTemplates.create as any).mutate({
      name: form.value.name.trim(),
      description: form.value.description.trim(),
      kind: form.value.kind,
      categoryConfig: form.value.kind === "CATEGORY" ? form.value.categoryConfig : undefined,
      partConfig: form.value.kind === "PART" ? form.value.partConfig : undefined,
      catalogItemConfig: form.value.kind === "CATALOG_ITEM" ? form.value.catalogItemConfig : undefined,
      isDefault: form.value.isDefault,
      isActive: form.value.isActive,
      partCategoryId: form.value.partCategoryId,
    });

    if (result) {
      navigate("/admin/templates");
    }
  } catch (error) {
    console.error("Ошибка сохранения:", error);
  } finally {
    loading.value = false;
  }
}

function onCompleteCategory(e: any) {
  (trpc.client.crud.partCategory.findMany as any)
    .query({ where: { name: { contains: e.query } }, take: 10 })
    .then((res: any) => (categorySuggestions.value = res || []))
    .catch((error: any) => {
      console.error('Ошибка загрузки категорий:', error);
      categorySuggestions.value = [];
    });
}

function onSelectCategory(e: any) {
  form.value.partCategoryId = e.value?.id;
}
</script>
