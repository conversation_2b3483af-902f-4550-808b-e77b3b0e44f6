import { createTRPCClient, loggerLink, httpBatchLink } from '@trpc/client';
import superjson from 'superjson';

const __vite_import_meta_env__ = {"ASSETS_PREFIX": undefined, "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SITE": undefined, "SSR": true};
const baseURL = Object.assign(__vite_import_meta_env__, { PUBLIC: process.env.PUBLIC, _: process.env._ })?.PUBLIC_API_URL || (typeof window !== "undefined" ? window.__PUBLIC_API_URL__ : void 0) || (typeof process !== "undefined" ? process?.env?.PUBLIC_API_URL : void 0) || (typeof process !== "undefined" ? process?.env?.API_URL : void 0) || "http://localhost:3000";
const apiUrl = `${String(baseURL).replace(/\/$/, "")}/trpc`;
const trpc = createTRPCClient({
  links: [
    loggerLink({ enabled: () => Object.assign(__vite_import_meta_env__, { PUBLIC: process.env.PUBLIC, _: process.env._ }).DEV }),
    httpBatchLink({
      url: apiUrl,
      transformer: superjson,
      fetch: (url, options) => fetch(url, { ...options, credentials: "include" })
    })
  ]
});

export { trpc as t };
