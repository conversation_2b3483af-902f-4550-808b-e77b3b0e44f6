import{s as b,_ as m,p as k}from"./utils.BWEB-mtU.js";import{B as v,c,o as s,g,b as p,j as u,m as o,a0 as h,a as w,d as x,i as S,r as P,w as $,k as _}from"./index.CpC-7sc3.js";import{f as T}from"./index.CMLtULFQ.js";import{t as B,b as j,r as z}from"./reactivity.esm-bundler.Bx7uHohy.js";var V=`
    .p-tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: dt('tag.primary.background');
        color: dt('tag.primary.color');
        font-size: dt('tag.font.size');
        font-weight: dt('tag.font.weight');
        padding: dt('tag.padding');
        border-radius: dt('tag.border.radius');
        gap: dt('tag.gap');
    }

    .p-tag-icon {
        font-size: dt('tag.icon.size');
        width: dt('tag.icon.size');
        height: dt('tag.icon.size');
    }

    .p-tag-rounded {
        border-radius: dt('tag.rounded.border.radius');
    }

    .p-tag-success {
        background: dt('tag.success.background');
        color: dt('tag.success.color');
    }

    .p-tag-info {
        background: dt('tag.info.background');
        color: dt('tag.info.color');
    }

    .p-tag-warn {
        background: dt('tag.warn.background');
        color: dt('tag.warn.color');
    }

    .p-tag-danger {
        background: dt('tag.danger.background');
        color: dt('tag.danger.color');
    }

    .p-tag-secondary {
        background: dt('tag.secondary.background');
        color: dt('tag.secondary.color');
    }

    .p-tag-contrast {
        background: dt('tag.contrast.background');
        color: dt('tag.contrast.color');
    }
`,C={root:function(t){var r=t.props;return["p-tag p-component",{"p-tag-info":r.severity==="info","p-tag-success":r.severity==="success","p-tag-warn":r.severity==="warn","p-tag-danger":r.severity==="danger","p-tag-secondary":r.severity==="secondary","p-tag-contrast":r.severity==="contrast","p-tag-rounded":r.rounded}]},icon:"p-tag-icon",label:"p-tag-label"},O=v.extend({name:"tag",style:V,classes:C}),M={name:"BaseTag",extends:b,props:{value:null,severity:null,rounded:Boolean,icon:String},style:O,provide:function(){return{$pcTag:this,$parentInstance:this}}};function a(e){"@babel/helpers - typeof";return a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(e)}function D(e,t,r){return(t=E(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E(e){var t=N(e,"string");return a(t)=="symbol"?t:t+""}function N(e,t){if(a(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(a(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var l={name:"Tag",extends:M,inheritAttrs:!1,computed:{dataP:function(){return T(D({rounded:this.rounded},this.severity,this.severity))}}},A=["data-p"];function I(e,t,r,n,f,i){return s(),c("span",o({class:e.cx("root"),"data-p":i.dataP},e.ptmi("root")),[e.$slots.icon?(s(),g(h(e.$slots.icon),o({key:0,class:e.cx("icon")},e.ptm("icon")),null,16,["class"])):e.icon?(s(),c("span",o({key:1,class:[e.cx("icon"),e.icon]},e.ptm("icon")),null,16)):p("",!0),e.value!=null||e.$slots.default?u(e.$slots,"default",{key:2},function(){return[w("span",o({class:e.cx("label")},e.ptm("label")),B(e.value),17)]}):p("",!0)],16,A)}l.render=I;const K=x({__name:"Tag",setup(e,{expose:t}){t();const n={theme:z({root:`inline-flex items-center justify-center text-sm font-bold py-1 px-2 rounded-md gap-1 p-rounded:rounded-2xl
        bg-primary-100 dark:bg-primary-500/15 text-primary-700 dark:text-primary-300
        p-success:bg-green-100 dark:p-success:bg-green-500/15 p-success:text-green-700 dark:p-success:text-green-300
        p-info:bg-sky-100 dark:p-info:bg-sky-500/15 p-info:text-sky-700 dark:p-info:text-sky-300
        p-warn:bg-orange-100 dark:p-warn:bg-orange-500/15 p-warn:text-orange-700 dark:p-warn:text-orange-300
        p-danger:bg-red-100 dark:p-danger:bg-red-500/15 p-danger:text-red-700 dark:p-danger:text-red-300
        p-secondary:bg-surface-100 dark:p-secondary:bg-surface-800 p-secondary:text-surface-600 dark:p-secondary:text-surface-300
        p-contrast:bg-surface-950 dark:p-contrast:bg-surface-0 p-contrast:text-surface-0 dark:p-contrast:text-surface-950`,icon:"text-xs w-3 h-3"}),get Tag(){return l},get ptViewMerge(){return k}};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}});function L(e,t,r,n,f,i){return s(),g(n.Tag,{unstyled:"",pt:n.theme,ptOptions:{mergeProps:n.ptViewMerge}},S({_:2},[P(e.$slots,(R,d)=>({name:d,fn:$(y=>[u(e.$slots,d,j(_(y??{})))])}))]),1032,["pt","ptOptions"])}const J=m(K,[["render",L]]);export{J as T};
