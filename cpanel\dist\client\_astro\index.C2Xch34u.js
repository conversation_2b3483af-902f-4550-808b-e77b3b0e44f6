import{B as y,L as S,z as v,C as b,U as E,I as h,x as g}from"./index.CpC-7sc3.js";import{B as O}from"./index.Cl2VmfYg.js";var j=y.extend({name:"focustrap-directive"}),P=O.extend({style:j});function m(o){"@babel/helpers - typeof";return m=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(o)}function F(o,e){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(o,n).enumerable})),t.push.apply(t,r)}return t}function $(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?F(Object(t),!0).forEach(function(r){x(o,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):F(Object(t)).forEach(function(r){Object.defineProperty(o,r,Object.getOwnPropertyDescriptor(t,r))})}return o}function x(o,e,t){return(e=T(e))in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}function T(o){var e=w(o,"string");return m(e)=="symbol"?e:e+""}function w(o,e){if(m(o)!="object"||!o)return o;var t=o[Symbol.toPrimitive];if(t!==void 0){var r=t.call(o,e);if(m(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(o)}var I=P.extend("focustrap",{mounted:function(e,t){var r=t.value||{},n=r.disabled;n||(this.createHiddenFocusableElements(e,t),this.bind(e,t),this.autoElementFocus(e,t)),e.setAttribute("data-pd-focustrap",!0),this.$el=e},updated:function(e,t){var r=t.value||{},n=r.disabled;n&&this.unbind(e)},unmounted:function(e){this.unbind(e)},methods:{getComputedSelector:function(e){return':not(.p-hidden-focusable):not([data-p-hidden-focusable="true"])'.concat(e??"")},bind:function(e,t){var r=this,n=t.value||{},u=n.onFocusIn,i=n.onFocusOut;e.$_pfocustrap_mutationobserver=new MutationObserver(function(s){s.forEach(function(l){if(l.type==="childList"&&!e.contains(document.activeElement)){var f=function(a){var c=h(a)?h(a,r.getComputedSelector(e.$_pfocustrap_focusableselector))?a:b(e,r.getComputedSelector(e.$_pfocustrap_focusableselector)):b(a);return g(c)?c:a.nextSibling&&f(a.nextSibling)};v(f(l.nextSibling))}})}),e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_mutationobserver.observe(e,{childList:!0}),e.$_pfocustrap_focusinlistener=function(s){return u&&u(s)},e.$_pfocustrap_focusoutlistener=function(s){return i&&i(s)},e.addEventListener("focusin",e.$_pfocustrap_focusinlistener),e.addEventListener("focusout",e.$_pfocustrap_focusoutlistener)},unbind:function(e){e.$_pfocustrap_mutationobserver&&e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_focusinlistener&&e.removeEventListener("focusin",e.$_pfocustrap_focusinlistener)&&(e.$_pfocustrap_focusinlistener=null),e.$_pfocustrap_focusoutlistener&&e.removeEventListener("focusout",e.$_pfocustrap_focusoutlistener)&&(e.$_pfocustrap_focusoutlistener=null)},autoFocus:function(e){this.autoElementFocus(this.$el,{value:$($({},e),{},{autoFocus:!0})})},autoElementFocus:function(e,t){var r=t.value||{},n=r.autoFocusSelector,u=n===void 0?"":n,i=r.firstFocusableSelector,s=i===void 0?"":i,l=r.autoFocus,f=l===void 0?!1:l,p=b(e,"[autofocus]".concat(this.getComputedSelector(u)));f&&!p&&(p=b(e,this.getComputedSelector(s))),v(p)},onFirstHiddenElementFocus:function(e){var t,r=e.currentTarget,n=e.relatedTarget,u=n===r.$_pfocustrap_lasthiddenfocusableelement||!((t=this.$el)!==null&&t!==void 0&&t.contains(n))?b(r.parentElement,this.getComputedSelector(r.$_pfocustrap_focusableselector)):r.$_pfocustrap_lasthiddenfocusableelement;v(u)},onLastHiddenElementFocus:function(e){var t,r=e.currentTarget,n=e.relatedTarget,u=n===r.$_pfocustrap_firsthiddenfocusableelement||!((t=this.$el)!==null&&t!==void 0&&t.contains(n))?S(r.parentElement,this.getComputedSelector(r.$_pfocustrap_focusableselector)):r.$_pfocustrap_firsthiddenfocusableelement;v(u)},createHiddenFocusableElements:function(e,t){var r=this,n=t.value||{},u=n.tabIndex,i=u===void 0?0:u,s=n.firstFocusableSelector,l=s===void 0?"":s,f=n.lastFocusableSelector,p=f===void 0?"":f,a=function(_){return E("span",{class:"p-hidden-accessible p-hidden-focusable",tabIndex:i,role:"presentation","aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0,onFocus:_?.bind(r)})},c=a(this.onFirstHiddenElementFocus),d=a(this.onLastHiddenElementFocus);c.$_pfocustrap_lasthiddenfocusableelement=d,c.$_pfocustrap_focusableselector=l,c.setAttribute("data-pc-section","firstfocusableelement"),d.$_pfocustrap_firsthiddenfocusableelement=c,d.$_pfocustrap_focusableselector=p,d.setAttribute("data-pc-section","lastfocusableelement"),e.prepend(c),e.append(d)}}});export{I as F};
