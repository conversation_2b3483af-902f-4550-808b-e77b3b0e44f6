import{c as r}from"./createLucideIcon.CxvjiKko.js";import{_ as u}from"./utils.BWEB-mtU.js";import{d as n,c,o,e as i,a as _}from"./index.CpC-7sc3.js";import{t as h,n as m}from"./reactivity.esm-bundler.Bx7uHohy.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=r("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=r("refresh-ccw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]),l=n({__name:"MatchingEmptyState",setup(a,{expose:t}){t();const e={};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),y={class:"text-center py-10 text-surface-500"};function g(a,t,e,s,p,d){return o(),c("div",y," Кандидаты не найдены ")}const B=u(l,[["render",g]]),k=n({__name:"MatchingLoadingState",props:{message:{default:"Выполняется подбор..."},paddingClass:{default:"py-10"}},setup(a,{expose:t}){t();const e={get LoaderIcon(){return f}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),x={class:"mt-2 text-surface-500"};function M(a,t,e,s,p,d){return o(),c("div",{class:m(["text-center flex justify-center items-center gap-3",e.paddingClass])},[i(s.LoaderIcon,{class:"animate-spin"}),_("div",x,h(e.message),1)],2)}const j=u(k,[["render",M]]);export{j as M,$ as R,B as a};
