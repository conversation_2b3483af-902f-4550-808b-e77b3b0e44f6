import{E as Z}from"./ErrorBoundary.aYPzjr8z.js";import{t as g}from"./trpc.CMxyjkwB.js";import K from"./Button.CplYapw1.js";import{D as G,s as J}from"./index.ClGz6GkZ.js";import{D as Q}from"./Dialog.DjvB895c.js";import{I as H}from"./InputText.CPqCR4in.js";import{V as W}from"./Textarea.C8hcWg9_.js";import{D as X}from"./Dropdown.BD5gZ7fY.js";import{S as Y}from"./SecondaryButton.ImrBLtmY.js";import{s as k,f as $,r as ee}from"./utils.D8YPi1lO.js";import{u as ue}from"./useTrpc.CcBnDuWb.js";import{_ as L}from"./utils.BWEB-mtU.js";import{d as M,g as R,o as v,w as r,a as n,e as t,c as h,b as ae,f as P,Z as z,_ as le,l as q,n as oe,h as te,F as ne,r as ie,i as re}from"./index.CpC-7sc3.js";import{r as m,y as N,t as b}from"./reactivity.esm-bundler.Bx7uHohy.js";import{n as O}from"./router.DKcY2uv6.js";import se from"./Toast.BFjVikSW.js";import{P as de,T as ce,a as me}from"./trash.D7SMYTt1.js";import"./createLucideIcon.CxvjiKko.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.CwY1vywt.js";import"./index.DqILEIKx.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.hiPlcmdl.js";import"./index.DUcQAuYR.js";import"./index.CyH7ziOX.js";import"./index.C2Xch34u.js";import"./Select.B5f7pqRM.js";import"./useToast.Cyn6G0qw.js";import"./index.2frgj6Y9.js";import"./index.BtgaEm74.js";import"./index.tSaDDWMG.js";const fe=M({__name:"EditCategoryDialog",props:z({category:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:z(["save","cancel"],["update:isVisible"]),setup(p,{expose:u,emit:f}){u();const e=m([]),s=le(p,"isVisible"),d=p,a=f,o=m({}),y=m(!1),w=m("Создать категорию");q(s,i=>{i&&(o.value={...d.category||{}},w.value=d.category?.id?`Редактировать: ${d.category.name}`:"Создать категорию",y.value=!1)}),q(()=>o.value.name,i=>{const c=String(o.value.slug??"");(!y.value||c.length===0)&&(o.value.slug=k(String(i??"")))});function S(){y.value=!0,o.value.slug=k(String(o.value.slug??""))}function D(){s.value=!1,a("cancel")}function A(){(!o.value.slug||String(o.value.slug).trim()==="")&&o.value.name&&(o.value.slug=k(String(o.value.name))),a("save",{...o.value}),s.value=!1}async function I(){const i=await g.crud.partCategory.findMany.query({select:{id:!0,name:!0},orderBy:{name:"asc"}});e.value=i}oe(()=>{I()});const{media:x}=ue(),C=m(null),_=m(!1),V=te(()=>d.category?.image?.url||o.value?.image?.url||null);function F(i){const c=i.target;c.files&&c.files[0]&&(C.value=c.files[0])}async function T(){if(!(!o.value?.id||!C.value)){_.value=!0;try{const i=await $(C.value);await x.uploadPartCategoryImage({partCategoryId:o.value.id,fileName:C.value.name,fileData:i,mimeType:C.value.type||"image/png"});const c=await g.crud.partCategory.findUnique.query({where:{id:o.value.id},include:{image:!0}});c&&(o.value.image=c.image),C.value=null}finally{_.value=!1}}}async function B(){if(o.value?.id){_.value=!0;try{await x.deletePartCategoryImage({partCategoryId:o.value.id});const i=await g.crud.partCategory.findUnique.query({where:{id:o.value.id},include:{image:!0}});i&&(o.value.image=i.image)}finally{_.value=!1}}}const l={parentOptions:e,isVisible:s,props:d,emit:a,localCategory:o,userEditedSlug:y,dialogTitle:w,onSlugInput:S,handleCancel:D,handleSave:A,loadParentCategories:I,media:x,selectedImage:C,uploading:_,categoryImageUrl:V,onSelectImage:F,uploadImage:T,deleteImage:B,Button:K,Dialog:Q,InputText:H,Textarea:W,Dropdown:X,SecondaryButton:Y,get resolveMediaUrl(){return ee}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}}),ge={class:"flex flex-col gap-4 py-4"},ve={class:"flex flex-col"},pe={class:"flex flex-col"},ye={class:"flex flex-col"},Ce={class:"flex flex-col"},_e={class:"flex flex-col"},he={class:"flex flex-col"},xe={class:"flex items-center gap-3"},Ee={class:"w-24 h-24 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},be=["src"],we={key:1,class:"text-surface-500 text-xs"},Ie={class:"flex flex-col gap-2"},Ve={class:"flex gap-2"},Be={key:0,class:"text-surface-500"},Se={class:"flex justify-end gap-2"};function De(p,u,f,e,s,d){return v(),R(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[5]||(u[5]=a=>e.isVisible=a),modal:"",header:e.dialogTitle,class:"w-auto flex"},{default:r(()=>[n("div",ge,[n("div",ve,[u[6]||(u[6]=n("label",{for:"name"},"Наименование",-1)),t(e.InputText,{id:"name",modelValue:e.localCategory.name,"onUpdate:modelValue":u[0]||(u[0]=a=>e.localCategory.name=a)},null,8,["modelValue"])]),n("div",pe,[u[7]||(u[7]=n("label",{for:"slug"},"URL слаг",-1)),t(e.InputText,{id:"slug",modelValue:e.localCategory.slug,"onUpdate:modelValue":u[1]||(u[1]=a=>e.localCategory.slug=a),onInput:e.onSlugInput},null,8,["modelValue"]),u[8]||(u[8]=n("small",{class:"text-surface-500"},"Автогенерация из названия, можно отредактировать вручную",-1))]),n("div",ye,[u[9]||(u[9]=n("label",{for:"description"},"Описание",-1)),t(e.Textarea,{id:"description",modelValue:e.localCategory.description,"onUpdate:modelValue":u[2]||(u[2]=a=>e.localCategory.description=a),rows:"3"},null,8,["modelValue"])]),n("div",Ce,[u[10]||(u[10]=n("label",{for:"parent"},"Родительская категория",-1)),t(e.Dropdown,{id:"parent",modelValue:e.localCategory.parentId,"onUpdate:modelValue":u[3]||(u[3]=a=>e.localCategory.parentId=a),options:e.parentOptions,optionLabel:"name",optionValue:"id",placeholder:"Выберите родительскую категорию",showClear:""},null,8,["modelValue","options"])]),n("div",_e,[u[11]||(u[11]=n("label",{for:"icon"},"Иконка",-1)),t(e.InputText,{id:"icon",modelValue:e.localCategory.icon,"onUpdate:modelValue":u[4]||(u[4]=a=>e.localCategory.icon=a),placeholder:"Например: engine, filter, etc."},null,8,["modelValue"])]),n("div",he,[u[14]||(u[14]=n("label",null,"Изображение категории",-1)),n("div",xe,[n("div",Ee,[e.categoryImageUrl?(v(),h("img",{key:0,src:e.resolveMediaUrl(e.categoryImageUrl),class:"object-cover w-full h-full"},null,8,be)):(v(),h("span",we,"Нет"))]),n("div",Ie,[n("input",{type:"file",accept:"image/*",onChange:e.onSelectImage},null,32),n("div",Ve,[t(e.Button,{size:"small",disabled:!e.selectedImage||e.uploading,onClick:e.uploadImage},{default:r(()=>u[12]||(u[12]=[P("Загрузить")])),_:1,__:[12]},8,["disabled"]),t(e.Button,{size:"small",severity:"danger",outlined:"",disabled:!e.categoryImageUrl||e.uploading,onClick:e.deleteImage},{default:r(()=>u[13]||(u[13]=[P("Удалить")])),_:1,__:[13]},8,["disabled"])]),e.uploading?(v(),h("small",Be,"Загрузка...")):ae("",!0)])])])]),n("div",Se,[t(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),t(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const Ae=L(fe,[["render",De]]),Fe=M({__name:"CategoryList",props:{initialData:{}},setup(p,{expose:u}){u();const f=m(""),e=m(!1),s=m(null),d=m([]),a=m({}),o=p,y=m(o.initialData),w={id:"ID",name:"Наименование",slug:"URL слаг",description:"Описание",parentId:"Родитель ID",level:"Уровень",path:"Путь",icon:"Иконка"},S=["id","name","slug","description","level","icon"];function D(){s.value={},e.value=!0}function A(l){s.value={...l},e.value=!0}async function I(l){if(l)try{if(l.id){const{id:i}=l,c={name:l.name,slug:l.slug,description:l.description,parentId:l.parentId??null,icon:l.icon},E=Object.fromEntries(Object.entries(c).filter(([j,U])=>U!==void 0));await g.crud.partCategory.update.mutate({where:{id:i},data:E})}else if(l.name&&l.slug){let i="01",c=0;if(l.parentId){const E=await g.crud.partCategory.findUnique.query({where:{id:l.parentId}});if(E){c=E.level+1;const j=await g.crud.partCategory.count.query({where:{parentId:l.parentId}}),U=String(j+1).padStart(2,"0");i=`${E.path}/${U}`}}await g.crud.partCategory.create.mutate({data:{name:l.name,slug:l.slug,description:l.description,parentId:l.parentId,level:c,path:i,icon:l.icon}})}else{console.error("Name and slug are required to create a category.");return}O(window.location.href)}catch(i){console.error("Failed to save category:",i)}finally{e.value=!1}}function x(){e.value=!1,s.value=null}async function C(l){e.value=!1,await g.crud.partCategory.delete.mutate({where:{id:l.id}}),O(window.location.href)}q(f,l=>{_(l)});async function _(l=""){console.log("value",l);const i=l?{AND:[{level:0},{OR:[{name:{contains:l}},{slug:{contains:l}},{description:{contains:l}}]}]}:{level:0};y.value=await g.crud.partCategory.findMany.query({where:i,include:{image:!0,_count:{select:{parts:!0,children:!0}}},orderBy:{name:"asc"}})}async function V(l){if(a.value[l])return a.value[l];const i=await g.crud.partCategory.findMany.query({where:{parentId:l},include:{image:!0,_count:{select:{parts:!0,children:!0}}},orderBy:{name:"asc"}});return a.value[l]=i,i}function F(l){return l._count.children>0}async function T(l){l.data._count.children>0&&await V(l.data.id)}const B={searchValue:f,dialogVisible:e,editingCategory:s,expandedRows:d,childrenCache:a,props:o,items:y,keyMapping:w,columnKeys:S,createCategory:D,editCategory:A,handleSave:I,handleCancel:x,deleteCategory:C,debouncedSearch:_,loadChildren:V,hasChildren:F,onRowExpand:T,Button:K,DataTable:G,get PencilIcon(){return me},get TrashIcon(){return ce},get PlusIcon(){return de},get Column(){return J},EditCategoryDialog:Ae,InputText:H,Toast:se};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),Te={class:"flex justify-between items-center mb-4"},Ue={class:"flex justify-end"},ke={class:"flex items-center"},Re=["title"],Pe={class:"flex gap-2"},qe={class:"p-4 bg-surface-50 dark:bg-surface-800"},Le={class:"mb-3 font-semibold"},Me={key:0},je={class:"flex items-center"},ze=["title"],Ne={class:"flex gap-2"},Oe={key:1,class:"text-surface-500"};function Ke(p,u,f,e,s,d){return v(),h("div",null,[n("div",Te,[u[4]||(u[4]=n("h1",{class:"text-2xl font-bold"},"Категории запчастей",-1)),t(e.Button,{onClick:e.createCategory},{default:r(()=>[t(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[3]||(u[3]=P(" Создать категорию "))]),_:1,__:[3]})]),t(e.DataTable,{"show-headers":"",value:e.items,expandedRows:e.expandedRows,"onUpdate:expandedRows":u[1]||(u[1]=a=>e.expandedRows=a),onRowExpand:e.onRowExpand,rowHover:!0},{header:r(()=>[n("div",Ue,[t(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=a=>e.searchValue=a),placeholder:"Поиск"},null,8,["modelValue"])])]),expansion:r(({data:a})=>[n("div",qe,[n("h5",Le,"Подкатегории: "+b(a.name),1),e.childrenCache[a.id]&&e.childrenCache[a.id].length>0?(v(),h("div",Me,[t(e.DataTable,{value:e.childrenCache[a.id],class:"mb-4"},{default:r(()=>[t(e.Column,{field:"name",header:"Наименование"},{body:r(({data:o})=>[n("div",je,[n("div",{style:N({marginLeft:`${(o.level-a.level-1)*20}px`})},b(o.name),5)])]),_:2},1024),t(e.Column,{field:"slug",header:"URL слаг"}),t(e.Column,{field:"description",header:"Описание"},{body:r(({data:o})=>[n("div",{class:"max-w-xs truncate",title:o.description},b(o.description||"-"),9,ze)]),_:1}),t(e.Column,{field:"_count.parts",header:"Кол-во деталей"}),t(e.Column,{field:"_count.children",header:"Подкатегории"}),t(e.Column,{header:"Действия"},{body:r(({data:o})=>[n("div",Ne,[t(e.Button,{onClick:y=>e.editCategory(o),outlined:"",size:"small"},{default:r(()=>[t(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(e.Button,{onClick:y=>e.deleteCategory(o),outlined:"",severity:"danger",size:"small"},{default:r(()=>[t(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:2},1032,["value"])])):(v(),h("div",Oe," Подкатегории отсутствуют "))])]),default:r(()=>[t(e.Column,{expander:!0,headerStyle:"width: 3rem"}),(v(),h(ne,null,ie(e.columnKeys,a=>t(e.Column,{key:a,field:a,header:e.keyMapping[a]||a},re({_:2},[a==="name"?{name:"body",fn:r(({data:o})=>[n("div",ke,[n("div",{style:N({marginLeft:`${o.level*20}px`})},b(o[a]),5)])]),key:"0"}:a==="description"?{name:"body",fn:r(({data:o})=>[n("div",{class:"max-w-xs truncate",title:o[a]},b(o[a]||"-"),9,Re)]),key:"1"}:void 0]),1032,["field","header"])),64)),t(e.Column,{field:"_count.parts",header:"Кол-во деталей"}),t(e.Column,{field:"_count.children",header:"Подкатегории"}),t(e.Column,{header:"Действия"},{body:r(({data:a})=>[n("div",Pe,[t(e.Button,{onClick:o=>e.editCategory(a),outlined:"",size:"small"},{default:r(()=>[t(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),t(e.Button,{onClick:o=>e.deleteCategory(a),outlined:"",severity:"danger",size:"small"},{default:r(()=>[t(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","expandedRows"]),t(e.EditCategoryDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[2]||(u[2]=a=>e.dialogVisible=a),category:e.editingCategory,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","category"]),t(e.Toast)])}const He=L(Fe,[["render",Ke]]),Ze=M({__name:"CategoryListBoundary",props:{initialData:{}},setup(p,{expose:u}){u();const f=p,e=m(0),d={props:f,key:e,onRetry:()=>{e.value++},ErrorBoundary:Z,CategoryList:He};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}});function Ge(p,u,f,e,s,d){return v(),R(e.ErrorBoundary,{variant:"minimal",title:"Ошибка категорий",message:"Список категорий не отрисовался. Повторите попытку.",onRetry:e.onRetry},{default:r(()=>[(v(),R(e.CategoryList,{initialData:f.initialData,key:e.key},null,8,["initialData"]))]),_:1})}const Tu=L(Ze,[["render",Ge]]);export{Tu as default};
