import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../chunks/AdminLayout_DWqIyijm.mjs';
import { defineComponent, useSSRContext, ref, onMounted, mergeProps, withCtx, createVNode } from 'vue';
import { V as VCard } from '../../chunks/Card_gUaTQQTl.mjs';
import { V as VButton } from '../../chunks/Button_CVFNKcZL.mjs';
import { S as Select } from '../../chunks/Select_CSb2vMfa.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_Ude6CFTv.mjs';
import { D as DataTable, s as script } from '../../chunks/index_BN4ZpeVD.mjs';
import { n as navigate, _ as _export_sfc } from '../../chunks/ClientRouter_Cit0rBg5.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_CFYBp3yo.mjs';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "TemplatesList",
  setup(__props, { expose: __expose }) {
    __expose();
    const trpc = useTrpc();
    const filters = ref({});
    const categoryQuery = ref("");
    const categorySuggestions = ref([]);
    const kindOptions = [
      { label: "\u0412\u0441\u0435", value: void 0 },
      { label: "CATEGORY", value: "CATEGORY" },
      { label: "PART", value: "PART" },
      { label: "CATALOG_ITEM", value: "CATALOG_ITEM" }
    ];
    const rows = ref([]);
    async function load() {
      const data = await trpc.pageTemplates.list.query(filters.value);
      rows.value = data;
    }
    function onCreate() {
      navigate();
    }
    function onCompleteCategory(e) {
      trpc.crud.partCategory.findMany.query({ where: { name: { contains: e.query } }, take: 10 }).then((res) => categorySuggestions.value = res);
    }
    function onSelectCategory(e) {
      filters.value.partCategoryId = e.value?.id;
      load();
    }
    onMounted(load);
    const __returned__ = { trpc, filters, categoryQuery, categorySuggestions, kindOptions, rows, load, onCreate, onCompleteCategory, onSelectCategory, VCard, VButton, VSelect: Select, VAutoComplete, VDataTable: DataTable, get VColumn() {
      return script;
    }, get navigate() {
      return navigate;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-7xl" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center justify-between"${_scopeId}><h2 class="text-lg font-semibold"${_scopeId}>\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0441\u0442\u0440\u0430\u043D\u0438\u0446</h2><div class="flex items-center gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.filters.kind,
          "onUpdate:modelValue": ($event) => $setup.filters.kind = $event,
          options: $setup.kindOptions,
          placeholder: "\u0422\u0438\u043F",
          optionLabel: "label",
          optionValue: "value",
          class: "w-44"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.categoryQuery,
          "onUpdate:modelValue": ($event) => $setup.categoryQuery = $event,
          suggestions: $setup.categorySuggestions,
          optionLabel: "name",
          placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
          class: "w-72",
          onComplete: $setup.onCompleteCategory,
          onItemSelect: $setup.onSelectCategory
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D",
          icon: "",
          onClick: $setup.onCreate
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center justify-between" }, [
            createVNode("h2", { class: "text-lg font-semibold" }, "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0441\u0442\u0440\u0430\u043D\u0438\u0446"),
            createVNode("div", { class: "flex items-center gap-2" }, [
              createVNode($setup["VSelect"], {
                modelValue: $setup.filters.kind,
                "onUpdate:modelValue": ($event) => $setup.filters.kind = $event,
                options: $setup.kindOptions,
                placeholder: "\u0422\u0438\u043F",
                optionLabel: "label",
                optionValue: "value",
                class: "w-44"
              }, null, 8, ["modelValue", "onUpdate:modelValue"]),
              createVNode($setup["VAutoComplete"], {
                modelValue: $setup.categoryQuery,
                "onUpdate:modelValue": ($event) => $setup.categoryQuery = $event,
                suggestions: $setup.categorySuggestions,
                optionLabel: "name",
                placeholder: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
                class: "w-72",
                onComplete: $setup.onCompleteCategory,
                onItemSelect: $setup.onSelectCategory
              }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"]),
              createVNode($setup["VButton"], {
                label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D",
                icon: "",
                onClick: $setup.onCreate
              })
            ])
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VDataTable"], {
          value: $setup.rows,
          dataKey: "id",
          paginator: true,
          rows: 20
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "name",
                header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "kind",
                header: "\u0422\u0438\u043F"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "isDefault",
                header: "\u0414\u0435\u0444\u043E\u043B\u0442"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "isActive",
                header: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                body: withCtx((slotProps, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["VButton"], {
                      label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
                      icon: "",
                      onClick: ($event) => $setup.navigate(`/admin/templates/${slotProps.data.id}`)
                    }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["VButton"], {
                        label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
                        icon: "",
                        onClick: ($event) => $setup.navigate(`/admin/templates/${slotProps.data.id}`)
                      }, null, 8, ["onClick"])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VColumn"], {
                  field: "name",
                  header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435"
                }),
                createVNode($setup["VColumn"], {
                  field: "kind",
                  header: "\u0422\u0438\u043F"
                }),
                createVNode($setup["VColumn"], {
                  field: "isDefault",
                  header: "\u0414\u0435\u0444\u043E\u043B\u0442"
                }),
                createVNode($setup["VColumn"], {
                  field: "isActive",
                  header: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D"
                }),
                createVNode($setup["VColumn"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                  body: withCtx((slotProps) => [
                    createVNode($setup["VButton"], {
                      label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
                      icon: "",
                      onClick: ($event) => $setup.navigate(`/admin/templates/${slotProps.data.id}`)
                    }, null, 8, ["onClick"])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VDataTable"], {
            value: $setup.rows,
            dataKey: "id",
            paginator: true,
            rows: 20
          }, {
            default: withCtx(() => [
              createVNode($setup["VColumn"], {
                field: "name",
                header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435"
              }),
              createVNode($setup["VColumn"], {
                field: "kind",
                header: "\u0422\u0438\u043F"
              }),
              createVNode($setup["VColumn"], {
                field: "isDefault",
                header: "\u0414\u0435\u0444\u043E\u043B\u0442"
              }),
              createVNode($setup["VColumn"], {
                field: "isActive",
                header: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D"
              }),
              createVNode($setup["VColumn"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                body: withCtx((slotProps) => [
                  createVNode($setup["VButton"], {
                    label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
                    icon: "",
                    onClick: ($event) => $setup.navigate(`/admin/templates/${slotProps.data.id}`)
                  }, null, 8, ["onClick"])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["value"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/widgets/templates/TemplatesList.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const TemplatesList = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0441\u0442\u0440\u0430\u043D\u0438\u0446 - PartTec Admin", "showSidebar": true }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "TemplatesList", TemplatesList, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/widgets/templates/TemplatesList.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/cpanel/src/pages/admin/templates/index.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/templates/index.astro";
const $$url = "/admin/templates";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
