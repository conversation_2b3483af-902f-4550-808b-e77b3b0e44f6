import { defineComponent, useSSRContext, ref, mergeProps, withCtx, createBlock, createCommentVNode, openBlock } from 'vue';
import CheckIcon from '@primevue/icons/check';
import MinusIcon from '@primevue/icons/minus';
import { s as script } from './index_DI-L7n6v.mjs';
import { p as ptViewMerge, _ as _export_sfc } from './ClientRouter_Cit0rBg5.mjs';
import { ssrRenderComponent } from 'vue/server-renderer';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Checkbox",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `relative inline-flex select-none w-5 h-5 align-bottom
        p-small:w-4 p-small:h-4
        p-large:w-6 p-large:h-6`,
      input: `peer cursor-pointer disabled:cursor-default appearance-none 
        absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
        border border-transparent rounded-xs`,
      box: `flex justify-center items-center rounded-sm w-5 h-5
        border border-surface-300 dark:border-surface-700
        bg-surface-0 dark:bg-surface-950
        text-surface-700 dark:text-surface-0
        peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
        p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
        peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
        peer-focus-visible:outline-1 peer-focus-visible:outline-offset-2 peer-focus-visible:outline-primary peer-focus-visible:outline 
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        p-disabled:bg-surface-200 dark:p-disabled:bg-surface-400 p-disabled:border-surface-300 dark:p-disabled:border-surface-700 p-disabled:text-surface-700 dark:p-disabled:text-surface-400
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200
        p-small:w-4 p-small:h-4
        p-large:w-6 p-large:h-6`,
      icon: `text-sm w-[0.875rem] h-[0.875rem] transition-none
        p-small:w-3 p-small:h-3
        p-large:w-4 p-large:h-4`
    });
    const __returned__ = { theme, get CheckIcon() {
      return CheckIcon;
    }, get MinusIcon() {
      return MinusIcon;
    }, get Checkbox() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Checkbox"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    icon: withCtx(({ checked, indeterminate, dataP }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if (checked) {
          _push2(ssrRenderComponent($setup["CheckIcon"], {
            class: $setup.theme.icon,
            "data-p": dataP
          }, null, _parent2, _scopeId));
        } else if (indeterminate) {
          _push2(ssrRenderComponent($setup["MinusIcon"], {
            class: $setup.theme.icon,
            "data-p": dataP
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          checked ? (openBlock(), createBlock($setup["CheckIcon"], {
            key: 0,
            class: $setup.theme.icon,
            "data-p": dataP
          }, null, 8, ["class", "data-p"])) : indeterminate ? (openBlock(), createBlock($setup["MinusIcon"], {
            key: 1,
            class: $setup.theme.icon,
            "data-p": dataP
          }, null, 8, ["class", "data-p"])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Checkbox.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const VCheckbox = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { VCheckbox as V };
