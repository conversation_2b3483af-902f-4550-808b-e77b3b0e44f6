import{t as W}from"./trpc.CMxyjkwB.js";import fe from"./Button.CplYapw1.js";import{D as ze,s as je}from"./index.ClGz6GkZ.js";import{D as Se}from"./Dialog.DjvB895c.js";import{I as Ve}from"./InputText.CPqCR4in.js";import{D as at}from"./Dropdown.BD5gZ7fY.js";import{S as lt}from"./SecondaryButton.ImrBLtmY.js";import{_ as ve}from"./utils.BWEB-mtU.js";import{d as ge,g as k,o as i,w as d,a,e as r,Z as Ge,_ as rt,l as ce,n as qe,c as s,F,r as j,b as x,f as y,h as T,$ as nt,i as it}from"./index.CpC-7sc3.js";import{r as E,t as g,n as de}from"./reactivity.esm-bundler.Bx7uHohy.js";import{T as Ae}from"./Tag.BtN2Bjhy.js";import st from"./Card.DllwvXut.js";import{V as ot}from"./AutoComplete.WqgqstcD.js";import{A as He}from"./AttributeValueInput.CrEMZkDz.js";import{o as G,n as I,s as M,r as U,u as w,c as $,a as Je,b as K,d as We}from"./schemas.BR5-L2eu.js";import{d as _e}from"./coerce.CW6lkyCY.js";import{o as Ke,s as me,u as Qe,n as pe,b as Me,d as Ne,r as dt,a as Pe,c as ct,e as mt}from"./types.FgRm47Sn.js";import{I as Fe}from"./Icon.DGPcirKX.js";/* empty css                       */import{c as be}from"./createLucideIcon.CxvjiKko.js";import{C as pt}from"./check.BdaJEI8c.js";import{T as Xe,a as Ze,P as Ye}from"./trash.D7SMYTt1.js";import{S as ft}from"./Select.B5f7pqRM.js";import{V as vt}from"./Message.DXe4eGzY.js";import{u as $e}from"./useTrpc.CcBnDuWb.js";import{u as gt}from"./useToast.Cyn6G0qw.js";import{I as bt}from"./info.D2H3qZLp.js";import{S as ht}from"./search.ChgJir38.js";import{T as yt}from"./tags.HHlfcBcj.js";import{C as xt}from"./chevron-down.bWdMazzL.js";import{n as Ie}from"./router.DKcY2uv6.js";import"./index.DV5zenSI.js";import"./index.CMLtULFQ.js";import"./index.Cl2VmfYg.js";import"./index.DCNsBfCe.js";import"./index.BRRJVlxZ.js";import"./index.DBjPSdxz.js";import"./runtime-dom.esm-bundler.0NVQG2L5.js";import"./index.CwY1vywt.js";import"./index.DqILEIKx.js";import"./index.CbINUYrU.js";import"./index.BuLnfHxv.js";import"./index.hiPlcmdl.js";import"./index.DUcQAuYR.js";import"./index.CyH7ziOX.js";import"./index.C2Xch34u.js";import"./index.sX0JnRp_.js";import"./index.tSaDDWMG.js";import"./InputNumber.8Wyucp72.js";import"./Checkbox.Czip7_Ii.js";import"./Textarea.C8hcWg9_.js";import"./index.B3SmZZpj.js";/* empty css                            */import"./index.2frgj6Y9.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=be("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Et=be("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=be("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const At=be("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=be("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ct=ge({__name:"EditEquipmentDialog",props:Ge({equipment:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:Ge(["save","cancel"],["update:isVisible"]),setup(p,{expose:u,emit:c}){u();const e=E([]),f=rt(p,"isVisible"),m=p,t=c,l=E({}),o=E("Создать модель техники");ce(f,C=>{C&&(l.value={...m.equipment||{}},o.value=m.equipment?.id?`Редактировать: ${m.equipment.name}`:"Создать модель техники")});function A(){f.value=!1,t("cancel")}function D(){t("save",{...l.value}),f.value=!1}async function B(){const C=await W.crud.brand.findMany.query({select:{id:!0,name:!0},orderBy:{name:"asc"}});e.value=C}qe(()=>{B()});const S={brandOptions:e,isVisible:f,props:m,emit:t,localEquipment:l,dialogTitle:o,handleCancel:A,handleSave:D,loadBrands:B,Button:fe,Dialog:Se,InputText:Ve,Dropdown:at,SecondaryButton:lt};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),Dt={class:"flex flex-col gap-4 py-4"},Tt={class:"flex flex-col"},wt={class:"flex flex-col"},It={class:"flex justify-end gap-2"};function Bt(p,u,c,e,f,m){return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[2]||(u[2]=t=>e.isVisible=t),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:d(()=>[a("div",Dt,[a("div",Tt,[u[3]||(u[3]=a("label",{for:"name"},"Наименование модели",-1)),r(e.InputText,{id:"name",modelValue:e.localEquipment.name,"onUpdate:modelValue":u[0]||(u[0]=t=>e.localEquipment.name=t),placeholder:"Например: Экскаватор CAT 320D"},null,8,["modelValue"])]),a("div",wt,[u[4]||(u[4]=a("label",{for:"brand"},"Бренд",-1)),r(e.Dropdown,{id:"brand",modelValue:e.localEquipment.brandId,"onUpdate:modelValue":u[1]||(u[1]=t=>e.localEquipment.brandId=t),options:e.brandOptions,optionLabel:"name",optionValue:"id",placeholder:"Выберите бренд",showClear:""},null,8,["modelValue","options"])])]),a("div",It,[r(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),r(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const St=ve(Ct,[["render",Bt]]),Ce=G({id:I(),value:M(),numericValue:I().nullish()}).strict(),Vt=G({equipmentModel:U(w()),template:U(w())}),Re=G({equipmentModelId:M(),templateId:I()}),qt=Ce;qt.merge(Re).merge(Vt.partial());Ce.partial().passthrough();G({id:$([I(),U(w())]),value:M(),numericValue:$([I().nullish(),U(w())])}).partial().passthrough();const Mt=Ce.partial({id:!0});Mt.merge(Re);const Nt=Ce.partial();Nt.merge(Re.partial());const tt=Je(["STRING","NUMBER","BOOLEAN","DATE","JSON"]),ut=Je(["MM","INCH","FT","G","KG","T","LB","ML","L","GAL","SEC","MIN","H","PCS","SET","PAIR","BAR","PSI","KW","HP","NM","RPM","C","F","PERCENT"]),De=G({id:I(),name:M(),title:M(),description:M().nullish(),dataType:tt,unit:ut.nullish(),isRequired:We().default(!1),minValue:I().nullish(),maxValue:I().nullish(),allowedValues:K(M()),tolerance:I().default(0).nullish(),createdAt:_e().default(()=>new Date),updatedAt:_e()}).strict(),Ft=G({group:U(w()).optional(),partAttributes:K(w()).optional(),catalogItemAttributes:K(w()).optional(),equipmentAttributes:K(w()).optional(),synonymGroups:K(w()).optional()}),Oe=G({groupId:I().nullish()}),Rt=De;Rt.merge(Oe).merge(Ft.partial());De.partial().passthrough();G({id:$([I(),U(w())]),name:M(),title:M(),description:M().nullish(),dataType:tt,unit:ut.nullish(),isRequired:We().default(!1),minValue:$([I().nullish(),U(w())]),maxValue:$([I().nullish(),U(w())]),allowedValues:K(M()),tolerance:$([I().default(0).nullish(),U(w())]),createdAt:_e().default(()=>new Date),updatedAt:_e()}).partial().passthrough();const Ot=De.partial({id:!0,dataType:!0,isRequired:!0,allowedValues:!0,tolerance:!0,createdAt:!0,updatedAt:!0});Ot.merge(Oe);const Lt=De.partial();Lt.merge(Oe.partial());const Te=G({id:I(),name:M(),description:M().nullish()}).strict(),Gt=G({templates:K(w()).optional(),parent:U(w()).optional(),children:K(w()).optional()}),Le=G({parentId:I().nullish()}),Pt=Te;Pt.merge(Le).merge(Gt.partial());Te.partial().passthrough();G({id:$([I(),U(w())]),name:M(),description:M().nullish()}).partial().passthrough();const Ut=Te.partial({id:!0});Ut.merge(Le);const zt=Te.partial();zt.merge(Le.partial());const jt=p=>{switch(p.dataType){case"STRING":let u=me().min(1,"Значение не может быть пустым");return p.allowedValues&&p.allowedValues.length>0?mt(p.allowedValues):u;case"NUMBER":let c=pe({required_error:"Значение обязательно",invalid_type_error:"Значение должно быть числом"});return p.minValue!==null&&p.minValue!==void 0&&(c=c.min(p.minValue,`Минимальное значение: ${p.minValue}`)),p.maxValue!==null&&p.maxValue!==void 0&&(c=c.max(p.maxValue,`Максимальное значение: ${p.maxValue}`)),c;case"BOOLEAN":return Me();case"DATE":return Ne({required_error:"Дата обязательна",invalid_type_error:"Неверный формат даты"});case"JSON":return dt(Pe()).or(ct(Pe()));default:return me()}};Ke({templateId:pe().min(1,"Выберите шаблон атрибута"),value:Qe([me(),pe(),Me(),Ne()]),equipmentModelId:me().min(1,"ID модели техники обязателен")});Ke({id:pe().min(1,"ID атрибута обязателен"),value:Qe([me(),pe(),Me(),Ne()])});function Be(p){const{value:u,template:c}=p,{dataType:e,unit:f}=c;let m,t;try{switch(e){case"STRING":t=u,m=u;break;case"NUMBER":t=parseFloat(u),m=isNaN(t)?u:t.toLocaleString("ru-RU"),f&&(m+=` ${Ee(f)}`);break;case"BOOLEAN":t=u.toLowerCase()==="true",m=t?"Да":"Нет";break;case"DATE":t=new Date(u),m=isNaN(t.getTime())?u:t.toLocaleDateString("ru-RU");break;case"JSON":try{t=JSON.parse(u),m=JSON.stringify(t,null,2)}catch{t=u,m=u}break;default:t=u,m=u}}catch{t=u,m=u}return{displayValue:m,rawValue:t,unit:f,dataType:e}}function xe(p){const u={};return p.forEach(c=>{const e=c.template.group?.name||"Общие";u[e]||(u[e]=[]),u[e].push(c)}),Object.keys(u).forEach(c=>{u[c].sort((e,f)=>e.template.title.localeCompare(f.template.title,"ru"))}),u}function Ht(p,u,c){const e=new Set(u.map(m=>m.templateId));let f=p.filter(m=>!e.has(m.id));if(c){if(c.excludeTemplateIds){const m=new Set(c.excludeTemplateIds);f=f.filter(t=>!m.has(t.id))}if(c.groupId&&(f=f.filter(m=>m.groupId===c.groupId)),c.dataType&&(f=f.filter(m=>m.dataType===c.dataType)),c.searchQuery){const m=c.searchQuery.toLowerCase();f=f.filter(t=>t.title.toLowerCase().includes(m)||t.name.toLowerCase().includes(m)||t.description&&t.description.toLowerCase().includes(m))}}return f.sort((m,t)=>m.title.localeCompare(t.title,"ru"))}function Ee(p){return p?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[p]||p:""}function ke(p){return{STRING:"Текст",NUMBER:"Число",BOOLEAN:"Да/Нет",DATE:"Дата",JSON:"JSON"}[p]||p}function Ue(p,u){switch(u){case"NUMBER":const c=parseFloat(p);return isNaN(c)?0:c;case"BOOLEAN":return p.toLowerCase()==="true"||p==="1"||p==="да";case"DATE":const e=new Date(p);return isNaN(e.getTime())?new Date:e;case"JSON":try{return JSON.parse(p)}catch{return p}default:return p}}const Jt=ge({__name:"EquipmentAttributesList",props:{attributes:{},readonly:{type:Boolean,default:!1},showGroupColumn:{type:Boolean,default:!0},showDataTypeColumn:{type:Boolean,default:!1},compact:{type:Boolean,default:!1},groupByTemplate:{type:Boolean,default:!1}},emits:["edit-attribute","delete-attribute","update-value"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,m=E(null),t=E(null),l=T(()=>e.attributes.map(b=>{const N=Be(b);return{id:b.id,name:b.template.title,value:N.displayValue,rawValue:N.rawValue,unit:N.unit,dataType:b.template.dataType,dataTypeDisplay:ke(b.template.dataType),group:b.template.group?.name||"Общие",description:b.template.description,isRequired:b.template.isRequired,template:b.template,attribute:b}})),o=T(()=>e.groupByTemplate?xe(e.attributes):{});function A(b){f("edit-attribute",b)}function D(b){f("delete-attribute",b)}function B(b){switch(b){case"NUMBER":return"info";case"BOOLEAN":return"success";case"DATE":return"warning";case"JSON":return"secondary";default:return}}function S(b){if(b.dataType==="BOOLEAN")return b.rawValue?"✓":"✗";if(b.dataType==="NUMBER"&&b.unit){const N=Ee(b.unit);return`${b.rawValue.toLocaleString("ru-RU")} ${N}`}return b.value}function C(b){m.value=b.id,t.value=b.rawValue}function L(){m.value=null,t.value=null}function R(b){t.value!==null&&t.value!==void 0&&f("update-value",b,t.value),L()}function Q(b){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[b]||"pi pi-question"}const H={props:e,emit:f,editingAttributeId:m,editingValue:t,tableData:l,groupedAttributes:o,handleEditAttribute:A,handleDeleteAttribute:D,getValueSeverity:B,formatValueForDisplay:S,startEditing:C,cancelEditing:L,saveEditing:R,getDataTypeIcon:Q,get PencilIcon(){return Ze},get TrashIcon(){return Xe},get CheckIcon(){return pt},get XIcon(){return et},Button:fe,Tag:Ae,DataTable:ze,get Column(){return je},AttributeValueInput:He,get formatAttributeValue(){return Be},get getUnitDisplayName(){return Ee},Icon:Fe};return Object.defineProperty(H,"__isScriptSetup",{enumerable:!1,value:!0}),H}}),Wt={class:"equipment-attributes-list"},Kt={key:0,class:"space-y-6"},Qt={class:"flex items-center gap-2 mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},Xt={class:"font-medium text-surface-900 dark:text-surface-0"},Zt={class:"space-y-3"},Yt={class:"flex-shrink-0 relative"},$t=["title"],eu={class:"flex-shrink-0 w-48"},tu={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},uu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},au={class:"flex-1"},lu={key:0,class:"flex items-center gap-2"},ru={class:"flex gap-1"},nu={key:1,class:"flex items-center gap-2 group"},iu={class:"text-surface-900 dark:text-surface-0"},su={class:"flex-shrink-0 w-16 text-center"},ou={key:0,class:"text-sm text-surface-500 font-medium"},du={key:0,class:"flex-shrink-0 flex gap-2"},cu={class:"flex items-start gap-2"},mu={class:"flex-1 min-w-0"},pu={class:"flex items-center gap-2 mb-1"},fu={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},vu=["title"],gu={key:0,class:"flex items-center gap-2"},bu={class:"flex gap-1"},hu={key:1,class:"flex items-center gap-2 group"},yu=["title"],xu={class:"flex items-center gap-1"};function _u(p,u,c,e,f,m){return i(),s("div",Wt,[c.groupByTemplate&&Object.keys(e.groupedAttributes).length>0?(i(),s("div",Kt,[(i(!0),s(F,null,j(e.groupedAttributes,(t,l)=>(i(),s("div",{key:l,class:"attribute-group"},[a("div",Qt,[u[2]||(u[2]=a("i",{class:"pi pi-folder text-blue-600"},null,-1)),a("h4",Xt,g(l==="undefined"?"Без группы":l),1),r(e.Tag,{value:`${t.length} атр.`,severity:"secondary",size:"small"},null,8,["value"])]),a("div",Zt,[(i(!0),s(F,null,j(t,o=>(i(),s("div",{key:o.id,class:"attribute-item"},[a("div",{class:de(["flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm",[o.value&&String(o.value).trim()?"border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10":"border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"]])},[a("div",Yt,[r(e.Icon,{name:e.getDataTypeIcon(o.template.dataType),class:"text-lg text-primary w-5 h-5"},null,8,["name"]),a("div",{class:de(["absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900",o.value&&String(o.value).trim()?"bg-green-500":"bg-orange-500"]),title:o.value&&String(o.value).trim()?"Заполнено":"Не заполнено"},null,10,$t)]),a("div",eu,[a("div",tu,[y(g(o.template.title)+" ",1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs ml-1"},{default:d(()=>u[3]||(u[3]=[y("*")])),_:1,__:[3]})):x("",!0)]),o.template.description?(i(),s("div",uu,g(o.template.description),1)):x("",!0)]),a("div",au,[e.editingAttributeId===o.id?(i(),s("div",lu,[r(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[0]||(u[0]=A=>e.editingValue=A),template:o.template,class:"flex-1",size:"small"},null,8,["modelValue","template"]),a("div",ru,[r(e.Button,{onClick:A=>e.saveEditing(o.id),size:"small",class:"p-1"},{default:d(()=>[r(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[r(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),s("div",nu,[a("span",iu,g(e.formatAttributeValue(o).displayValue||"Не указано"),1),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:A=>e.startEditing(o),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]))]))]),a("div",su,[o.template.unit?(i(),s("span",ou,g(e.getUnitDisplayName(o.template.unit)),1)):x("",!0)]),c.readonly?x("",!0):(i(),s("div",du,[r(e.Button,{onClick:A=>e.handleEditAttribute(o.id),severity:"secondary",size:"small",outlined:"",class:"p-1"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:A=>e.handleDeleteAttribute(o.id),severity:"danger",size:"small",outlined:"",class:"p-1",disabled:o.template.isRequired},{default:d(()=>[r(e.TrashIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick","disabled"])]))],2)]))),128))])]))),128))])):(i(),k(e.DataTable,{key:1,value:e.tableData,paginator:!c.compact&&e.tableData.length>10,rows:c.compact?5:10,rowsPerPageOptions:[5,10,25,50],sortMode:"multiple",removableSort:"",loading:!1,dataKey:"id",size:c.compact?"small":"normal",class:de(["p-datatable-sm",{"compact-table":c.compact}])},{empty:d(()=>u[6]||(u[6]=[a("div",{class:"text-center py-6 text-surface-500 dark:text-surface-400"},[a("i",{class:"pi pi-info-circle text-2xl mb-2 block"}),a("p",{class:"text-sm"},"Нет атрибутов для отображения")],-1)])),default:d(()=>[r(e.Column,{field:"name",header:"Атрибут",sortable:"",style:{minWidth:"200px"}},{body:d(({data:t})=>[a("div",cu,[a("div",mu,[a("div",pu,[a("span",fu,g(t.name),1),t.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[4]||(u[4]=[y(" Обязательный ")])),_:1,__:[4]})):x("",!0)]),t.description&&!c.compact?(i(),s("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:t.description},g(t.description),9,vu)):x("",!0)])])]),_:1}),r(e.Column,{field:"value",header:"Значение",sortable:"",style:{minWidth:"150px"}},{body:d(({data:t})=>[e.editingAttributeId===t.id?(i(),s("div",gu,[r(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[1]||(u[1]=l=>e.editingValue=l),template:t.template,class:"w-full",size:"small"},null,8,["modelValue","template"]),a("div",bu,[r(e.Button,{onClick:l=>e.saveEditing(t.id),size:"small",class:"p-1"},{default:d(()=>[r(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[r(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),s("div",hu,[r(e.Tag,{severity:e.getValueSeverity(t.dataType),class:de(["font-mono text-sm",{"text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20":t.dataType==="BOOLEAN"&&t.rawValue,"text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20":t.dataType==="BOOLEAN"&&!t.rawValue}])},{default:d(()=>[y(g(e.formatValueForDisplay(t)),1)]),_:2},1032,["severity","class"]),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:l=>e.startEditing(t),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"])),c.compact&&!c.showDataTypeColumn?(i(),s("span",{key:1,class:"text-xs text-surface-400 dark:text-surface-500",title:`Тип: ${t.dataTypeDisplay}`},g(t.dataType),9,yu)):x("",!0)]))]),_:1}),c.showGroupColumn?(i(),k(e.Column,{key:0,field:"group",header:"Группа",sortable:"",style:{minWidth:"120px"}},{body:d(({data:t})=>[r(e.Tag,{severity:"secondary",class:"text-xs"},{default:d(()=>[u[5]||(u[5]=a("i",{class:"pi pi-folder text-xs mr-1"},null,-1)),y(" "+g(t.group),1)]),_:2,__:[5]},1024)]),_:1})):x("",!0),c.showDataTypeColumn?(i(),k(e.Column,{key:1,field:"dataTypeDisplay",header:"Тип",sortable:"",style:{minWidth:"100px"}},{body:d(({data:t})=>[r(e.Tag,{severity:e.getValueSeverity(t.dataType),class:"text-xs"},{default:d(()=>[y(g(t.dataTypeDisplay),1)]),_:2},1032,["severity"])]),_:1})):x("",!0),c.readonly?x("",!0):(i(),k(e.Column,{key:2,field:"actions",header:"Действия",sortable:!1,style:{minWidth:"120px",width:"120px"}},{body:d(({data:t})=>[a("div",xu,[r(e.Button,{onClick:l=>e.handleEditAttribute(t.id),text:"",size:"small",class:"p-2",title:`Редактировать ${t.name}`},{default:d(()=>[r(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title"]),r(e.Button,{onClick:l=>e.handleDeleteAttribute(t.id),text:"",severity:"danger",size:"small",class:"p-2",title:`Удалить ${t.name}`,disabled:t.isRequired},{default:d(()=>[r(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title","disabled"])])]),_:1}))]),_:1},8,["value","paginator","rows","size","class"]))])}const Eu=ve(Jt,[["render",_u],["__scopeId","data-v-b449289d"]]),ku=ge({__name:"AddEquipmentAttributeDialog",props:{visible:{type:Boolean},equipmentId:{},existingAttributes:{default:()=>[]}},emits:["update:visible","save","cancel"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,{$trpc:m}=$e(),t=gt(),l=E({templateId:0,value:"",template:void 0}),o=E(!1),A=E(!1),D=E(""),B=E(null),S=E(null),C=E([]),L=E([]),R=E([]),Q=T({get:()=>e.visible,set:v=>f("update:visible",v)}),H=T(()=>{const v={searchQuery:D.value||void 0,groupId:B.value||void 0,dataType:S.value||void 0,excludeTemplateIds:e.existingAttributes.map(n=>n.templateId)};return Ht(L.value,e.existingAttributes,v)}),b=T(()=>L.value.find(v=>v.id===l.value.templateId)||null),N=T(()=>l.value.templateId>0&&l.value.value!==null&&l.value.value!==""&&C.value.length===0),z=T(()=>["STRING","NUMBER","BOOLEAN","DATE","JSON"].map(n=>({label:ke(n),value:n}))),le=T(()=>[{label:"Все группы",value:null},...R.value.map(v=>({label:v.name,value:v.id}))]);async function X(){o.value=!0;try{const v=await m.crud.attributeTemplate.findMany.query({include:{group:!0},orderBy:[{group:{name:"asc"}},{title:"asc"}]});L.value=v}catch(v){t.error("Не удалось загрузить шаблоны атрибутов"),console.error("Failed to load templates:",v)}finally{o.value=!1}}async function Z(){try{const v=await m.crud.attributeGroup.findMany.query({orderBy:{name:"asc"}});R.value=v}catch(v){console.error("Failed to load attribute groups:",v)}}function re(v){l.value.templateId=v.id,l.value.template=v,ee(),D.value=""}function ee(){const v=b.value;if(!v){l.value.value="";return}switch(v.dataType){case"STRING":l.value.value="";break;case"NUMBER":l.value.value=v.minValue||0;break;case"BOOLEAN":l.value.value=!1;break;case"DATE":l.value.value=new Date;break;case"JSON":l.value.value="";break;default:l.value.value=""}J()}function J(){C.value=[];const v=b.value;if(!(!v||l.value.value===null||l.value.value===""))try{const n=jt(v),h=Ue(String(l.value.value),v.dataType);n.parse(h)}catch(n){n.errors?C.value=n.errors.map(h=>h.message):C.value=["Неверное значение атрибута"]}}async function te(){if(N.value){A.value=!0;try{const v=b.value,n=Ue(String(l.value.value),v.dataType),h={templateId:l.value.templateId,value:n,template:v};f("save",h),ue()}catch(v){t.error("Ошибка при сохранении атрибута"),console.error("Save error:",v)}finally{A.value=!1}}}function ue(){l.value={templateId:0,value:"",template:void 0},D.value="",B.value=null,S.value=null,C.value=[],f("cancel")}function ne(){D.value="",B.value=null,S.value=null}function ie(v){const n=[];return v.group&&n.push(v.group.name),n.push(ke(v.dataType)),v.unit&&n.push(Ee(v.unit)),v.isRequired&&n.push("Обязательный"),n.join(" • ")}ce(()=>e.visible,v=>{v&&(X(),Z())}),ce(()=>l.value.value,()=>{J()},{deep:!0}),ce(()=>l.value.templateId,()=>{J()}),qe(()=>{e.visible&&(X(),Z())});const ae={props:e,emit:f,$trpc:m,toast:t,formData:l,loading:o,saving:A,searchQuery:D,selectedGroupId:B,selectedDataType:S,validationErrors:C,allTemplates:L,attributeGroups:R,isVisible:Q,availableTemplates:H,selectedTemplate:b,isFormValid:N,dataTypeOptions:z,groupOptions:le,loadTemplates:X,loadAttributeGroups:Z,selectTemplate:re,resetValue:ee,validateValue:J,handleSave:te,handleCancel:ue,clearFilters:ne,getTemplateDisplayInfo:ie,get SearchIcon(){return ht},get XIcon(){return et},get InfoIcon(){return bt},Dialog:Se,Button:fe,InputText:Ve,Select:ft,Tag:Ae,Message:vt,AttributeValueInput:He};return Object.defineProperty(ae,"__isScriptSetup",{enumerable:!1,value:!0}),ae}}),Au={class:"space-y-6"},Cu={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg"},Du={class:"relative"},Tu={class:"flex justify-between items-center mb-4"},wu={class:"text-sm text-surface-600 dark:text-surface-400"},Iu={class:"max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg"},Bu={key:0,class:"p-4 text-center text-surface-500"},Su={key:1,class:"p-4 text-center text-surface-500"},Vu={key:2,class:"divide-y divide-surface-200 dark:divide-surface-700"},qu=["onClick"],Mu={class:"flex items-start justify-between"},Nu={class:"flex-1"},Fu={class:"flex items-center gap-2 mb-1"},Ru={class:"font-medium text-surface-900 dark:text-surface-0"},Ou={class:"text-sm text-surface-600 dark:text-surface-400 mb-2"},Lu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2"},Gu={key:0,class:"ml-4"},Pu={key:0},Uu={class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"},zu={class:"p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4"},ju={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},Hu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},Ju={class:"space-y-4"},Wu={key:0,class:"mt-4"},Ku={class:"flex justify-end gap-2"};function Qu(p,u,c,e,f,m){const t=nt("Icon");return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[4]||(u[4]=l=>e.isVisible=l),modal:"",closable:!0,draggable:!1,class:"w-full max-w-4xl",header:"Добавить атрибут"},{footer:d(()=>[a("div",Ku,[r(e.Button,{onClick:e.handleCancel,outlined:"",disabled:e.saving},{default:d(()=>u[13]||(u[13]=[y(" Отмена ")])),_:1,__:[13]},8,["disabled"]),r(e.Button,{onClick:e.handleSave,disabled:!e.isFormValid||e.saving,loading:e.saving},{default:d(()=>[y(g(e.saving?"Сохранение...":"Добавить атрибут"),1)]),_:1},8,["disabled","loading"])])]),default:d(()=>[a("div",Au,[a("div",null,[u[11]||(u[11]=a("h6",{class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"}," Выберите шаблон атрибута ",-1)),a("div",Cu,[a("div",Du,[r(e.InputText,{modelValue:e.searchQuery,"onUpdate:modelValue":u[0]||(u[0]=l=>e.searchQuery=l),placeholder:"Поиск по названию...",class:"w-full pl-10"},null,8,["modelValue"]),r(e.SearchIcon,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400"})]),r(e.Select,{modelValue:e.selectedGroupId,"onUpdate:modelValue":u[1]||(u[1]=l=>e.selectedGroupId=l),options:e.groupOptions,"option-label":"label","option-value":"value",placeholder:"Выберите группу",class:"w-full","show-clear":!0},null,8,["modelValue","options"]),r(e.Select,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[2]||(u[2]=l=>e.selectedDataType=l),options:e.dataTypeOptions,"option-label":"label","option-value":"value",placeholder:"Тип данных",class:"w-full","show-clear":!0},null,8,["modelValue","options"])]),a("div",Tu,[a("span",wu," Найдено шаблонов: "+g(e.availableTemplates.length),1),r(e.Button,{onClick:e.clearFilters,text:"",size:"small",class:"text-sm"},{default:d(()=>[r(e.XIcon,{class:"w-4 h-4 mr-1"}),u[5]||(u[5]=y(" Очистить фильтры "))]),_:1,__:[5]})]),a("div",Iu,[e.loading?(i(),s("div",Bu,[r(t,{name:"pi pi-spinner pi-spin",class:"mr-2 inline-block"}),u[6]||(u[6]=y(" Загрузка шаблонов... "))])):e.availableTemplates.length===0?(i(),s("div",Su,[r(e.InfoIcon,{class:"w-5 h-5 mx-auto mb-2"}),u[7]||(u[7]=a("p",{class:"text-sm"},"Нет доступных шаблонов",-1)),u[8]||(u[8]=a("p",{class:"text-xs mt-1"},"Попробуйте изменить фильтры или все шаблоны уже используются",-1))])):(i(),s("div",Vu,[(i(!0),s(F,null,j(e.availableTemplates,l=>(i(),s("div",{key:l.id,class:de(["p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors",{"bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary":e.formData.templateId===l.id}]),onClick:o=>e.selectTemplate(l)},[a("div",Mu,[a("div",Nu,[a("div",Fu,[a("h6",Ru,g(l.title),1),l.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs"},{default:d(()=>u[9]||(u[9]=[y(" Обязательный ")])),_:1,__:[9]})):x("",!0)]),a("p",Ou,g(e.getTemplateDisplayInfo(l)),1),l.description?(i(),s("p",Lu,g(l.description),1)):x("",!0)]),e.formData.templateId===l.id?(i(),s("div",Gu,u[10]||(u[10]=[a("i",{class:"pi pi-check text-primary text-lg"},null,-1)]))):x("",!0)])],10,qu))),128))]))])]),e.selectedTemplate?(i(),s("div",Pu,[a("h6",Uu,' Введите значение для "'+g(e.selectedTemplate.title)+'" ',1),a("div",zu,[a("div",ju,[r(e.InfoIcon,{class:"w-4 h-4"}),a("span",null,g(e.getTemplateDisplayInfo(e.selectedTemplate)),1)]),e.selectedTemplate.description?(i(),s("p",Hu,g(e.selectedTemplate.description),1)):x("",!0)]),a("div",Ju,[u[12]||(u[12]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Значение ",-1)),r(e.AttributeValueInput,{modelValue:e.formData.value,"onUpdate:modelValue":u[3]||(u[3]=l=>e.formData.value=l),template:e.selectedTemplate,placeholder:"Введите значение атрибута",class:"w-full"},null,8,["modelValue","template"])]),e.validationErrors.length>0?(i(),s("div",Wu,[(i(!0),s(F,null,j(e.validationErrors,l=>(i(),k(e.Message,{key:l,severity:"error",closable:!1,class:"mb-2"},{default:d(()=>[y(g(l),1)]),_:2},1024))),128))])):x("",!0)])):x("",!0)])]),_:1},8,["visible"])}const Xu=ve(ku,[["render",Qu],["__scopeId","data-v-cb27c7ef"]]),oe=6,Zu=ge({__name:"EquipmentAttributesSection",props:{equipmentId:{},attributes:{},readonly:{type:Boolean,default:!1}},emits:["add-attribute","edit-attribute","delete-attribute"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,{client:m}=$e(),t=E(!1),l=E("grid"),o=E(!1),A=E(!1),D=E(null),B=E(null),S=E([]),C=E([]),L=E([]),R=E(!1),Q=T(()=>e.attributes?xe(e.attributes):{}),H=T(()=>e.attributes&&e.attributes.length>0),b=T(()=>e.attributes?.length||0),N=T(()=>b.value>oe),z=T(()=>e.attributes?t.value||b.value<=oe?e.attributes:e.attributes.slice(0,oe):[]),le=T(()=>z.value.length?xe(z.value):{}),X=T(()=>Math.max(0,b.value-oe)),Z=T(()=>e.attributes?.filter(_=>_.value&&String(_.value).trim()).length||0);function re(){o.value=!0}function ee(_){f("add-attribute",_),o.value=!1}function J(){o.value=!1}function te(_){f("edit-attribute",_)}function ue(_){f("delete-attribute",_)}function ne(){t.value=!t.value}function ie(){l.value=l.value==="grid"?"table":"grid"}function ae(_){return Be(_)}const v=T(()=>!e.readonly);async function n(_){const V=_.query.toLowerCase();try{const q=await m.crud.attributeGroup.findMany.query({where:{name:{contains:V,mode:"insensitive"}},take:10});S.value=q||[]}catch(q){console.error("Error filtering groups:",q),S.value=[]}}async function h(_){const V=_.query.toLowerCase();try{const q=await m.crud.attributeTemplate.findMany.query({where:{OR:[{title:{contains:V,mode:"insensitive"}},{name:{contains:V,mode:"insensitive"}}]},include:{group:!0},take:10});C.value=q||[]}catch(q){console.error("Error filtering templates:",q),C.value=[]}}async function O(){if(D.value){R.value=!0;try{const _=D.value.id||D.value,V=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(V)for(const q of V){const we={templateId:q.id,value:Y(q.dataType),template:q};f("add-attribute",we)}D.value=null}catch(_){console.error("Error loading group templates:",_)}finally{R.value=!1}}}function P(){if(!B.value)return;const _=B.value,V={templateId:_.id,value:Y(_.dataType),template:_};f("add-attribute",V),B.value=null}async function he(_){R.value=!0;try{const V=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(V)for(const q of V){const we={templateId:q.id,value:Y(q.dataType),template:q};f("add-attribute",we)}A.value=!1}catch(V){console.error("Error loading templates by group:",V)}finally{R.value=!1}}function Y(_){switch(_){case"STRING":return"";case"NUMBER":return 0;case"BOOLEAN":return!1;case"DATE":return new Date;case"JSON":return"";default:return""}}function ye(_){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[_]||"pi pi-question"}qe(async()=>{try{const _=await m.crud.attributeGroup.findMany.query({include:{_count:{select:{templates:!0}}},orderBy:{name:"asc"}});L.value=_||[]}catch(_){console.error("Error loading template groups:",_)}});const se={props:e,emit:f,client:m,showAllAttributes:t,COMPACT_LIMIT:oe,viewMode:l,showAddDialog:o,showGroupDialog:A,selectedTemplateGroup:D,selectedTemplate:B,groupSuggestions:S,templateSuggestions:C,templateGroups:L,loadingTemplates:R,groupedAttributes:Q,hasAttributes:H,totalAttributesCount:b,shouldShowExpandButton:N,visibleAttributes:z,visibleGroupedAttributes:le,hiddenAttributesCount:X,filledAttributesCount:Z,handleAddAttribute:re,handleSaveAttribute:ee,handleCancelAddAttribute:J,handleEditAttribute:te,handleDeleteAttribute:ue,toggleShowAll:ne,toggleViewMode:ie,formatAttribute:ae,canManageAttributes:v,filterGroups:n,filterTemplates:h,loadSelectedGroupTemplates:O,addSingleTemplate:P,loadTemplatesByGroupId:he,getDefaultValueForType:Y,getDataTypeIcon:ye,get PlusIcon(){return Ye},get ChevronDownIcon(){return xt},get ChevronUpIcon(){return _t},get TableIcon(){return At},get GridIcon(){return kt},get TagsIcon(){return yt},Button:fe,Tag:Ae,Card:st,AutoComplete:ot,Dialog:Se,EquipmentAttributesList:Eu,AddEquipmentAttributeDialog:Xu,get groupAttributes(){return xe},get getDataTypeDisplayName(){return ke},Icon:Fe};return Object.defineProperty(se,"__isScriptSetup",{enumerable:!1,value:!0}),se}}),Yu={class:"equipment-attributes-section"},$u={class:"flex items-center justify-between mb-4"},ea={class:"flex items-center gap-3"},ta={class:"font-semibold flex items-center gap-2 text-surface-900 dark:text-surface-0"},ua={class:"flex items-center gap-2"},aa={class:"p-4"},la={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ra={class:"flex items-center gap-2"},na={class:"flex-1"},ia={class:"font-medium"},sa={class:"text-sm text-surface-600"},oa={class:"flex items-end gap-2"},da={key:1},ca={key:0},ma={key:0,class:"text-center pt-4"},pa={key:1},fa={class:"mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},va={class:"text-sm font-medium text-surface-700 dark:text-surface-300 flex items-center gap-2"},ga={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"},ba={class:"flex items-start justify-between"},ha={class:"flex-1 min-w-0"},ya={class:"flex items-center gap-2 mb-1"},xa={class:"text-sm font-medium text-surface-900 dark:text-surface-0 truncate"},_a={class:"text-sm text-surface-600 dark:text-surface-400 mb-2 break-words"},Ea=["title"],ka={key:0,class:"ml-2 flex flex-col gap-1 flex-shrink-0"},Aa={key:0,class:"text-center pt-2"},Ca={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"},Da={class:"space-y-4"},Ta={class:"flex items-center justify-between"},wa={class:"font-medium text-surface-900 dark:text-surface-0"},Ia={class:"text-sm text-surface-600 dark:text-surface-400"},Ba={class:"text-surface-500"};function Sa(p,u,c,e,f,m){return i(),s("div",Yu,[a("div",$u,[a("div",ea,[a("h5",ta,[r(e.Icon,{name:"pi pi-list",class:"text-green-600 w-4 h-4"}),u[5]||(u[5]=y(" Атрибуты модели "))]),e.totalAttributesCount>0?(i(),k(e.Tag,{key:0,value:`${e.filledAttributesCount}/${e.totalAttributesCount} заполнено`,severity:e.filledAttributesCount===e.totalAttributesCount?"success":"warn",size:"small"},null,8,["value","severity"])):x("",!0)]),a("div",ua,[e.hasAttributes?(i(),k(e.Button,{key:0,onClick:e.toggleViewMode,text:"",size:"small",title:e.viewMode==="grid"?"Переключить на табличный вид":"Переключить на сеточный вид"},{default:d(()=>[e.viewMode==="grid"?(i(),k(e.TableIcon,{key:0,class:"w-4 h-4"})):(i(),k(e.GridIcon,{key:1,class:"w-4 h-4"}))]),_:1},8,["title"])):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:1,onClick:u[0]||(u[0]=t=>e.showGroupDialog=!0),outlined:"",severity:"secondary",size:"small",class:"text-sm"},{default:d(()=>[r(e.TagsIcon,{class:"w-4 h-4 mr-1"}),u[6]||(u[6]=y(" Добавить группу "))]),_:1,__:[6]})):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:2,onClick:e.handleAddAttribute,size:"small",class:"text-sm"},{default:d(()=>[r(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[7]||(u[7]=y(" Добавить атрибут "))]),_:1,__:[7]})):x("",!0)])]),e.canManageAttributes?(i(),k(e.Card,{key:0,class:"mb-4"},{content:d(()=>[a("div",aa,[a("div",la,[a("div",null,[u[8]||(u[8]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа шаблонов ",-1)),r(e.AutoComplete,{modelValue:e.selectedTemplateGroup,"onUpdate:modelValue":u[1]||(u[1]=t=>e.selectedTemplateGroup=t),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name",placeholder:"Поиск группы...",class:"w-full",dropdown:""},null,8,["modelValue","suggestions"])]),a("div",null,[u[9]||(u[9]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Или отдельный шаблон ",-1)),r(e.AutoComplete,{modelValue:e.selectedTemplate,"onUpdate:modelValue":u[2]||(u[2]=t=>e.selectedTemplate=t),suggestions:e.templateSuggestions,onComplete:e.filterTemplates,"option-label":"title",placeholder:"Поиск шаблона...",class:"w-full",dropdown:""},{option:d(({option:t})=>[a("div",ra,[r(e.Icon,{name:e.getDataTypeIcon(t.dataType),class:"text-primary w-4 h-4"},null,8,["name"]),a("div",na,[a("div",ia,g(t.title),1),a("div",sa,g(t.group?.name)+" • "+g(e.getDataTypeDisplayName(t.dataType)),1)])])]),_:1},8,["modelValue","suggestions"])]),a("div",oa,[r(e.Button,{onClick:e.loadSelectedGroupTemplates,size:"small",outlined:"",disabled:!e.selectedTemplateGroup||e.loadingTemplates,loading:e.loadingTemplates,label:"Добавить группу",class:"flex-1"},null,8,["disabled","loading"]),r(e.Button,{onClick:e.addSingleTemplate,size:"small",outlined:"",disabled:!e.selectedTemplate,label:"Добавить",class:"flex-1"},null,8,["disabled"])])])])]),_:1})):x("",!0),e.hasAttributes?(i(),s("div",da,[e.viewMode==="table"?(i(),s("div",ca,[r(e.EquipmentAttributesList,{attributes:e.visibleAttributes,readonly:c.readonly,compact:!e.showAllAttributes&&e.shouldShowExpandButton,onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["attributes","readonly","compact"]),e.shouldShowExpandButton?(i(),s("div",ma,[r(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),s(F,{key:1},[r(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[10]||(u[10]=y(" Свернуть "))],64)):(i(),s(F,{key:0},[r(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),y(" Показать все (еще "+g(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)])):(i(),s("div",pa,[(i(!0),s(F,null,j(e.visibleGroupedAttributes,(t,l)=>(i(),s("div",{key:l,class:"attribute-group mb-4"},[a("div",fa,[a("h6",va,[r(e.Icon,{name:"pi pi-folder",class:"w-3 h-3"}),y(" "+g(l)+" ",1),r(e.Tag,{severity:"info",value:t.length.toString(),class:"text-xs"},null,8,["value"])])]),a("div",ga,[(i(!0),s(F,null,j(t,o=>(i(),s("div",{key:o.id,class:"attribute-card p-3 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",ba,[a("div",ha,[a("div",ya,[a("span",xa,g(o.template.title),1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[11]||(u[11]=[y(" Обязательный ")])),_:1,__:[11]})):x("",!0)]),a("div",_a,g(e.formatAttribute(o).displayValue),1),o.template.description?(i(),s("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:o.template.description},g(o.template.description),9,Ea)):x("",!0)]),e.canManageAttributes?(i(),s("div",ka,[r(e.Button,{onClick:A=>e.handleEditAttribute(o.id),text:"",size:"small",class:"p-1 text-xs",title:`Редактировать ${o.template.title}`},{default:d(()=>[r(e.Icon,{name:"pi pi-pencil",class:"w-3 h-3"})]),_:2},1032,["onClick","title"]),r(e.Button,{onClick:A=>e.handleDeleteAttribute(o.id),text:"",severity:"danger",size:"small",class:"p-1 text-xs",title:`Удалить ${o.template.title}`,disabled:o.template.isRequired},{default:d(()=>[r(e.Icon,{name:"pi pi-trash",class:"w-3 h-3"})]),_:2},1032,["onClick","title","disabled"])])):x("",!0)])]))),128))])]))),128)),e.shouldShowExpandButton?(i(),s("div",Aa,[r(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),s(F,{key:1},[r(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[12]||(u[12]=y(" Свернуть "))],64)):(i(),s(F,{key:0},[r(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),y(" Показать все (еще "+g(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)]))])):(i(),s("div",Ca,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[14]||(u[14]=a("p",{class:"text-sm mb-3"},"У данной модели техники нет атрибутов",-1)),e.canManageAttributes?(i(),k(e.Button,{key:0,onClick:e.handleAddAttribute,outlined:"",size:"small"},{default:d(()=>[r(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[13]||(u[13]=y(" Добавить первый атрибут "))]),_:1,__:[13]})):x("",!0)])),r(e.AddEquipmentAttributeDialog,{visible:e.showAddDialog,"onUpdate:visible":u[3]||(u[3]=t=>e.showAddDialog=t),"equipment-id":c.equipmentId,"existing-attributes":c.attributes||[],onSave:e.handleSaveAttribute,onCancel:e.handleCancelAddAttribute},null,8,["visible","equipment-id","existing-attributes"]),r(e.Dialog,{visible:e.showGroupDialog,"onUpdate:visible":u[4]||(u[4]=t=>e.showGroupDialog=t),modal:"",header:"Выбор группы шаблонов",style:{width:"40rem"}},{default:d(()=>[a("div",Da,[(i(!0),s(F,null,j(e.templateGroups,t=>(i(),s("div",{key:t.id,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4"},[a("div",Ta,[a("div",null,[a("h4",wa,g(t.name),1),a("p",Ia,g(t.description),1),a("small",Ba,g(t._count?.templates||0)+" шаблонов",1)]),r(e.Button,{onClick:l=>e.loadTemplatesByGroupId(t.id),size:"small",loading:e.loadingTemplates},{default:d(()=>u[15]||(u[15]=[y(" Добавить все ")])),_:2,__:[15]},1032,["onClick","loading"])])]))),128))])]),_:1},8,["visible"])])}const Va=ve(Zu,[["render",Sa],["__scopeId","data-v-2cbb67a9"]]),qa=ge({__name:"EquipmentList",props:{initialData:{}},setup(p,{expose:u}){u();const c=E(""),e=E(!1),f=E(null),m=E([]),t=E({}),l=E({}),o=p,A=E(o.initialData),D={id:"ID",name:"Наименование",brandId:"Бренд ID",createdAt:"Создано",updatedAt:"Обновлено"},B=["id","name","createdAt"];function S(){f.value={},e.value=!0}function C(n){f.value={...n},e.value=!0}async function L(n){if(n)try{if(n.id){const{id:h,...O}=n;await W.crud.equipmentModel.update.mutate({where:{id:h},data:O})}else if(n.name)await W.crud.equipmentModel.create.mutate({data:{name:n.name,brandId:n.brandId||null}});else{console.error("Name is required to create an equipment model.");return}Ie(window.location.href)}catch(h){console.error("Failed to save equipment model:",h)}finally{e.value=!1}}function R(){e.value=!1,f.value=null}async function Q(n){e.value=!1,await W.crud.equipmentModel.delete.mutate({where:{id:n.id}}),Ie(window.location.href)}ce(c,n=>{H(n)});async function H(n=""){console.log("value",n),A.value=await W.crud.equipmentModel.findMany.query({where:{OR:[{name:{contains:n}},{brand:{name:{contains:n}}}]},include:{brand:{select:{name:!0}},_count:{select:{partApplicabilities:!0,attributes:!0}}}})}function b(n){return new Date(n).toLocaleDateString("ru-RU",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}async function N(n){if(t.value[n])return t.value[n];try{const h=await W.crud.equipmentApplicability.findMany.query({where:{equipmentModelId:n},include:{part:{include:{partCategory:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},_count:{select:{attributes:!0,applicabilities:!0}}}}},orderBy:{part:{name:"asc"}}});return t.value[n]=h||[],h||[]}catch(h){return console.error("Failed to load equipment parts:",h),[]}}async function z(n){if(l.value[n])return l.value[n];try{const O=(await W.crud.equipmentModelAttribute.findMany.query({where:{equipmentModelId:n},include:{template:{include:{group:!0}}},orderBy:[{template:{group:{name:"asc"}}},{template:{title:"asc"}}]})).map(P=>({...P,template:{...P.template,group:P.template.group}}));return l.value[n]=O,O}catch(h){return console.error("Failed to load equipment attributes:",h),[]}}async function le(n){n.data._count.partApplicabilities>0&&await N(n.data.id),n.data._count.attributes>0&&await z(n.data.id)}function X(n){return{EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"}[n]||n}function Z(n){return{EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"}[n]||"secondary"}function re(n){console.log("Edit part:",n)}function ee(n){const h={};return n.forEach(O=>{const P=O.template.group?.name||"Общие";h[P]||(h[P]=[]),h[P].push(O)}),h}function J(n){const{value:h,template:O}=n,{dataType:P,unit:he}=O;switch(P){case"STRING":return h;case"NUMBER":const Y=parseFloat(h),ye=isNaN(Y)?h:Y.toLocaleString("ru-RU");return he?`${ye} ${te(he)}`:ye;case"BOOLEAN":return h.toLowerCase()==="true"?"Да":"Нет";case"DATE":const se=new Date(h);return isNaN(se.getTime())?h:se.toLocaleDateString("ru-RU");case"JSON":try{return JSON.stringify(JSON.parse(h),null,2)}catch{return h}default:return h}}function te(n){return n?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[n]||n:""}async function ue(n){m.value.some(O=>O.id===n.id)||(m.value.push(n),n._count.attributes>0&&!l.value[n.id]&&await z(n.id))}async function ne(n){try{await W.crud.equipmentModelAttribute.create.mutate({data:{equipmentModelId:n.equipmentId||n.equipmentModelId,templateId:n.templateId,value:String(n.value)}}),n.equipmentId&&(delete l.value[n.equipmentId],await z(n.equipmentId)),console.log("Attribute added successfully")}catch(h){console.error("Failed to add attribute:",h)}}function ie(n){console.log("Edit attribute:",n)}function ae(n){console.log("Delete attribute:",n)}const v={searchValue:c,dialogVisible:e,editingEquipment:f,expandedRows:m,equipmentPartsCache:t,equipmentAttributesCache:l,props:o,items:A,keyMapping:D,columnKeys:B,createEquipment:S,editEquipment:C,handleSave:L,handleCancel:R,deleteEquipment:Q,debouncedSearch:H,formatDate:b,loadEquipmentParts:N,loadEquipmentAttributes:z,onRowExpand:le,getAccuracyLabel:X,getAccuracySeverity:Z,editPart:re,groupAttributesByGroup:ee,formatAttributeValue:J,getUnitDisplayName:te,expandRowForAttributes:ue,handleAddAttribute:ne,handleEditAttribute:ie,handleDeleteAttribute:ae,Button:fe,DataTable:ze,get PencilIcon(){return Ze},get TrashIcon(){return Xe},get PlusIcon(){return Ye},get ExternalLinkIcon(){return Et},get Column(){return je},EditEquipmentDialog:St,EquipmentAttributesSection:Va,get navigate(){return Ie},InputText:Ve,Tag:Ae,Icon:Fe};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}}),Ma={class:"flex justify-between items-center mb-4"},Na={class:"flex justify-end"},Fa={class:"font-mono text-sm"},Ra={key:1,class:"text-surface-500"},Oa={key:1,class:"text-surface-500"},La={class:"flex gap-2"},Ga={class:"p-4 bg-surface-50 dark:bg-surface-800"},Pa={key:0,class:"mb-6"},Ua={key:1},za={class:"mb-3 font-semibold flex items-center gap-2"},ja={key:0},Ha={class:"grid gap-3"},Ja={class:"flex items-start justify-between"},Wa={class:"flex-1"},Ka={class:"flex items-center gap-2 mb-2"},Qa={class:"font-medium text-surface-900 dark:text-surface-0"},Xa={class:"flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2"},Za={class:"flex items-center gap-1"},Ya={class:"flex items-center gap-1"},$a={key:0,class:"mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400"},e0={key:1,class:"mt-3"},t0={class:"flex flex-wrap gap-2"},u0={class:"font-medium"},a0={class:"text-surface-500"},l0={key:0,class:"px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"},r0={class:"ml-4 flex flex-col gap-2"},n0={key:1,class:"text-center py-6 text-surface-500 dark:text-surface-400"},i0={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"};function s0(p,u,c,e,f,m){return i(),s("div",null,[a("div",Ma,[u[4]||(u[4]=a("h1",{class:"text-2xl font-bold"},"Модели техники",-1)),r(e.Button,{onClick:e.createEquipment},{default:d(()=>[r(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[3]||(u[3]=y(" Создать модель "))]),_:1,__:[3]})]),r(e.DataTable,{"show-headers":"",value:e.items,expandedRows:e.expandedRows,"onUpdate:expandedRows":u[1]||(u[1]=t=>e.expandedRows=t),onRowExpand:e.onRowExpand,rowHover:!0},{header:d(()=>[a("div",Na,[r(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=t=>e.searchValue=t),placeholder:"Поиск"},null,8,["modelValue"])])]),expansion:d(({data:t})=>[a("div",Ga,[(t._count.attributes>0,i(),s("div",Pa,[r(e.EquipmentAttributesSection,{"equipment-id":t.id,attributes:e.equipmentAttributesCache[t.id],onAddAttribute:l=>e.handleAddAttribute({...l,equipmentId:t.id}),onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["equipment-id","attributes","onAddAttribute"])])),t._count.partApplicabilities>0?(i(),s("div",Ua,[a("h5",za,[r(e.Icon,{name:"pi pi-wrench",class:"text-blue-600 w-4 h-4"}),y(" Запчасти для: "+g(t.name),1)]),e.equipmentPartsCache[t.id]&&e.equipmentPartsCache[t.id].length>0?(i(),s("div",ja,[a("div",Ha,[(i(!0),s(F,null,j(e.equipmentPartsCache[t.id],l=>(i(),s("div",{key:l.id,class:"p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",Ja,[a("div",Wa,[a("div",Ka,[a("h6",Qa,g(l.part?.name||"Без названия"),1),l.part?.partCategory?(i(),k(e.Tag,{key:0,severity:"info",class:"text-xs"},{default:d(()=>[y(g(l.part.partCategory.name),1)]),_:2},1024)):x("",!0)]),a("div",Xa,[a("span",Za,[r(e.Icon,{name:"pi pi-list",class:"w-3 h-3"}),y(" "+g(l.part?._count?.attributes||0)+" атрибутов ",1)]),a("span",Ya,[r(e.Icon,{name:"pi pi-box",class:"w-3 h-3"}),y(" "+g(l.part?._count?.applicabilities||0)+" каталожных позиций ",1)])]),l.notes?(i(),s("div",$a,[r(e.Icon,{name:"pi pi-info-circle",class:"mr-1 w-4 h-4 inline-block"}),y(" "+g(l.notes),1)])):x("",!0),l.part?.applicabilities?.length>0?(i(),s("div",e0,[u[5]||(u[5]=a("div",{class:"text-xs font-medium text-surface-700 dark:text-surface-300 mb-2"}," Каталожные позиции: ",-1)),a("div",t0,[(i(!0),s(F,null,j(l.part.applicabilities.slice(0,3),o=>(i(),s("div",{key:o.id,class:"flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"},[a("span",u0,g(o.catalogItem?.sku),1),a("span",a0,g(o.catalogItem?.brand?.name),1),r(e.Tag,{severity:e.getAccuracySeverity(o.accuracy),class:"text-xs"},{default:d(()=>[y(g(e.getAccuracyLabel(o.accuracy)),1)]),_:2},1032,["severity"])]))),128)),l.part.applicabilities.length>3?(i(),s("div",l0," +"+g(l.part.applicabilities.length-3)+" еще ",1)):x("",!0)])])):x("",!0)]),a("div",r0,[r(e.Button,{onClick:o=>e.editPart(l.part.id),outlined:"",size:"small",class:"text-xs"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3 mr-1"}),u[6]||(u[6]=y(" Редактировать "))]),_:2,__:[6]},1032,["onClick"]),r(e.Button,{onClick:()=>e.navigate(`/admin/parts/${l.part.id}`),outlined:"",severity:"secondary",size:"small",class:"text-xs"},{default:d(()=>[r(e.ExternalLinkIcon,{class:"w-3 h-3 mr-1"}),u[7]||(u[7]=y(" Подробнее "))]),_:2,__:[7]},1032,["onClick"])])])]))),128))])])):(i(),s("div",n0,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[8]||(u[8]=y(" Запчасти для данной модели техники не найдены "))]))])):x("",!0),t._count.attributes===0&&t._count.partApplicabilities===0?(i(),s("div",i0,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[9]||(u[9]=y(" Для данной модели техники нет дополнительной информации "))])):x("",!0)])]),default:d(()=>[r(e.Column,{expander:!0,headerStyle:"width: 3rem"}),(i(),s(F,null,j(e.columnKeys,t=>r(e.Column,{key:t,field:t,header:e.keyMapping[t]||t},it({_:2},[t==="createdAt"?{name:"body",fn:d(({data:l})=>[y(g(e.formatDate(l[t])),1)]),key:"0"}:t==="id"?{name:"body",fn:d(({data:l})=>[a("div",Fa,g(l[t].substring(0,8))+"... ",1)]),key:"1"}:void 0]),1032,["field","header"])),64)),r(e.Column,{field:"brand.name",header:"Бренд"},{body:d(({data:t})=>[y(g(t.brand?.name||"-"),1)]),_:1}),r(e.Column,{field:"_count.partApplicabilities",header:"Применимость деталей"},{body:d(({data:t})=>[t._count.partApplicabilities>0?(i(),k(e.Tag,{key:0,severity:"info",value:t._count.partApplicabilities.toString()},null,8,["value"])):(i(),s("span",Ra,"0"))]),_:1}),r(e.Column,{field:"_count.attributes",header:"Атрибуты"},{body:d(({data:t})=>[t._count.attributes>0?(i(),k(e.Tag,{key:0,severity:"secondary",value:t._count.attributes.toString(),class:"cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",onClick:l=>e.expandRowForAttributes(t)},null,8,["value","onClick"])):(i(),s("span",Oa,"0"))]),_:1}),r(e.Column,{header:"Действия"},{body:d(({data:t})=>[a("div",La,[r(e.Button,{onClick:l=>e.editEquipment(t),outlined:"",size:"small"},{default:d(()=>[r(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:l=>e.deleteEquipment(t),outlined:"",severity:"danger",size:"small"},{default:d(()=>[r(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","expandedRows"]),r(e.EditEquipmentDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[2]||(u[2]=t=>e.dialogVisible=t),equipment:e.editingEquipment,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","equipment"])])}const o4=ve(qa,[["render",s0]]);export{o4 as default};
