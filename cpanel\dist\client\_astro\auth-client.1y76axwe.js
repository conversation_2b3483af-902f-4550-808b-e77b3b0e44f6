import{a5 as Re,a6 as be,a7 as Se}from"./reactivity.esm-bundler.Bx7uHohy.js";import{a as Pe,o as G,s as P,d as V,b as X,u as Y}from"./schemas.BR5-L2eu.js";import{d as N}from"./coerce.CW6lkyCY.js";var Oe=Object.defineProperty,Te=Object.defineProperties,Ae=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,Ee=Object.prototype.hasOwnProperty,Ue=Object.prototype.propertyIsEnumerable,K=(e,t,r)=>t in e?Oe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,b=(e,t)=>{for(var r in t||(t={}))Ee.call(t,r)&&K(e,r,t[r]);if(Z)for(var r of Z(t))Ue.call(t,r)&&K(e,r,t[r]);return e},S=(e,t)=>Te(e,Ae(t)),Le=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},Ie=async(e,t)=>{var r,n,s,a,o,l;let c=t||{};const i={onRequest:[t?.onRequest],onResponse:[t?.onResponse],onSuccess:[t?.onSuccess],onError:[t?.onError],onRetry:[t?.onRetry]};if(!t||!t?.plugins)return{url:e,options:c,hooks:i};for(const u of t?.plugins||[]){if(u.init){const f=await((r=u.init)==null?void 0:r.call(u,e.toString(),t));c=f.options||c,e=f.url}i.onRequest.push((n=u.hooks)==null?void 0:n.onRequest),i.onResponse.push((s=u.hooks)==null?void 0:s.onResponse),i.onSuccess.push((a=u.hooks)==null?void 0:a.onSuccess),i.onError.push((o=u.hooks)==null?void 0:o.onError),i.onRetry.push((l=u.hooks)==null?void 0:l.onRetry)}return{url:e,options:c,hooks:i}},ee=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},Ne=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){return Math.min(this.options.maxDelay,this.options.baseDelay*2**e)}};function xe(e){if(typeof e=="number")return new ee({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new ee(e);case"exponential":return new Ne(e);default:throw new Error("Invalid retry strategy")}}var Ce=async e=>{const t={},r=async n=>typeof n=="function"?await n():n;if(e?.auth){if(e.auth.type==="Bearer"){const n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if(e.auth.type==="Basic"){const n=r(e.auth.username),s=r(e.auth.password);if(!n||!s)return t;t.authorization=`Basic ${btoa(`${n}:${s}`)}`}else if(e.auth.type==="Custom"){const n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},qe=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function $e(e){const t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";const n=t.split(";").shift()||"";return qe.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}function je(e){try{return JSON.parse(e),!0}catch{return!1}}function ae(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}function te(e){try{return JSON.parse(e)}catch{return e}}function re(e){return typeof e=="function"}function ke(e){if(e?.customFetchImpl)return e.customFetchImpl;if(typeof globalThis<"u"&&re(globalThis.fetch))return globalThis.fetch;if(typeof window<"u"&&re(window.fetch))return window.fetch;throw new Error("No fetch implementation found")}async function De(e){const t=new Headers(e?.headers),r=await Ce(e);for(const[n,s]of Object.entries(r||{}))t.set(n,s);if(!t.has("content-type")){const n=Be(e?.body);n&&t.set("content-type",n)}return t}function Be(e){return ae(e)?"application/json":null}function Me(e){if(!e?.body)return null;const t=new Headers(e?.headers);if(ae(e.body)&&!t.has("content-type")){for(const[r,n]of Object.entries(e?.body))n instanceof Date&&(e.body[r]=n.toISOString());return JSON.stringify(e.body)}return e.body}function Fe(e,t){var r;if(t?.method)return t.method.toUpperCase();if(e.startsWith("@")){const n=(r=e.split("@")[1])==null?void 0:r.split("/")[0];return le.includes(n)?n.toUpperCase():t?.body?"POST":"GET"}return t?.body?"POST":"GET"}function Ve(e,t){let r;return!e?.signal&&e?.timeout&&(r=setTimeout(()=>t?.abort(),e?.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}var We=class ue extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,ue.prototype)}};async function M(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new We(r.issues);return r.value}var le=["get","post","put","patch","delete"],He=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,s,a,o;const l=((s=(n=e.plugins)==null?void 0:n.find(c=>{var i;return(i=c.schema)!=null&&i.config?t.startsWith(c.schema.config.baseURL||"")||t.startsWith(c.schema.config.prefix||""):!1}))==null?void 0:s.schema)||e.schema;if(l){let c=t;(a=l.config)!=null&&a.prefix&&c.startsWith(l.config.prefix)&&(c=c.replace(l.config.prefix,""),l.config.baseURL&&(t=t.replace(l.config.prefix,l.config.baseURL))),(o=l.config)!=null&&o.baseURL&&c.startsWith(l.config.baseURL)&&(c=c.replace(l.config.baseURL,""));const i=l.schema[c];if(i){let u=S(b({},r),{method:i.method,output:i.output});return r?.disableValidation||(u=S(b({},u),{body:i.input?await M(i.input,r?.body):r?.body,params:i.params?await M(i.params,r?.params):r?.params,query:i.query?await M(i.query,r?.query):r?.query})),{url:t,options:u}}}return{url:t,options:r}}}),Je=e=>{async function t(r,n){const s=S(b(b({},e),n),{plugins:[...e?.plugins||[],He(e||{})]});if(e?.catchAllError)try{return await J(r,s)}catch(a){return{data:null,error:{status:500,statusText:"Fetch Error",message:"Fetch related error. Captured by catchAllError option. See error property for more details.",error:a}}}return await J(r,s)}return t};function Ge(e,t){let{baseURL:r,params:n,query:s}=t||{query:{},params:{},baseURL:""},a=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){const f=e.toString().split("@")[1].split("/")[0];le.includes(f)&&(e=e.replace(`@${f}/`,"/"))}a.endsWith("/")||(a+="/");let[o,l]=e.replace(a,"").split("?");const c=new URLSearchParams(l);for(const[f,h]of Object.entries(s||{}))h!=null&&c.set(f,String(h));if(n)if(Array.isArray(n)){const f=o.split("/").filter(h=>h.startsWith(":"));for(const[h,v]of f.entries()){const y=n[h];o=o.replace(v,y)}}else for(const[f,h]of Object.entries(n))o=o.replace(`:${f}`,String(h));o=o.split("/").map(encodeURIComponent).join("/"),o.startsWith("/")&&(o=o.slice(1));let i=c.toString();return i=i.length>0?`?${i}`.replace(/\+/g,"%20"):"",a.startsWith("http")?new URL(`${o}${i}`,a):`${a}${o}${i}`}var J=async(e,t)=>{var r,n,s,a,o,l,c,i;const{hooks:u,url:f,options:h}=await Ie(e,t),v=ke(h),y=new AbortController,_=(r=h.signal)!=null?r:y.signal,O=Ge(f,h),I=Me(h),E=await De(h),d=Fe(f,h);let p=S(b({},h),{url:O,headers:E,body:I,method:d,signal:_});for(const w of u.onRequest)if(w){const g=await w(p);g instanceof Object&&(p=g)}("pipeTo"in p&&typeof p.pipeTo=="function"||typeof((n=t?.body)==null?void 0:n.pipe)=="function")&&("duplex"in p||(p.duplex="half"));const{clearTimeout:q}=Ve(h,y);let m=await v(p.url,p);q();const z={response:m,request:p};for(const w of u.onResponse)if(w){const g=await w(S(b({},z),{response:(s=t?.hookOptions)!=null&&s.cloneResponse?m.clone():m}));g instanceof Response?m=g:g instanceof Object&&(m=g.response)}if(m.ok){if(!(p.method!=="HEAD"))return{data:"",error:null};const g=$e(m),T={data:"",response:m,request:p};if(g==="json"||g==="text"){const A=await m.text(),we=await((a=p.jsonParser)!=null?a:te)(A);T.data=we}else T.data=await m[g]();p?.output&&p.output&&!p.disableValidation&&(T.data=await M(p.output,T.data));for(const A of u.onSuccess)A&&await A(S(b({},T),{response:(o=t?.hookOptions)!=null&&o.cloneResponse?m.clone():m}));return t?.throw?T.data:{data:T.data,error:null}}const ve=(l=t?.jsonParser)!=null?l:te,$=await m.text(),Q=je($),W=Q?await ve($):null,_e={response:m,responseText:$,request:p,error:S(b({},W),{status:m.status,statusText:m.statusText})};for(const w of u.onError)w&&await w(S(b({},_e),{response:(c=t?.hookOptions)!=null&&c.cloneResponse?m.clone():m}));if(t?.retry){const w=xe(t.retry),g=(i=t.retryAttempt)!=null?i:0;if(await w.shouldAttemptRetry(g,m)){for(const A of u.onRetry)A&&await A(z);const T=w.getDelay(g);return await new Promise(A=>setTimeout(A,T)),await J(e,S(b({},t),{retryAttempt:g+1}))}}if(t?.throw)throw new Le(m.status,m.statusText,Q?W:$);return{data:null,error:S(b({},W),{status:m.status,statusText:m.statusText})}},ze={},Qe={};const F=Object.create(null),x=e=>ze||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?F:globalThis),L=new Proxy(F,{get(e,t){return x()[t]??F[t]},has(e,t){const r=x();return t in r||t in F},set(e,t,r){const n=x(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;const r=x(!0);return delete r[t],!0},ownKeys(){const e=x(!0);return Object.keys(e)}});function Xe(e){return e?e!=="false":!1}const Ye=typeof process<"u"&&Qe&&"production"||"";Ye==="test"||Xe(L.TEST);class ce extends Error{constructor(t,r){super(t),this.name="BetterAuthError",this.message=t,this.cause=r,this.stack=""}}function Ze(e){try{return new URL(e).pathname!=="/"}catch{throw new ce(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}function H(e,t="/api/auth"){return Ze(e)?e:(t=t.startsWith("/")?t:`/${t}`,`${e.replace(/\/+$/,"")}${t}`)}function Ke(e,t,r){if(e)return H(e,t);const n=L.BETTER_AUTH_URL||L.NEXT_PUBLIC_BETTER_AUTH_URL||L.PUBLIC_BETTER_AUTH_URL||L.NUXT_PUBLIC_BETTER_AUTH_URL||L.NUXT_PUBLIC_AUTH_URL||(L.BASE_URL!=="/"?L.BASE_URL:void 0);if(n)return H(n,t);if(typeof window<"u"&&window.location)return H(window.location.origin,t)}let R=[],U=0;const j=4;let fe=e=>{let t=[],r={get(){return r.lc||r.listen(()=>{})(),r.value},lc:0,listen(n){return r.lc=t.push(n),()=>{for(let a=U+j;a<R.length;)R[a]===n?R.splice(a,j):a+=j;let s=t.indexOf(n);~s&&(t.splice(s,1),--r.lc||r.off())}},notify(n,s){let a=!R.length;for(let o of t)R.push(o,r.value,n,s);if(a){for(U=0;U<R.length;U+=j)R[U](R[U+1],R[U+2],R[U+3]);R.length=0}},off(){},set(n){let s=r.value;s!==n&&(r.value=n,r.notify(s))},subscribe(n){let s=r.listen(n);return n(r.value),s},value:e};return r};const et=5,k=6,D=10;let tt=(e,t,r,n)=>(e.events=e.events||{},e.events[r+D]||(e.events[r+D]=n(s=>{e.events[r].reduceRight((a,o)=>(o(a),a),{shared:{},...s})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let s=e.events[r],a=s.indexOf(t);s.splice(a,1),s.length||(delete e.events[r],e.events[r+D](),delete e.events[r+D])}),rt=1e3,nt=(e,t)=>tt(e,n=>{let s=t(n);s&&e.events[k].push(s)},et,n=>{let s=e.listen;e.listen=(...o)=>(!e.lc&&!e.active&&(e.active=!0,n()),s(...o));let a=e.off;return e.events[k]=[],e.off=()=>{a(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let o of e.events[k])o();e.events[k]=[]}},rt)},()=>{e.listen=s,e.off=a}});const st=typeof window>"u",ot=(e,t,r,n)=>{const s=fe({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>a()}),a=()=>{const l=typeof n=="function"?n({data:s.get().data,error:s.get().error,isPending:s.get().isPending}):n;return r(t,{...l,async onSuccess(c){s.set({data:c.data,error:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onSuccess?.(c)},async onError(c){const{request:i}=c,u=typeof i.retry=="number"?i.retry:i.retry?.attempts,f=i.retryAttempt||0;u&&f<u||(s.set({error:c.error,data:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onError?.(c))},async onRequest(c){const i=s.get();s.set({isPending:i.data===null,data:i.data,error:null,isRefetching:!0,refetch:s.value.refetch}),await l?.onRequest?.(c)}})};e=Array.isArray(e)?e:[e];let o=!1;for(const l of e)l.subscribe(()=>{st||(o?a():nt(s,()=>(setTimeout(()=>{a()},0),o=!0,()=>{s.off(),l.off()})))});return s},it={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},at=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,ne={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},ut=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;function lt(e){return e instanceof Date&&!isNaN(e.getTime())}function ct(e){const t=ut.exec(e);if(!t)return null;const[,r,n,s,a,o,l,c,i,u,f]=t;let h=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(s,10),parseInt(a,10),parseInt(o,10),parseInt(l,10),c?parseInt(c.padEnd(3,"0"),10):0));if(i){const v=(parseInt(u,10)*60+parseInt(f,10))*(i==="+"?-1:1);h.setUTCMinutes(h.getUTCMinutes()+v)}return lt(h)?h:null}function ft(e,t={}){const{strict:r=!1,warnings:n=!1,reviver:s,parseDates:a=!0}=t;if(typeof e!="string")return e;const o=e.trim();if(o[0]==='"'&&o.endsWith('"')&&!o.slice(1,-1).includes('"'))return o.slice(1,-1);const l=o.toLowerCase();if(l.length<=9&&l in ne)return ne[l];if(!at.test(o)){if(r)throw new SyntaxError("[better-json] Invalid JSON");return e}if(Object.entries(it).some(([i,u])=>{const f=u.test(o);return f&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${i} pattern`),f})&&r)throw new Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(o,(u,f)=>{if(u==="__proto__"||u==="constructor"&&f&&typeof f=="object"&&"prototype"in f){n&&console.warn(`[better-json] Dropping "${u}" key to prevent prototype pollution`);return}if(a&&typeof f=="string"){const h=ct(f);if(h)return h}return s?s(u,f):f})}catch(i){if(r)throw i;return e}}function dt(e,t={strict:!0}){return ft(e,t)}const ht={id:"redirect",name:"Redirect",hooks:{onSuccess(e){if(e.data?.url&&e.data?.redirect&&typeof window<"u"&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}};function pt(e){const t=fe(!1);return{session:ot(t,"/get-session",e,{method:"GET"}),$sessionSignal:t}}const mt=e=>{const t="credentials"in Request.prototype,r=Ke(e?.baseURL,e?.basePath),n=e?.plugins?.flatMap(d=>d.fetchPlugins).filter(d=>d!==void 0)||[],s={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:e?.fetchOptions?.onSuccess,onError:e?.fetchOptions?.onError,onRequest:e?.fetchOptions?.onRequest,onResponse:e?.fetchOptions?.onResponse}},{onSuccess:a,onError:o,onRequest:l,onResponse:c,...i}=e?.fetchOptions||{},u=Je({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser(d){return d?dt(d,{strict:!1}):null},customFetchImpl:async(d,p)=>{try{return await fetch(d,p)}catch{return Response.error()}},...i,plugins:[s,...i.plugins||[],...e?.disableDefaultFetchPlugins?[]:[ht],...n]}),{$sessionSignal:f,session:h}=pt(u),v=e?.plugins||[];let y={},_={$sessionSignal:f,session:h},O={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"};const I=[{signal:"$sessionSignal",matcher(d){return d==="/sign-out"||d==="/update-user"||d.startsWith("/sign-in")||d.startsWith("/sign-up")||d==="/delete-user"||d==="/verify-email"}}];for(const d of v)d.getAtoms&&Object.assign(_,d.getAtoms?.(u)),d.pathMethods&&Object.assign(O,d.pathMethods),d.atomListeners&&I.push(...d.atomListeners);const E={notify:d=>{_[d].set(!_[d].get())},listen:(d,p)=>{_[d].subscribe(p)},atoms:_};for(const d of v)d.getActions&&Object.assign(y,d.getActions?.(u,E,e));return{pluginsActions:y,pluginsAtoms:_,pluginPathMethods:O,atomListeners:I,$fetch:u,$store:E}};function yt(e,t,r){const n=t[e],{fetchOptions:s,query:a,...o}=r||{};return n||(s?.method?s.method:o&&Object.keys(o).length>0?"POST":"GET")}function gt(e,t,r,n,s){function a(o=[]){return new Proxy(function(){},{get(l,c){const i=[...o,c];let u=e;for(const f of i)if(u&&typeof u=="object"&&f in u)u=u[f];else{u=void 0;break}return typeof u=="function"?u:a(i)},apply:async(l,c,i)=>{const u="/"+o.map(E=>E.replace(/[A-Z]/g,d=>`-${d.toLowerCase()}`)).join("/"),f=i[0]||{},h=i[1]||{},{query:v,fetchOptions:y,..._}=f,O={...h,...y},I=yt(u,r,f);return await t(u,{...O,body:I==="GET"?void 0:{..._,...O?.body||{}},query:v||O?.query,method:I,async onSuccess(E){await O?.onSuccess?.(E);const d=s?.find(m=>m.matcher(u));if(!d)return;const p=n[d.signal];if(!p)return;const q=p.get();setTimeout(()=>{p.set(!q)},10)}})}})}return a()}function vt(e){return e.charAt(0).toUpperCase()+e.slice(1)}function se(e){let t=Re(),r=e.subscribe(n=>{t.value=n});return be()&&Se(r),t}function _t(e){return`use${vt(e)}`}function wt(e){const{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:s,$store:a,atomListeners:o}=mt(e);let l={};for(const[f,h]of Object.entries(n))l[_t(f)]=()=>se(h);function c(f){if(f){const h=se(n.$sessionSignal),v=e?.fetchOptions?.baseURL||e?.baseURL;let y=v?new URL(v).pathname:"/api/auth";return y=y==="/"?"/api/auth":y,y=y.endsWith("/")?y.slice(0,-1):y,f(`${y}/get-session`,{ref:h}).then(_=>({data:_.data,isPending:!1,error:_.error}))}return l.useSession()}const i={...r,...l,useSession:c,$fetch:s,$store:a};return gt(i,s,t,n,o)}function Rt(e){return{authorize(t,r="AND"){let n=!1;for(const[s,a]of Object.entries(t)){const o=e[s];if(!o)return{success:!1,error:`You are not allowed to access resource: ${s}`};if(Array.isArray(a))n=a.every(l=>o.includes(l));else if(typeof a=="object"){const l=a;l.connector==="OR"?n=l.actions.some(c=>o.includes(c)):n=l.actions.every(c=>o.includes(c))}else throw new ce("Invalid access control request");if(n&&r==="OR")return{success:n};if(!n&&r==="AND")return{success:!1,error:`unauthorized to access resource "${s}"`}}return n?{success:n}:{success:!1,error:"Not authorized"}},statements:e}}function bt(e){return{newRole(t){return Rt(t)},statements:e}}const St={user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]},de=bt(St),he=de.newRole({user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]}),pe=de.newRole({user:[],session:[]}),Pt={admin:he,user:pe},Ot=e=>{if(e.userId&&e.options?.adminUserIds?.includes(e.userId))return!0;if(!e.permissions&&!e.permission)return!1;const t=(e.role||e.options?.defaultRole||"user").split(","),r=e.options?.roles||Pt;for(const n of t)if(r[n]?.authorize(e.permission??e.permissions)?.success)return!0;return!1},Tt=()=>({id:"username",$InferServerPlugin:{}}),At=()=>({id:"phoneNumber",$InferServerPlugin:{},atomListeners:[{matcher(e){return e==="/phone-number/update"||e==="/phone-number/verify"},signal:"$sessionSignal"}]}),Et=()=>({id:"anonymous",$InferServerPlugin:{},pathMethods:{"/sign-in/anonymous":"POST"}}),Ut=e=>{const t={admin:he,user:pe,...e?.roles};return{id:"admin-client",$InferServerPlugin:{},getActions:()=>({admin:{checkRolePermission:r=>Ot({role:r.role,options:{ac:e?.ac,roles:t},permissions:r.permissions??r.permission})}}),pathMethods:{"/admin/list-users":"GET","/admin/stop-impersonating":"POST"}}},me=Pe(["GUEST","USER","SHOP","ADMIN"]),C=G({id:P(),name:P().nullish(),email:P(),emailVerified:V().default(!1),image:P().nullish(),role:me,banned:V().default(!1),banReason:P().nullish(),banExpires:N().nullish(),createdAt:N().default(()=>new Date),updatedAt:N()}).strict(),Lt=G({accounts:X(Y()).optional(),sessions:X(Y()).optional()}),It=C;It.merge(Lt.partial());C.partial().passthrough();G({id:P(),name:P().nullish(),email:P(),emailVerified:V().default(!1),image:P().nullish(),role:me,banned:V().default(!1),banReason:P().nullish(),banExpires:N().nullish(),createdAt:N().default(()=>new Date),updatedAt:N()}).partial().passthrough();C.partial({id:!0,emailVerified:!0,role:!0,banned:!0,createdAt:!0,updatedAt:!0});const Dt=C.partial({id:!0,emailVerified:!0,role:!0,banned:!0,createdAt:!0,updatedAt:!0});C.partial();const oe={};var ie={};const Nt=()=>{if(typeof window>"u"){const e=ie.INTERNAL_API_URL||"http://api:3000",t=oe?.PUBLIC_API_URL||ie.PUBLIC_API_URL||"http://localhost:3000";return e.includes("api:")?e:t}else return oe?.PUBLIC_API_URL||window.__PUBLIC_API_URL__||"http://localhost:3000"},B={baseURL:Nt(),fetchOptions:{timeout:1e4,retries:3}},ye=wt({baseURL:B.baseURL,plugins:[Ut(),At(),Tt(),Et()],fetchOptions:{timeout:B.fetchOptions?.timeout,retry:B.fetchOptions?.retries,onRequest:e=>{typeof window>"u"&&console.log("🌐 Server auth request to:",e.url,"baseURL:",B.baseURL)},onResponse:e=>{},onError:e=>{console.error("❌ Auth error:",e.error),e.response?.status===401?(console.warn("🔒 Unauthorized - redirecting to login"),typeof window<"u"&&window.location.pathname.includes("/login")):e.response?.status===403?console.warn("🚫 Forbidden - insufficient permissions"):e.response?.status>=500&&console.error("🔥 Server error - check API status")}}}),{signIn:Bt,signUp:Mt,signOut:Ft,useSession:Vt,getSession:Wt,$ERROR_CODES:Ht}=ye;ye.admin;const ge=(e,t)=>e?.role===t,xt=e=>ge(e,"ADMIN"),Ct=e=>ge(e,"SHOP"),Jt=e=>xt(e)||Ct(e),Gt=(e,t="Произошла ошибка")=>typeof e=="string"?e:e?.message?e.message:e?.error?.message?e.error.message:t;export{Dt as U,Ct as a,ye as b,Jt as c,Gt as g,ge as h,xt as i,Vt as u};
