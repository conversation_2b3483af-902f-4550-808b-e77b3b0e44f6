import{n as r}from"./router.DKcY2uv6.js";import{b as e}from"./auth-client.1y76axwe.js";import"./reactivity.esm-bundler.Bx7uHohy.js";import"./schemas.BR5-L2eu.js";import"./coerce.CW6lkyCY.js";async function t(){try{console.log("🔄 Выполняется выход из системы...");const o=await e.signOut();o.error?console.error("❌ Ошибка при выходе:",o.error):console.log("✅ Успешный выход из системы")}catch(o){console.error("❌ Ошибка при выходе:",o)}finally{setTimeout(()=>{r("/admin/login")},1e3)}}typeof window<"u"&&t();
