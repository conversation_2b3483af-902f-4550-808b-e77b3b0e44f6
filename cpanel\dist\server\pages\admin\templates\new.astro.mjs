import { e as createComponent, r as renderTemplate } from '../../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import 'clsx';
export { r as renderers } from '../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$New = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate``;
}, "D:/Dev/parttec/cpanel/src/pages/admin/templates/new.astro", void 0);

const $$file = "D:/Dev/parttec/cpanel/src/pages/admin/templates/new.astro";
const $$url = "/admin/templates/new";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
	__proto__: null,
	default: $$New,
	file: $$file,
	url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
