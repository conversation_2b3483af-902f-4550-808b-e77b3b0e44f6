function c(e,n,t){function r(s,a){var l;Object.defineProperty(s,"_zod",{value:s._zod??{},enumerable:!1}),(l=s._zod).traits??(l.traits=new Set),s._zod.traits.add(e),n(s,a);for(const h in i.prototype)h in s||Object.defineProperty(s,h,{value:i.prototype[h].bind(s)});s._zod.constr=i,s._zod.def=a}const o=t?.Parent??Object;class u extends o{}Object.defineProperty(u,"name",{value:e});function i(s){var a;const l=t?.Parent?new u:this;r(l,s),(a=l._zod).deferred??(a.deferred=[]);for(const h of l._zod.deferred)h();return l}return Object.defineProperty(i,"init",{value:r}),Object.defineProperty(i,Symbol.hasInstance,{value:s=>t?.Parent&&s instanceof t.Parent?!0:s?._zod?.traits?.has(e)}),Object.defineProperty(i,"name",{value:e}),i}class N extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class Pe extends Error{constructor(n){super(`Encountered unidirectional transform during encode: ${n}`),this.name="ZodEncodeError"}}const Oe={};function I(e){return Oe}function nn(e){const n=Object.values(e).filter(r=>typeof r=="number");return Object.entries(e).filter(([r,o])=>n.indexOf(+r)===-1).map(([r,o])=>o)}function q(e,n){return typeof n=="bigint"?n.toString():n}function ee(e){return{get value(){{const n=e();return Object.defineProperty(this,"value",{value:n}),n}}}}function ne(e){return e==null}function te(e){const n=e.startsWith("^")?1:0,t=e.endsWith("$")?e.length-1:e.length;return e.slice(n,t)}function tn(e,n){const t=(e.toString().split(".")[1]||"").length,r=n.toString();let o=(r.split(".")[1]||"").length;if(o===0&&/\d?e-\d?/.test(r)){const a=r.match(/\d?e-(\d?)/);a?.[1]&&(o=Number.parseInt(a[1]))}const u=t>o?t:o,i=Number.parseInt(e.toFixed(u).replace(".","")),s=Number.parseInt(n.toFixed(u).replace(".",""));return i%s/10**u}const se=Symbol("evaluating");function d(e,n,t){let r;Object.defineProperty(e,n,{get(){if(r!==se)return r===void 0&&(r=se,r=t()),r},set(o){Object.defineProperty(e,n,{value:o})},configurable:!0})}function rn(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function T(e,n,t){Object.defineProperty(e,n,{value:t,writable:!0,enumerable:!0,configurable:!0})}function S(...e){const n={};for(const t of e){const r=Object.getOwnPropertyDescriptors(t);Object.assign(n,r)}return Object.defineProperties({},n)}function ae(e){return JSON.stringify(e)}const Ae="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function B(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const on=ee(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const e=Function;return new e(""),!0}catch{return!1}});function D(e){if(B(e)===!1)return!1;const n=e.constructor;if(n===void 0)return!0;const t=n.prototype;return!(B(t)===!1||Object.prototype.hasOwnProperty.call(t,"isPrototypeOf")===!1)}function Ne(e){return D(e)?{...e}:e}const un=new Set(["string","number","symbol"]);function J(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function P(e,n,t){const r=new e._zod.constr(n??e._zod.def);return(!n||t?.parent)&&(r._zod.parent=e),r}function f(e){const n=e;if(!n)return{};if(typeof n=="string")return{error:()=>n};if(n?.message!==void 0){if(n?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");n.error=n.message}return delete n.message,typeof n.error=="string"?{...n,error:()=>n.error}:n}function cn(e){return Object.keys(e).filter(n=>e[n]._zod.optin==="optional"&&e[n]._zod.optout==="optional")}const sn={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function an(e,n){const t=e._zod.def,r=S(e._zod.def,{get shape(){const o={};for(const u in n){if(!(u in t.shape))throw new Error(`Unrecognized key: "${u}"`);n[u]&&(o[u]=t.shape[u])}return T(this,"shape",o),o},checks:[]});return P(e,r)}function ln(e,n){const t=e._zod.def,r=S(e._zod.def,{get shape(){const o={...e._zod.def.shape};for(const u in n){if(!(u in t.shape))throw new Error(`Unrecognized key: "${u}"`);n[u]&&delete o[u]}return T(this,"shape",o),o},checks:[]});return P(e,r)}function fn(e,n){if(!D(n))throw new Error("Invalid input to extend: expected a plain object");const t=e._zod.def.checks;if(t&&t.length>0)throw new Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");const o=S(e._zod.def,{get shape(){const u={...e._zod.def.shape,...n};return T(this,"shape",u),u},checks:[]});return P(e,o)}function hn(e,n){if(!D(n))throw new Error("Invalid input to safeExtend: expected a plain object");const t={...e._zod.def,get shape(){const r={...e._zod.def.shape,...n};return T(this,"shape",r),r},checks:e._zod.def.checks};return P(e,t)}function dn(e,n){const t=S(e._zod.def,{get shape(){const r={...e._zod.def.shape,...n._zod.def.shape};return T(this,"shape",r),r},get catchall(){return n._zod.def.catchall},checks:[]});return P(e,t)}function pn(e,n,t){const r=S(n._zod.def,{get shape(){const o=n._zod.def.shape,u={...o};if(t)for(const i in t){if(!(i in o))throw new Error(`Unrecognized key: "${i}"`);t[i]&&(u[i]=e?new e({type:"optional",innerType:o[i]}):o[i])}else for(const i in o)u[i]=e?new e({type:"optional",innerType:o[i]}):o[i];return T(this,"shape",u),u},checks:[]});return P(n,r)}function mn(e,n,t){const r=S(n._zod.def,{get shape(){const o=n._zod.def.shape,u={...o};if(t)for(const i in t){if(!(i in u))throw new Error(`Unrecognized key: "${i}"`);t[i]&&(u[i]=new e({type:"nonoptional",innerType:o[i]}))}else for(const i in o)u[i]=new e({type:"nonoptional",innerType:o[i]});return T(this,"shape",u),u},checks:[]});return P(n,r)}function O(e,n=0){if(e.aborted===!0)return!0;for(let t=n;t<e.issues.length;t++)if(e.issues[t]?.continue!==!0)return!0;return!1}function A(e,n){return n.map(t=>{var r;return(r=t).path??(r.path=[]),t.path.unshift(e),t})}function C(e){return typeof e=="string"?e:e?.message}function E(e,n,t){const r={...e,path:e.path??[]};if(!e.message){const o=C(e.inst?._zod.def?.error?.(e))??C(n?.error?.(e))??C(t.customError?.(e))??C(t.localeError?.(e))??"Invalid input";r.message=o}return delete r.inst,delete r.continue,n?.reportInput||delete r.input,r}function re(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function x(...e){const[n,t,r]=e;return typeof n=="string"?{message:n,code:"custom",input:t,inst:r}:{...n}}const De=(e,n)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:n,enumerable:!1}),e.message=JSON.stringify(n,q,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},Se=c("$ZodError",De),je=c("$ZodError",De,{Parent:Error});function _n(e,n=t=>t.message){const t={},r=[];for(const o of e.issues)o.path.length>0?(t[o.path[0]]=t[o.path[0]]||[],t[o.path[0]].push(n(o))):r.push(n(o));return{formErrors:r,fieldErrors:t}}function vn(e,n){const t=n||function(u){return u.message},r={_errors:[]},o=u=>{for(const i of u.issues)if(i.code==="invalid_union"&&i.errors.length)i.errors.map(s=>o({issues:s}));else if(i.code==="invalid_key")o({issues:i.issues});else if(i.code==="invalid_element")o({issues:i.issues});else if(i.path.length===0)r._errors.push(t(i));else{let s=r,a=0;for(;a<i.path.length;){const l=i.path[a];a===i.path.length-1?(s[l]=s[l]||{_errors:[]},s[l]._errors.push(t(i))):s[l]=s[l]||{_errors:[]},s=s[l],a++}}};return o(e),r}const oe=e=>(n,t,r,o)=>{const u=r?Object.assign(r,{async:!1}):{async:!1},i=n._zod.run({value:t,issues:[]},u);if(i instanceof Promise)throw new N;if(i.issues.length){const s=new(o?.Err??e)(i.issues.map(a=>E(a,u,I())));throw Ae(s,o?.callee),s}return i.value},ie=e=>async(n,t,r,o)=>{const u=r?Object.assign(r,{async:!0}):{async:!0};let i=n._zod.run({value:t,issues:[]},u);if(i instanceof Promise&&(i=await i),i.issues.length){const s=new(o?.Err??e)(i.issues.map(a=>E(a,u,I())));throw Ae(s,o?.callee),s}return i.value},K=e=>(n,t,r)=>{const o=r?{...r,async:!1}:{async:!1},u=n._zod.run({value:t,issues:[]},o);if(u instanceof Promise)throw new N;return u.issues.length?{success:!1,error:new(e??Se)(u.issues.map(i=>E(i,o,I())))}:{success:!0,data:u.value}},gn=K(je),Y=e=>async(n,t,r)=>{const o=r?Object.assign(r,{async:!0}):{async:!0};let u=n._zod.run({value:t,issues:[]},o);return u instanceof Promise&&(u=await u),u.issues.length?{success:!1,error:new e(u.issues.map(i=>E(i,o,I())))}:{success:!0,data:u.value}},zn=Y(je),bn=e=>(n,t,r)=>{const o=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return oe(e)(n,t,o)},kn=e=>(n,t,r)=>oe(e)(n,t,r),wn=e=>async(n,t,r)=>{const o=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return ie(e)(n,t,o)},Zn=e=>async(n,t,r)=>ie(e)(n,t,r),$n=e=>(n,t,r)=>{const o=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return K(e)(n,t,o)},yn=e=>(n,t,r)=>K(e)(n,t,r),In=e=>async(n,t,r)=>{const o=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return Y(e)(n,t,o)},En=e=>async(n,t,r)=>Y(e)(n,t,r),Tn=/^[cC][^\s-]{8,}$/,Pn=/^[0-9a-z]+$/,On=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,An=/^[0-9a-vA-V]{20}$/,Nn=/^[A-Za-z0-9]{27}$/,Dn=/^[a-zA-Z0-9_-]{21}$/,Sn=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,jn=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,le=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,xn=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,Rn="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Cn(){return new RegExp(Rn,"u")}const Un=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Fn=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,Ln=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,Mn=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Vn=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,xe=/^[A-Za-z0-9_-]*$/,Bn=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,Wn=/^\+(?:[0-9]){6,14}[0-9]$/,Re="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",Gn=new RegExp(`^${Re}$`);function Ce(e){const n="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${n}`:e.precision===0?`${n}:[0-5]\\d`:`${n}:[0-5]\\d\\.\\d{${e.precision}}`:`${n}(?::[0-5]\\d(?:\\.\\d+)?)?`}function Jn(e){return new RegExp(`^${Ce(e)}$`)}function Kn(e){const n=Ce({precision:e.precision}),t=["Z"];e.local&&t.push(""),e.offset&&t.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");const r=`${n}(?:${t.join("|")})`;return new RegExp(`^${Re}T(?:${r})$`)}const Yn=e=>{const n=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${n}$`)},Xn=/^\d+$/,qn=/^-?\d+(?:\.\d+)?/i,Hn=/true|false/i,Qn=/^[^A-Z]*$/,et=/^[^a-z]*$/,k=c("$ZodCheck",(e,n)=>{var t;e._zod??(e._zod={}),e._zod.def=n,(t=e._zod).onattach??(t.onattach=[])}),Ue={number:"number",bigint:"bigint",object:"date"},Fe=c("$ZodCheckLessThan",(e,n)=>{k.init(e,n);const t=Ue[typeof n.value];e._zod.onattach.push(r=>{const o=r._zod.bag,u=(n.inclusive?o.maximum:o.exclusiveMaximum)??Number.POSITIVE_INFINITY;n.value<u&&(n.inclusive?o.maximum=n.value:o.exclusiveMaximum=n.value)}),e._zod.check=r=>{(n.inclusive?r.value<=n.value:r.value<n.value)||r.issues.push({origin:t,code:"too_big",maximum:n.value,input:r.value,inclusive:n.inclusive,inst:e,continue:!n.abort})}}),Le=c("$ZodCheckGreaterThan",(e,n)=>{k.init(e,n);const t=Ue[typeof n.value];e._zod.onattach.push(r=>{const o=r._zod.bag,u=(n.inclusive?o.minimum:o.exclusiveMinimum)??Number.NEGATIVE_INFINITY;n.value>u&&(n.inclusive?o.minimum=n.value:o.exclusiveMinimum=n.value)}),e._zod.check=r=>{(n.inclusive?r.value>=n.value:r.value>n.value)||r.issues.push({origin:t,code:"too_small",minimum:n.value,input:r.value,inclusive:n.inclusive,inst:e,continue:!n.abort})}}),nt=c("$ZodCheckMultipleOf",(e,n)=>{k.init(e,n),e._zod.onattach.push(t=>{var r;(r=t._zod.bag).multipleOf??(r.multipleOf=n.value)}),e._zod.check=t=>{if(typeof t.value!=typeof n.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof t.value=="bigint"?t.value%n.value===BigInt(0):tn(t.value,n.value)===0)||t.issues.push({origin:typeof t.value,code:"not_multiple_of",divisor:n.value,input:t.value,inst:e,continue:!n.abort})}}),tt=c("$ZodCheckNumberFormat",(e,n)=>{k.init(e,n),n.format=n.format||"float64";const t=n.format?.includes("int"),r=t?"int":"number",[o,u]=sn[n.format];e._zod.onattach.push(i=>{const s=i._zod.bag;s.format=n.format,s.minimum=o,s.maximum=u,t&&(s.pattern=Xn)}),e._zod.check=i=>{const s=i.value;if(t){if(!Number.isInteger(s)){i.issues.push({expected:r,format:n.format,code:"invalid_type",continue:!1,input:s,inst:e});return}if(!Number.isSafeInteger(s)){s>0?i.issues.push({input:s,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!n.abort}):i.issues.push({input:s,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!n.abort});return}}s<o&&i.issues.push({origin:"number",input:s,code:"too_small",minimum:o,inclusive:!0,inst:e,continue:!n.abort}),s>u&&i.issues.push({origin:"number",input:s,code:"too_big",maximum:u,inst:e})}}),rt=c("$ZodCheckMaxLength",(e,n)=>{var t;k.init(e,n),(t=e._zod.def).when??(t.when=r=>{const o=r.value;return!ne(o)&&o.length!==void 0}),e._zod.onattach.push(r=>{const o=r._zod.bag.maximum??Number.POSITIVE_INFINITY;n.maximum<o&&(r._zod.bag.maximum=n.maximum)}),e._zod.check=r=>{const o=r.value;if(o.length<=n.maximum)return;const i=re(o);r.issues.push({origin:i,code:"too_big",maximum:n.maximum,inclusive:!0,input:o,inst:e,continue:!n.abort})}}),ot=c("$ZodCheckMinLength",(e,n)=>{var t;k.init(e,n),(t=e._zod.def).when??(t.when=r=>{const o=r.value;return!ne(o)&&o.length!==void 0}),e._zod.onattach.push(r=>{const o=r._zod.bag.minimum??Number.NEGATIVE_INFINITY;n.minimum>o&&(r._zod.bag.minimum=n.minimum)}),e._zod.check=r=>{const o=r.value;if(o.length>=n.minimum)return;const i=re(o);r.issues.push({origin:i,code:"too_small",minimum:n.minimum,inclusive:!0,input:o,inst:e,continue:!n.abort})}}),it=c("$ZodCheckLengthEquals",(e,n)=>{var t;k.init(e,n),(t=e._zod.def).when??(t.when=r=>{const o=r.value;return!ne(o)&&o.length!==void 0}),e._zod.onattach.push(r=>{const o=r._zod.bag;o.minimum=n.length,o.maximum=n.length,o.length=n.length}),e._zod.check=r=>{const o=r.value,u=o.length;if(u===n.length)return;const i=re(o),s=u>n.length;r.issues.push({origin:i,...s?{code:"too_big",maximum:n.length}:{code:"too_small",minimum:n.length},inclusive:!0,exact:!0,input:r.value,inst:e,continue:!n.abort})}}),X=c("$ZodCheckStringFormat",(e,n)=>{var t,r;k.init(e,n),e._zod.onattach.push(o=>{const u=o._zod.bag;u.format=n.format,n.pattern&&(u.patterns??(u.patterns=new Set),u.patterns.add(n.pattern))}),n.pattern?(t=e._zod).check??(t.check=o=>{n.pattern.lastIndex=0,!n.pattern.test(o.value)&&o.issues.push({origin:"string",code:"invalid_format",format:n.format,input:o.value,...n.pattern?{pattern:n.pattern.toString()}:{},inst:e,continue:!n.abort})}):(r=e._zod).check??(r.check=()=>{})}),ut=c("$ZodCheckRegex",(e,n)=>{X.init(e,n),e._zod.check=t=>{n.pattern.lastIndex=0,!n.pattern.test(t.value)&&t.issues.push({origin:"string",code:"invalid_format",format:"regex",input:t.value,pattern:n.pattern.toString(),inst:e,continue:!n.abort})}}),ct=c("$ZodCheckLowerCase",(e,n)=>{n.pattern??(n.pattern=Qn),X.init(e,n)}),st=c("$ZodCheckUpperCase",(e,n)=>{n.pattern??(n.pattern=et),X.init(e,n)}),at=c("$ZodCheckIncludes",(e,n)=>{k.init(e,n);const t=J(n.includes),r=new RegExp(typeof n.position=="number"?`^.{${n.position}}${t}`:t);n.pattern=r,e._zod.onattach.push(o=>{const u=o._zod.bag;u.patterns??(u.patterns=new Set),u.patterns.add(r)}),e._zod.check=o=>{o.value.includes(n.includes,n.position)||o.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:n.includes,input:o.value,inst:e,continue:!n.abort})}}),lt=c("$ZodCheckStartsWith",(e,n)=>{k.init(e,n);const t=new RegExp(`^${J(n.prefix)}.*`);n.pattern??(n.pattern=t),e._zod.onattach.push(r=>{const o=r._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(t)}),e._zod.check=r=>{r.value.startsWith(n.prefix)||r.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:n.prefix,input:r.value,inst:e,continue:!n.abort})}}),ft=c("$ZodCheckEndsWith",(e,n)=>{k.init(e,n);const t=new RegExp(`.*${J(n.suffix)}$`);n.pattern??(n.pattern=t),e._zod.onattach.push(r=>{const o=r._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(t)}),e._zod.check=r=>{r.value.endsWith(n.suffix)||r.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:n.suffix,input:r.value,inst:e,continue:!n.abort})}}),ht=c("$ZodCheckOverwrite",(e,n)=>{k.init(e,n),e._zod.check=t=>{t.value=n.tx(t.value)}});class dt{constructor(n=[]){this.content=[],this.indent=0,this&&(this.args=n)}indented(n){this.indent+=1,n(this),this.indent-=1}write(n){if(typeof n=="function"){n(this,{execution:"sync"}),n(this,{execution:"async"});return}const r=n.split(`
`).filter(i=>i),o=Math.min(...r.map(i=>i.length-i.trimStart().length)),u=r.map(i=>i.slice(o)).map(i=>" ".repeat(this.indent*2)+i);for(const i of u)this.content.push(i)}compile(){const n=Function,t=this?.args,o=[...(this?.content??[""]).map(u=>`  ${u}`)];return new n(...t,o.join(`
`))}}const pt={major:4,minor:1,patch:1},m=c("$ZodType",(e,n)=>{var t;e??(e={}),e._zod.def=n,e._zod.bag=e._zod.bag||{},e._zod.version=pt;const r=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&r.unshift(e);for(const o of r)for(const u of o._zod.onattach)u(e);if(r.length===0)(t=e._zod).deferred??(t.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{const o=(i,s,a)=>{let l=O(i),h;for(const b of s){if(b._zod.def.when){if(!b._zod.def.when(i))continue}else if(l)continue;const z=i.issues.length,g=b._zod.check(i);if(g instanceof Promise&&a?.async===!1)throw new N;if(h||g instanceof Promise)h=(h??Promise.resolve()).then(async()=>{await g,i.issues.length!==z&&(l||(l=O(i,z)))});else{if(i.issues.length===z)continue;l||(l=O(i,z))}}return h?h.then(()=>i):i},u=(i,s,a)=>{if(O(i))return i.aborted=!0,i;const l=o(s,r,a);if(l instanceof Promise){if(a.async===!1)throw new N;return l.then(h=>e._zod.parse(h,a))}return e._zod.parse(l,a)};e._zod.run=(i,s)=>{if(s.skipChecks)return e._zod.parse(i,s);if(s.direction==="backward"){const l=e._zod.parse({value:i.value,issues:[]},{...s,skipChecks:!0});return l instanceof Promise?l.then(h=>u(h,i,s)):u(l,i,s)}const a=e._zod.parse(i,s);if(a instanceof Promise){if(s.async===!1)throw new N;return a.then(l=>o(l,r,s))}return o(a,r,s)}}e["~standard"]={validate:o=>{try{const u=gn(e,o);return u.success?{value:u.data}:{issues:u.error?.issues}}catch{return zn(e,o).then(i=>i.success?{value:i.data}:{issues:i.error?.issues})}},vendor:"zod",version:1}}),ue=c("$ZodString",(e,n)=>{m.init(e,n),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??Yn(e._zod.bag),e._zod.parse=(t,r)=>{if(n.coerce)try{t.value=String(t.value)}catch{}return typeof t.value=="string"||t.issues.push({expected:"string",code:"invalid_type",input:t.value,inst:e}),t}}),p=c("$ZodStringFormat",(e,n)=>{X.init(e,n),ue.init(e,n)}),mt=c("$ZodGUID",(e,n)=>{n.pattern??(n.pattern=jn),p.init(e,n)}),_t=c("$ZodUUID",(e,n)=>{if(n.version){const r={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[n.version];if(r===void 0)throw new Error(`Invalid UUID version: "${n.version}"`);n.pattern??(n.pattern=le(r))}else n.pattern??(n.pattern=le());p.init(e,n)}),vt=c("$ZodEmail",(e,n)=>{n.pattern??(n.pattern=xn),p.init(e,n)}),gt=c("$ZodURL",(e,n)=>{p.init(e,n),e._zod.check=t=>{try{const r=t.value.trim(),o=new URL(r);n.hostname&&(n.hostname.lastIndex=0,n.hostname.test(o.hostname)||t.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:Bn.source,input:t.value,inst:e,continue:!n.abort})),n.protocol&&(n.protocol.lastIndex=0,n.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||t.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:n.protocol.source,input:t.value,inst:e,continue:!n.abort})),n.normalize?t.value=o.href:t.value=r;return}catch{t.issues.push({code:"invalid_format",format:"url",input:t.value,inst:e,continue:!n.abort})}}}),zt=c("$ZodEmoji",(e,n)=>{n.pattern??(n.pattern=Cn()),p.init(e,n)}),bt=c("$ZodNanoID",(e,n)=>{n.pattern??(n.pattern=Dn),p.init(e,n)}),kt=c("$ZodCUID",(e,n)=>{n.pattern??(n.pattern=Tn),p.init(e,n)}),wt=c("$ZodCUID2",(e,n)=>{n.pattern??(n.pattern=Pn),p.init(e,n)}),Zt=c("$ZodULID",(e,n)=>{n.pattern??(n.pattern=On),p.init(e,n)}),$t=c("$ZodXID",(e,n)=>{n.pattern??(n.pattern=An),p.init(e,n)}),yt=c("$ZodKSUID",(e,n)=>{n.pattern??(n.pattern=Nn),p.init(e,n)}),It=c("$ZodISODateTime",(e,n)=>{n.pattern??(n.pattern=Kn(n)),p.init(e,n)}),Et=c("$ZodISODate",(e,n)=>{n.pattern??(n.pattern=Gn),p.init(e,n)}),Tt=c("$ZodISOTime",(e,n)=>{n.pattern??(n.pattern=Jn(n)),p.init(e,n)}),Pt=c("$ZodISODuration",(e,n)=>{n.pattern??(n.pattern=Sn),p.init(e,n)}),Ot=c("$ZodIPv4",(e,n)=>{n.pattern??(n.pattern=Un),p.init(e,n),e._zod.onattach.push(t=>{const r=t._zod.bag;r.format="ipv4"})}),At=c("$ZodIPv6",(e,n)=>{n.pattern??(n.pattern=Fn),p.init(e,n),e._zod.onattach.push(t=>{const r=t._zod.bag;r.format="ipv6"}),e._zod.check=t=>{try{new URL(`http://[${t.value}]`)}catch{t.issues.push({code:"invalid_format",format:"ipv6",input:t.value,inst:e,continue:!n.abort})}}}),Nt=c("$ZodCIDRv4",(e,n)=>{n.pattern??(n.pattern=Ln),p.init(e,n)}),Dt=c("$ZodCIDRv6",(e,n)=>{n.pattern??(n.pattern=Mn),p.init(e,n),e._zod.check=t=>{const[r,o]=t.value.split("/");try{if(!o)throw new Error;const u=Number(o);if(`${u}`!==o)throw new Error;if(u<0||u>128)throw new Error;new URL(`http://[${r}]`)}catch{t.issues.push({code:"invalid_format",format:"cidrv6",input:t.value,inst:e,continue:!n.abort})}}});function Me(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const St=c("$ZodBase64",(e,n)=>{n.pattern??(n.pattern=Vn),p.init(e,n),e._zod.onattach.push(t=>{t._zod.bag.contentEncoding="base64"}),e._zod.check=t=>{Me(t.value)||t.issues.push({code:"invalid_format",format:"base64",input:t.value,inst:e,continue:!n.abort})}});function jt(e){if(!xe.test(e))return!1;const n=e.replace(/[-_]/g,r=>r==="-"?"+":"/"),t=n.padEnd(Math.ceil(n.length/4)*4,"=");return Me(t)}const xt=c("$ZodBase64URL",(e,n)=>{n.pattern??(n.pattern=xe),p.init(e,n),e._zod.onattach.push(t=>{t._zod.bag.contentEncoding="base64url"}),e._zod.check=t=>{jt(t.value)||t.issues.push({code:"invalid_format",format:"base64url",input:t.value,inst:e,continue:!n.abort})}}),Rt=c("$ZodE164",(e,n)=>{n.pattern??(n.pattern=Wn),p.init(e,n)});function Ct(e,n=null){try{const t=e.split(".");if(t.length!==3)return!1;const[r]=t;if(!r)return!1;const o=JSON.parse(atob(r));return!("typ"in o&&o?.typ!=="JWT"||!o.alg||n&&(!("alg"in o)||o.alg!==n))}catch{return!1}}const Ut=c("$ZodJWT",(e,n)=>{p.init(e,n),e._zod.check=t=>{Ct(t.value,n.alg)||t.issues.push({code:"invalid_format",format:"jwt",input:t.value,inst:e,continue:!n.abort})}}),Ve=c("$ZodNumber",(e,n)=>{m.init(e,n),e._zod.pattern=e._zod.bag.pattern??qn,e._zod.parse=(t,r)=>{if(n.coerce)try{t.value=Number(t.value)}catch{}const o=t.value;if(typeof o=="number"&&!Number.isNaN(o)&&Number.isFinite(o))return t;const u=typeof o=="number"?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return t.issues.push({expected:"number",code:"invalid_type",input:o,inst:e,...u?{received:u}:{}}),t}}),Ft=c("$ZodNumber",(e,n)=>{tt.init(e,n),Ve.init(e,n)}),Lt=c("$ZodBoolean",(e,n)=>{m.init(e,n),e._zod.pattern=Hn,e._zod.parse=(t,r)=>{if(n.coerce)try{t.value=!!t.value}catch{}const o=t.value;return typeof o=="boolean"||t.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:e}),t}}),Mt=c("$ZodUnknown",(e,n)=>{m.init(e,n),e._zod.parse=t=>t}),Vt=c("$ZodNever",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)}),Bt=c("$ZodDate",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>{if(n.coerce)try{t.value=new Date(t.value)}catch{}const o=t.value,u=o instanceof Date;return u&&!Number.isNaN(o.getTime())||t.issues.push({expected:"date",code:"invalid_type",input:o,...u?{received:"Invalid Date"}:{},inst:e}),t}});function fe(e,n,t){e.issues.length&&n.issues.push(...A(t,e.issues)),n.value[t]=e.value}const Wt=c("$ZodArray",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>{const o=t.value;if(!Array.isArray(o))return t.issues.push({expected:"array",code:"invalid_type",input:o,inst:e}),t;t.value=Array(o.length);const u=[];for(let i=0;i<o.length;i++){const s=o[i],a=n.element._zod.run({value:s,issues:[]},r);a instanceof Promise?u.push(a.then(l=>fe(l,t,i))):fe(a,t,i)}return u.length?Promise.all(u).then(()=>t):t}});function W(e,n,t,r){e.issues.length&&n.issues.push(...A(t,e.issues)),e.value===void 0?t in r&&(n.value[t]=void 0):n.value[t]=e.value}function Be(e){const n=Object.keys(e.shape);for(const r of n)if(!e.shape[r]._zod.traits.has("$ZodType"))throw new Error(`Invalid element at key "${r}": expected a Zod schema`);const t=cn(e.shape);return{...e,keys:n,keySet:new Set(n),numKeys:n.length,optionalKeys:new Set(t)}}function We(e,n,t,r,o,u){const i=[],s=o.keySet,a=o.catchall._zod,l=a.def.type;for(const h of Object.keys(n)){if(s.has(h))continue;if(l==="never"){i.push(h);continue}const b=a.run({value:n[h],issues:[]},r);b instanceof Promise?e.push(b.then(z=>W(z,t,h,n))):W(b,t,h,n)}return i.length&&t.issues.push({code:"unrecognized_keys",keys:i,input:n,inst:u}),e.length?Promise.all(e).then(()=>t):t}const Gt=c("$ZodObject",(e,n)=>{m.init(e,n);const t=ee(()=>Be(n));d(e._zod,"propValues",()=>{const i=n.shape,s={};for(const a in i){const l=i[a]._zod;if(l.values){s[a]??(s[a]=new Set);for(const h of l.values)s[a].add(h)}}return s});const r=B,o=n.catchall;let u;e._zod.parse=(i,s)=>{u??(u=t.value);const a=i.value;if(!r(a))return i.issues.push({expected:"object",code:"invalid_type",input:a,inst:e}),i;i.value={};const l=[],h=u.shape;for(const b of u.keys){const g=h[b]._zod.run({value:a[b],issues:[]},s);g instanceof Promise?l.push(g.then(Z=>W(Z,i,b,a))):W(g,i,b,a)}return o?We(l,a,i,s,t.value,e):l.length?Promise.all(l).then(()=>i):i}}),Jt=c("$ZodObjectJIT",(e,n)=>{Gt.init(e,n);const t=e._zod.parse,r=ee(()=>Be(n)),o=z=>{const g=new dt(["shape","payload","ctx"]),Z=r.value,He=y=>{const $=ae(y);return`shape[${$}]._zod.run({ value: input[${$}], issues: [] }, ctx)`};g.write("const input = payload.value;");const ce=Object.create(null);let Qe=0;for(const y of Z.keys)ce[y]=`key_${Qe++}`;g.write("const newResult = {}");for(const y of Z.keys){const $=ce[y],j=ae(y);g.write(`const ${$} = ${He(y)};`),g.write(`
        if (${$}.issues.length) {
          payload.issues = payload.issues.concat(${$}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${j}, ...iss.path] : [${j}]
          })));
        }
        
        if (${$}.value === undefined) {
          if (${j} in input) {
            newResult[${j}] = undefined;
          }
        } else {
          newResult[${j}] = ${$}.value;
        }
      `)}g.write("payload.value = newResult;"),g.write("return payload;");const en=g.compile();return(y,$)=>en(z,y,$)};let u;const i=B,s=!Oe.jitless,l=s&&on.value,h=n.catchall;let b;e._zod.parse=(z,g)=>{b??(b=r.value);const Z=z.value;return i(Z)?s&&l&&g?.async===!1&&g.jitless!==!0?(u||(u=o(n.shape)),z=u(z,g),h?We([],Z,z,g,b,e):z):t(z,g):(z.issues.push({expected:"object",code:"invalid_type",input:Z,inst:e}),z)}});function he(e,n,t,r){for(const u of e)if(u.issues.length===0)return n.value=u.value,n;const o=e.filter(u=>!O(u));return o.length===1?(n.value=o[0].value,o[0]):(n.issues.push({code:"invalid_union",input:n.value,inst:t,errors:e.map(u=>u.issues.map(i=>E(i,r,I())))}),n)}const Kt=c("$ZodUnion",(e,n)=>{m.init(e,n),d(e._zod,"optin",()=>n.options.some(o=>o._zod.optin==="optional")?"optional":void 0),d(e._zod,"optout",()=>n.options.some(o=>o._zod.optout==="optional")?"optional":void 0),d(e._zod,"values",()=>{if(n.options.every(o=>o._zod.values))return new Set(n.options.flatMap(o=>Array.from(o._zod.values)))}),d(e._zod,"pattern",()=>{if(n.options.every(o=>o._zod.pattern)){const o=n.options.map(u=>u._zod.pattern);return new RegExp(`^(${o.map(u=>te(u.source)).join("|")})$`)}});const t=n.options.length===1,r=n.options[0]._zod.run;e._zod.parse=(o,u)=>{if(t)return r(o,u);let i=!1;const s=[];for(const a of n.options){const l=a._zod.run({value:o.value,issues:[]},u);if(l instanceof Promise)s.push(l),i=!0;else{if(l.issues.length===0)return l;s.push(l)}}return i?Promise.all(s).then(a=>he(a,o,e,u)):he(s,o,e,u)}}),Yt=c("$ZodIntersection",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>{const o=t.value,u=n.left._zod.run({value:o,issues:[]},r),i=n.right._zod.run({value:o,issues:[]},r);return u instanceof Promise||i instanceof Promise?Promise.all([u,i]).then(([a,l])=>de(t,a,l)):de(t,u,i)}});function H(e,n){if(e===n)return{valid:!0,data:e};if(e instanceof Date&&n instanceof Date&&+e==+n)return{valid:!0,data:e};if(D(e)&&D(n)){const t=Object.keys(n),r=Object.keys(e).filter(u=>t.indexOf(u)!==-1),o={...e,...n};for(const u of r){const i=H(e[u],n[u]);if(!i.valid)return{valid:!1,mergeErrorPath:[u,...i.mergeErrorPath]};o[u]=i.data}return{valid:!0,data:o}}if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return{valid:!1,mergeErrorPath:[]};const t=[];for(let r=0;r<e.length;r++){const o=e[r],u=n[r],i=H(o,u);if(!i.valid)return{valid:!1,mergeErrorPath:[r,...i.mergeErrorPath]};t.push(i.data)}return{valid:!0,data:t}}return{valid:!1,mergeErrorPath:[]}}function de(e,n,t){if(n.issues.length&&e.issues.push(...n.issues),t.issues.length&&e.issues.push(...t.issues),O(e))return e;const r=H(n.value,t.value);if(!r.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return e.value=r.data,e}const Xt=c("$ZodRecord",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>{const o=t.value;if(!D(o))return t.issues.push({expected:"record",code:"invalid_type",input:o,inst:e}),t;const u=[];if(n.keyType._zod.values){const i=n.keyType._zod.values;t.value={};for(const a of i)if(typeof a=="string"||typeof a=="number"||typeof a=="symbol"){const l=n.valueType._zod.run({value:o[a],issues:[]},r);l instanceof Promise?u.push(l.then(h=>{h.issues.length&&t.issues.push(...A(a,h.issues)),t.value[a]=h.value})):(l.issues.length&&t.issues.push(...A(a,l.issues)),t.value[a]=l.value)}let s;for(const a in o)i.has(a)||(s=s??[],s.push(a));s&&s.length>0&&t.issues.push({code:"unrecognized_keys",input:o,inst:e,keys:s})}else{t.value={};for(const i of Reflect.ownKeys(o)){if(i==="__proto__")continue;const s=n.keyType._zod.run({value:i,issues:[]},r);if(s instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(s.issues.length){t.issues.push({code:"invalid_key",origin:"record",issues:s.issues.map(l=>E(l,r,I())),input:i,path:[i],inst:e}),t.value[s.value]=s.value;continue}const a=n.valueType._zod.run({value:o[i],issues:[]},r);a instanceof Promise?u.push(a.then(l=>{l.issues.length&&t.issues.push(...A(i,l.issues)),t.value[s.value]=l.value})):(a.issues.length&&t.issues.push(...A(i,a.issues)),t.value[s.value]=a.value)}}return u.length?Promise.all(u).then(()=>t):t}}),qt=c("$ZodEnum",(e,n)=>{m.init(e,n);const t=nn(n.entries),r=new Set(t);e._zod.values=r,e._zod.pattern=new RegExp(`^(${t.filter(o=>un.has(typeof o)).map(o=>typeof o=="string"?J(o):o.toString()).join("|")})$`),e._zod.parse=(o,u)=>{const i=o.value;return r.has(i)||o.issues.push({code:"invalid_value",values:t,input:i,inst:e}),o}}),Ht=c("$ZodTransform",(e,n)=>{m.init(e,n),e._zod.parse=(t,r)=>{if(r.direction==="backward")throw new Pe(e.constructor.name);const o=n.transform(t.value,t);if(r.async)return(o instanceof Promise?o:Promise.resolve(o)).then(i=>(t.value=i,t));if(o instanceof Promise)throw new N;return t.value=o,t}});function pe(e,n){return e.issues.length&&n===void 0?{issues:[],value:void 0}:e}const Qt=c("$ZodOptional",(e,n)=>{m.init(e,n),e._zod.optin="optional",e._zod.optout="optional",d(e._zod,"values",()=>n.innerType._zod.values?new Set([...n.innerType._zod.values,void 0]):void 0),d(e._zod,"pattern",()=>{const t=n.innerType._zod.pattern;return t?new RegExp(`^(${te(t.source)})?$`):void 0}),e._zod.parse=(t,r)=>{if(n.innerType._zod.optin==="optional"){const o=n.innerType._zod.run(t,r);return o instanceof Promise?o.then(u=>pe(u,t.value)):pe(o,t.value)}return t.value===void 0?t:n.innerType._zod.run(t,r)}}),er=c("$ZodNullable",(e,n)=>{m.init(e,n),d(e._zod,"optin",()=>n.innerType._zod.optin),d(e._zod,"optout",()=>n.innerType._zod.optout),d(e._zod,"pattern",()=>{const t=n.innerType._zod.pattern;return t?new RegExp(`^(${te(t.source)}|null)$`):void 0}),d(e._zod,"values",()=>n.innerType._zod.values?new Set([...n.innerType._zod.values,null]):void 0),e._zod.parse=(t,r)=>t.value===null?t:n.innerType._zod.run(t,r)}),nr=c("$ZodDefault",(e,n)=>{m.init(e,n),e._zod.optin="optional",d(e._zod,"values",()=>n.innerType._zod.values),e._zod.parse=(t,r)=>{if(r.direction==="backward")return n.innerType._zod.run(t,r);if(t.value===void 0)return t.value=n.defaultValue,t;const o=n.innerType._zod.run(t,r);return o instanceof Promise?o.then(u=>me(u,n)):me(o,n)}});function me(e,n){return e.value===void 0&&(e.value=n.defaultValue),e}const tr=c("$ZodPrefault",(e,n)=>{m.init(e,n),e._zod.optin="optional",d(e._zod,"values",()=>n.innerType._zod.values),e._zod.parse=(t,r)=>(r.direction==="backward"||t.value===void 0&&(t.value=n.defaultValue),n.innerType._zod.run(t,r))}),rr=c("$ZodNonOptional",(e,n)=>{m.init(e,n),d(e._zod,"values",()=>{const t=n.innerType._zod.values;return t?new Set([...t].filter(r=>r!==void 0)):void 0}),e._zod.parse=(t,r)=>{const o=n.innerType._zod.run(t,r);return o instanceof Promise?o.then(u=>_e(u,e)):_e(o,e)}});function _e(e,n){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:n}),e}const or=c("$ZodCatch",(e,n)=>{m.init(e,n),d(e._zod,"optin",()=>n.innerType._zod.optin),d(e._zod,"optout",()=>n.innerType._zod.optout),d(e._zod,"values",()=>n.innerType._zod.values),e._zod.parse=(t,r)=>{if(r.direction==="backward")return n.innerType._zod.run(t,r);const o=n.innerType._zod.run(t,r);return o instanceof Promise?o.then(u=>(t.value=u.value,u.issues.length&&(t.value=n.catchValue({...t,error:{issues:u.issues.map(i=>E(i,r,I()))},input:t.value}),t.issues=[]),t)):(t.value=o.value,o.issues.length&&(t.value=n.catchValue({...t,error:{issues:o.issues.map(u=>E(u,r,I()))},input:t.value}),t.issues=[]),t)}}),ir=c("$ZodPipe",(e,n)=>{m.init(e,n),d(e._zod,"values",()=>n.in._zod.values),d(e._zod,"optin",()=>n.in._zod.optin),d(e._zod,"optout",()=>n.out._zod.optout),d(e._zod,"propValues",()=>n.in._zod.propValues),e._zod.parse=(t,r)=>{if(r.direction==="backward"){const u=n.out._zod.run(t,r);return u instanceof Promise?u.then(i=>U(i,n.in,r)):U(u,n.in,r)}const o=n.in._zod.run(t,r);return o instanceof Promise?o.then(u=>U(u,n.out,r)):U(o,n.out,r)}});function U(e,n,t){return e.issues.length?(e.aborted=!0,e):n._zod.run({value:e.value,issues:e.issues},t)}const ur=c("$ZodReadonly",(e,n)=>{m.init(e,n),d(e._zod,"propValues",()=>n.innerType._zod.propValues),d(e._zod,"values",()=>n.innerType._zod.values),d(e._zod,"optin",()=>n.innerType._zod.optin),d(e._zod,"optout",()=>n.innerType._zod.optout),e._zod.parse=(t,r)=>{if(r.direction==="backward")return n.innerType._zod.run(t,r);const o=n.innerType._zod.run(t,r);return o instanceof Promise?o.then(ve):ve(o)}});function ve(e){return e.value=Object.freeze(e.value),e}const cr=c("$ZodCustom",(e,n)=>{k.init(e,n),m.init(e,n),e._zod.parse=(t,r)=>t,e._zod.check=t=>{const r=t.value,o=n.fn(r);if(o instanceof Promise)return o.then(u=>ge(u,t,r,e));ge(o,t,r,e)}});function ge(e,n,t,r){if(!e){const o={code:"custom",input:t,inst:r,path:[...r._zod.def.path??[]],continue:!r._zod.def.abort};r._zod.def.params&&(o.params=r._zod.def.params),n.issues.push(x(o))}}class sr{constructor(){this._map=new Map,this._idmap=new Map}add(n,...t){const r=t[0];if(this._map.set(n,r),r&&typeof r=="object"&&"id"in r){if(this._idmap.has(r.id))throw new Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,n)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(n){const t=this._map.get(n);return t&&typeof t=="object"&&"id"in t&&this._idmap.delete(t.id),this._map.delete(n),this}get(n){const t=n._zod.parent;if(t){const r={...this.get(t)??{}};delete r.id;const o={...r,...this._map.get(n)};return Object.keys(o).length?o:void 0}return this._map.get(n)}has(n){return this._map.has(n)}}function ar(){return new sr}const F=ar();function lr(e,n){return new e({type:"string",...f(n)})}function fr(e,n){return new e({type:"string",format:"email",check:"string_format",abort:!1,...f(n)})}function ze(e,n){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...f(n)})}function hr(e,n){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...f(n)})}function dr(e,n){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...f(n)})}function pr(e,n){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...f(n)})}function mr(e,n){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...f(n)})}function _r(e,n){return new e({type:"string",format:"url",check:"string_format",abort:!1,...f(n)})}function vr(e,n){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...f(n)})}function gr(e,n){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...f(n)})}function zr(e,n){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...f(n)})}function br(e,n){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...f(n)})}function kr(e,n){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...f(n)})}function wr(e,n){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...f(n)})}function Zr(e,n){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...f(n)})}function $r(e,n){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...f(n)})}function yr(e,n){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...f(n)})}function Ir(e,n){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...f(n)})}function Er(e,n){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...f(n)})}function Tr(e,n){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...f(n)})}function Pr(e,n){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...f(n)})}function Or(e,n){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...f(n)})}function Ar(e,n){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...f(n)})}function Nr(e,n){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...f(n)})}function Dr(e,n){return new e({type:"string",format:"date",check:"string_format",...f(n)})}function Sr(e,n){return new e({type:"string",format:"time",check:"string_format",precision:null,...f(n)})}function jr(e,n){return new e({type:"string",format:"duration",check:"string_format",...f(n)})}function xr(e,n){return new e({type:"number",checks:[],...f(n)})}function Rr(e,n){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...f(n)})}function Cr(e,n){return new e({type:"boolean",...f(n)})}function Ur(e){return new e({type:"unknown"})}function Fr(e,n){return new e({type:"never",...f(n)})}function vi(e,n){return new e({type:"date",coerce:!0,...f(n)})}function be(e,n){return new Fe({check:"less_than",...f(n),value:e,inclusive:!1})}function M(e,n){return new Fe({check:"less_than",...f(n),value:e,inclusive:!0})}function ke(e,n){return new Le({check:"greater_than",...f(n),value:e,inclusive:!1})}function V(e,n){return new Le({check:"greater_than",...f(n),value:e,inclusive:!0})}function we(e,n){return new nt({check:"multiple_of",...f(n),value:e})}function Ge(e,n){return new rt({check:"max_length",...f(n),maximum:e})}function G(e,n){return new ot({check:"min_length",...f(n),minimum:e})}function Je(e,n){return new it({check:"length_equals",...f(n),length:e})}function Lr(e,n){return new ut({check:"string_format",format:"regex",...f(n),pattern:e})}function Mr(e){return new ct({check:"string_format",format:"lowercase",...f(e)})}function Vr(e){return new st({check:"string_format",format:"uppercase",...f(e)})}function Br(e,n){return new at({check:"string_format",format:"includes",...f(n),includes:e})}function Wr(e,n){return new lt({check:"string_format",format:"starts_with",...f(n),prefix:e})}function Gr(e,n){return new ft({check:"string_format",format:"ends_with",...f(n),suffix:e})}function R(e){return new ht({check:"overwrite",tx:e})}function Jr(e){return R(n=>n.normalize(e))}function Kr(){return R(e=>e.trim())}function Yr(){return R(e=>e.toLowerCase())}function Xr(){return R(e=>e.toUpperCase())}function qr(e,n,t){return new e({type:"array",element:n,...f(t)})}function Hr(e,n,t){return new e({type:"custom",check:"custom",fn:n,...f(t)})}function Qr(e){const n=eo(t=>(t.addIssue=r=>{if(typeof r=="string")t.issues.push(x(r,t.value,n._zod.def));else{const o=r;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=t.value),o.inst??(o.inst=n),o.continue??(o.continue=!n._zod.def.abort),t.issues.push(x(o))}},e(t.value,t)));return n}function eo(e,n){const t=new k({check:"custom",...f(n)});return t._zod.check=e,t}const no=c("ZodISODateTime",(e,n)=>{It.init(e,n),_.init(e,n)});function to(e){return Nr(no,e)}const ro=c("ZodISODate",(e,n)=>{Et.init(e,n),_.init(e,n)});function oo(e){return Dr(ro,e)}const io=c("ZodISOTime",(e,n)=>{Tt.init(e,n),_.init(e,n)});function uo(e){return Sr(io,e)}const co=c("ZodISODuration",(e,n)=>{Pt.init(e,n),_.init(e,n)});function so(e){return jr(co,e)}const ao=(e,n)=>{Se.init(e,n),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>vn(e,t)},flatten:{value:t=>_n(e,t)},addIssue:{value:t=>{e.issues.push(t),e.message=JSON.stringify(e.issues,q,2)}},addIssues:{value:t=>{e.issues.push(...t),e.message=JSON.stringify(e.issues,q,2)}},isEmpty:{get(){return e.issues.length===0}}})},w=c("ZodError",ao,{Parent:Error}),lo=oe(w),fo=ie(w),ho=K(w),po=Y(w),mo=bn(w),_o=kn(w),vo=wn(w),go=Zn(w),zo=$n(w),bo=yn(w),ko=In(w),wo=En(w),v=c("ZodType",(e,n)=>(m.init(e,n),e.def=n,e.type=n.type,Object.defineProperty(e,"_def",{value:n}),e.check=(...t)=>e.clone({...n,checks:[...n.checks??[],...t.map(r=>typeof r=="function"?{_zod:{check:r,def:{check:"custom"},onattach:[]}}:r)]}),e.clone=(t,r)=>P(e,t,r),e.brand=()=>e,e.register=(t,r)=>(t.add(e,r),e),e.parse=(t,r)=>lo(e,t,r,{callee:e.parse}),e.safeParse=(t,r)=>ho(e,t,r),e.parseAsync=async(t,r)=>fo(e,t,r,{callee:e.parseAsync}),e.safeParseAsync=async(t,r)=>po(e,t,r),e.spa=e.safeParseAsync,e.encode=(t,r)=>mo(e,t,r),e.decode=(t,r)=>_o(e,t,r),e.encodeAsync=async(t,r)=>vo(e,t,r),e.decodeAsync=async(t,r)=>go(e,t,r),e.safeEncode=(t,r)=>zo(e,t,r),e.safeDecode=(t,r)=>bo(e,t,r),e.safeEncodeAsync=async(t,r)=>ko(e,t,r),e.safeDecodeAsync=async(t,r)=>wo(e,t,r),e.refine=(t,r)=>e.check(mi(t,r)),e.superRefine=t=>e.check(_i(t)),e.overwrite=t=>e.check(R(t)),e.optional=()=>Ie(e),e.nullable=()=>Ee(e),e.nullish=()=>Ie(Ee(e)),e.nonoptional=t=>si(e,t),e.array=()=>Jo(e),e.or=t=>Xo([e,t]),e.and=t=>Ho(e,t),e.transform=t=>Te(e,ti(t)),e.default=t=>ii(e,t),e.prefault=t=>ci(e,t),e.catch=t=>li(e,t),e.pipe=t=>Te(e,t),e.readonly=()=>di(e),e.describe=t=>{const r=e.clone();return F.add(r,{description:t}),r},Object.defineProperty(e,"description",{get(){return F.get(e)?.description},configurable:!0}),e.meta=(...t)=>{if(t.length===0)return F.get(e);const r=e.clone();return F.add(r,t[0]),r},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),Ke=c("_ZodString",(e,n)=>{ue.init(e,n),v.init(e,n);const t=e._zod.bag;e.format=t.format??null,e.minLength=t.minimum??null,e.maxLength=t.maximum??null,e.regex=(...r)=>e.check(Lr(...r)),e.includes=(...r)=>e.check(Br(...r)),e.startsWith=(...r)=>e.check(Wr(...r)),e.endsWith=(...r)=>e.check(Gr(...r)),e.min=(...r)=>e.check(G(...r)),e.max=(...r)=>e.check(Ge(...r)),e.length=(...r)=>e.check(Je(...r)),e.nonempty=(...r)=>e.check(G(1,...r)),e.lowercase=r=>e.check(Mr(r)),e.uppercase=r=>e.check(Vr(r)),e.trim=()=>e.check(Kr()),e.normalize=(...r)=>e.check(Jr(...r)),e.toLowerCase=()=>e.check(Yr()),e.toUpperCase=()=>e.check(Xr())}),Zo=c("ZodString",(e,n)=>{ue.init(e,n),Ke.init(e,n),e.email=t=>e.check(fr($o,t)),e.url=t=>e.check(_r(yo,t)),e.jwt=t=>e.check(Ar(Fo,t)),e.emoji=t=>e.check(vr(Io,t)),e.guid=t=>e.check(ze(Ze,t)),e.uuid=t=>e.check(hr(L,t)),e.uuidv4=t=>e.check(dr(L,t)),e.uuidv6=t=>e.check(pr(L,t)),e.uuidv7=t=>e.check(mr(L,t)),e.nanoid=t=>e.check(gr(Eo,t)),e.guid=t=>e.check(ze(Ze,t)),e.cuid=t=>e.check(zr(To,t)),e.cuid2=t=>e.check(br(Po,t)),e.ulid=t=>e.check(kr(Oo,t)),e.base64=t=>e.check(Tr(Ro,t)),e.base64url=t=>e.check(Pr(Co,t)),e.xid=t=>e.check(wr(Ao,t)),e.ksuid=t=>e.check(Zr(No,t)),e.ipv4=t=>e.check($r(Do,t)),e.ipv6=t=>e.check(yr(So,t)),e.cidrv4=t=>e.check(Ir(jo,t)),e.cidrv6=t=>e.check(Er(xo,t)),e.e164=t=>e.check(Or(Uo,t)),e.datetime=t=>e.check(to(t)),e.date=t=>e.check(oo(t)),e.time=t=>e.check(uo(t)),e.duration=t=>e.check(so(t))});function gi(e){return lr(Zo,e)}const _=c("ZodStringFormat",(e,n)=>{p.init(e,n),Ke.init(e,n)}),$o=c("ZodEmail",(e,n)=>{vt.init(e,n),_.init(e,n)}),Ze=c("ZodGUID",(e,n)=>{mt.init(e,n),_.init(e,n)}),L=c("ZodUUID",(e,n)=>{_t.init(e,n),_.init(e,n)}),yo=c("ZodURL",(e,n)=>{gt.init(e,n),_.init(e,n)}),Io=c("ZodEmoji",(e,n)=>{zt.init(e,n),_.init(e,n)}),Eo=c("ZodNanoID",(e,n)=>{bt.init(e,n),_.init(e,n)}),To=c("ZodCUID",(e,n)=>{kt.init(e,n),_.init(e,n)}),Po=c("ZodCUID2",(e,n)=>{wt.init(e,n),_.init(e,n)}),Oo=c("ZodULID",(e,n)=>{Zt.init(e,n),_.init(e,n)}),Ao=c("ZodXID",(e,n)=>{$t.init(e,n),_.init(e,n)}),No=c("ZodKSUID",(e,n)=>{yt.init(e,n),_.init(e,n)}),Do=c("ZodIPv4",(e,n)=>{Ot.init(e,n),_.init(e,n)}),So=c("ZodIPv6",(e,n)=>{At.init(e,n),_.init(e,n)}),jo=c("ZodCIDRv4",(e,n)=>{Nt.init(e,n),_.init(e,n)}),xo=c("ZodCIDRv6",(e,n)=>{Dt.init(e,n),_.init(e,n)}),Ro=c("ZodBase64",(e,n)=>{St.init(e,n),_.init(e,n)}),Co=c("ZodBase64URL",(e,n)=>{xt.init(e,n),_.init(e,n)}),Uo=c("ZodE164",(e,n)=>{Rt.init(e,n),_.init(e,n)}),Fo=c("ZodJWT",(e,n)=>{Ut.init(e,n),_.init(e,n)}),Ye=c("ZodNumber",(e,n)=>{Ve.init(e,n),v.init(e,n),e.gt=(r,o)=>e.check(ke(r,o)),e.gte=(r,o)=>e.check(V(r,o)),e.min=(r,o)=>e.check(V(r,o)),e.lt=(r,o)=>e.check(be(r,o)),e.lte=(r,o)=>e.check(M(r,o)),e.max=(r,o)=>e.check(M(r,o)),e.int=r=>e.check($e(r)),e.safe=r=>e.check($e(r)),e.positive=r=>e.check(ke(0,r)),e.nonnegative=r=>e.check(V(0,r)),e.negative=r=>e.check(be(0,r)),e.nonpositive=r=>e.check(M(0,r)),e.multipleOf=(r,o)=>e.check(we(r,o)),e.step=(r,o)=>e.check(we(r,o)),e.finite=()=>e;const t=e._zod.bag;e.minValue=Math.max(t.minimum??Number.NEGATIVE_INFINITY,t.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(t.maximum??Number.POSITIVE_INFINITY,t.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(t.format??"").includes("int")||Number.isSafeInteger(t.multipleOf??.5),e.isFinite=!0,e.format=t.format??null});function zi(e){return xr(Ye,e)}const Lo=c("ZodNumberFormat",(e,n)=>{Ft.init(e,n),Ye.init(e,n)});function $e(e){return Rr(Lo,e)}const Mo=c("ZodBoolean",(e,n)=>{Lt.init(e,n),v.init(e,n)});function bi(e){return Cr(Mo,e)}const Vo=c("ZodUnknown",(e,n)=>{Mt.init(e,n),v.init(e,n)});function ye(){return Ur(Vo)}const Bo=c("ZodNever",(e,n)=>{Vt.init(e,n),v.init(e,n)});function Wo(e){return Fr(Bo,e)}const ki=c("ZodDate",(e,n)=>{Bt.init(e,n),v.init(e,n),e.min=(r,o)=>e.check(V(r,o)),e.max=(r,o)=>e.check(M(r,o));const t=e._zod.bag;e.minDate=t.minimum?new Date(t.minimum):null,e.maxDate=t.maximum?new Date(t.maximum):null}),Go=c("ZodArray",(e,n)=>{Wt.init(e,n),v.init(e,n),e.element=n.element,e.min=(t,r)=>e.check(G(t,r)),e.nonempty=t=>e.check(G(1,t)),e.max=(t,r)=>e.check(Ge(t,r)),e.length=(t,r)=>e.check(Je(t,r)),e.unwrap=()=>e.element});function Jo(e,n){return qr(Go,e,n)}const Ko=c("ZodObject",(e,n)=>{Jt.init(e,n),v.init(e,n),d(e,"shape",()=>n.shape),e.keyof=()=>ei(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:ye()}),e.loose=()=>e.clone({...e._zod.def,catchall:ye()}),e.strict=()=>e.clone({...e._zod.def,catchall:Wo()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>fn(e,t),e.safeExtend=t=>hn(e,t),e.merge=t=>dn(e,t),e.pick=t=>an(e,t),e.omit=t=>ln(e,t),e.partial=(...t)=>pn(Xe,e,t[0]),e.required=(...t)=>mn(qe,e,t[0])});function wi(e,n){const t={type:"object",get shape(){return T(this,"shape",e?rn(e):{}),this.shape},...f(n)};return new Ko(t)}const Yo=c("ZodUnion",(e,n)=>{Kt.init(e,n),v.init(e,n),e.options=n.options});function Xo(e,n){return new Yo({type:"union",options:e,...f(n)})}const qo=c("ZodIntersection",(e,n)=>{Yt.init(e,n),v.init(e,n)});function Ho(e,n){return new qo({type:"intersection",left:e,right:n})}const Qo=c("ZodRecord",(e,n)=>{Xt.init(e,n),v.init(e,n),e.keyType=n.keyType,e.valueType=n.valueType});function Zi(e,n,t){return new Qo({type:"record",keyType:e,valueType:n,...f(t)})}const Q=c("ZodEnum",(e,n)=>{qt.init(e,n),v.init(e,n),e.enum=n.entries,e.options=Object.values(n.entries);const t=new Set(Object.keys(n.entries));e.extract=(r,o)=>{const u={};for(const i of r)if(t.has(i))u[i]=n.entries[i];else throw new Error(`Key ${i} not found in enum`);return new Q({...n,checks:[],...f(o),entries:u})},e.exclude=(r,o)=>{const u={...n.entries};for(const i of r)if(t.has(i))delete u[i];else throw new Error(`Key ${i} not found in enum`);return new Q({...n,checks:[],...f(o),entries:u})}});function ei(e,n){const t=Array.isArray(e)?Object.fromEntries(e.map(r=>[r,r])):e;return new Q({type:"enum",entries:t,...f(n)})}const ni=c("ZodTransform",(e,n)=>{Ht.init(e,n),v.init(e,n),e._zod.parse=(t,r)=>{if(r.direction==="backward")throw new Pe(e.constructor.name);t.addIssue=u=>{if(typeof u=="string")t.issues.push(x(u,t.value,n));else{const i=u;i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=t.value),i.inst??(i.inst=e),t.issues.push(x(i))}};const o=n.transform(t.value,t);return o instanceof Promise?o.then(u=>(t.value=u,t)):(t.value=o,t)}});function ti(e){return new ni({type:"transform",transform:e})}const Xe=c("ZodOptional",(e,n)=>{Qt.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType});function Ie(e){return new Xe({type:"optional",innerType:e})}const ri=c("ZodNullable",(e,n)=>{er.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType});function Ee(e){return new ri({type:"nullable",innerType:e})}const oi=c("ZodDefault",(e,n)=>{nr.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function ii(e,n){return new oi({type:"default",innerType:e,get defaultValue(){return typeof n=="function"?n():Ne(n)}})}const ui=c("ZodPrefault",(e,n)=>{tr.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType});function ci(e,n){return new ui({type:"prefault",innerType:e,get defaultValue(){return typeof n=="function"?n():Ne(n)}})}const qe=c("ZodNonOptional",(e,n)=>{rr.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType});function si(e,n){return new qe({type:"nonoptional",innerType:e,...f(n)})}const ai=c("ZodCatch",(e,n)=>{or.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function li(e,n){return new ai({type:"catch",innerType:e,catchValue:typeof n=="function"?n:()=>n})}const fi=c("ZodPipe",(e,n)=>{ir.init(e,n),v.init(e,n),e.in=n.in,e.out=n.out});function Te(e,n){return new fi({type:"pipe",in:e,out:n})}const hi=c("ZodReadonly",(e,n)=>{ur.init(e,n),v.init(e,n),e.unwrap=()=>e._zod.def.innerType});function di(e){return new hi({type:"readonly",innerType:e})}const pi=c("ZodCustom",(e,n)=>{cr.init(e,n),v.init(e,n)});function mi(e,n={}){return Hr(pi,e,n)}function _i(e){return Qr(e)}export{ki as Z,vi as _,ei as a,Jo as b,Xo as c,bi as d,zi as n,wi as o,Zi as r,gi as s,ye as u};
